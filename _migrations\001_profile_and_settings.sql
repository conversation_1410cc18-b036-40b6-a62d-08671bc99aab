-- ========================================
-- MIGRATION: Profile and Settings
-- Date: 2025-08-20
-- Description: Adiciona tabelas para perfil do usuário, configurações do assistente e pagamentos
-- ========================================

-- Adicionar campo is_admin na tabela users
ALTER TABLE `users` 
ADD COLUMN `is_admin` TINYINT(1) DEFAULT 0 COMMENT 'Indica se o usuário é administrador';

-- Criar índice para busca de administradores
CREATE INDEX idx_users_is_admin ON `users` (`is_admin`);

-- Tabela para configurações do assistente (humor IA, tamanho respostas)
CREATE TABLE IF NOT EXISTS `user_assistant_settings` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) UNSIGNED NOT NULL,
    `ai_humor` ENUM('formal', 'friendly', 'casual', 'professional') NOT NULL DEFAULT 'friendly' COMMENT 'Humor da IA',
    `response_size` ENUM('short', 'medium', 'long', 'detailed') NOT NULL DEFAULT 'medium' COMMENT 'Tamanho das respostas',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_settings` (`user_id`),
    KEY `idx_user_id` (`user_id`),
    CONSTRAINT `fk_user_assistant_settings_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

-- Tabela para configurações de pagamento/Stripe (admin)
CREATE TABLE IF NOT EXISTS `payment_settings` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `stripe_public_key` TEXT NULL COMMENT 'Chave pública do Stripe',
    `stripe_secret_key` TEXT NULL COMMENT 'Chave secreta do Stripe (criptografada)',
    `stripe_webhook_secret` TEXT NULL COMMENT 'Segredo do webhook do Stripe',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT 'Se as configurações estão ativas',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

-- Inserir configuração padrão para assistente para usuários existentes
INSERT INTO `user_assistant_settings` (`user_id`, `ai_humor`, `response_size`)
SELECT `id`, 'friendly', 'medium' FROM `users` WHERE `id` NOT IN (
    SELECT `user_id` FROM `user_assistant_settings`
);
