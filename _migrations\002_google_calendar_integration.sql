-- ========================================
-- MIGRATION: Google Calendar Integration
-- Date: 2025-08-20
-- Description: Adiciona tabela para integração com Google Calendar
-- ========================================

-- Tabela para integração com Google Calendar
CREATE TABLE IF NOT EXISTS `google_calendar_integration` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) UNSIGNED NOT NULL,
    `calendar_email` VARCHAR(255) NOT NULL COMMENT 'Email para onde enviar convites do Google Calendar',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT 'Se a integração está ativa',
    `sync_tasks` TINYINT(1) DEFAULT 1 COMMENT 'Se deve sincronizar tarefas como eventos',
    `sync_appointments` TINYINT(1) DEFAULT 1 COMMENT 'Se deve sincronizar compromissos como eventos',
    `default_reminder_minutes` INT DEFAULT 15 COMMENT 'Lembrete padrão em minutos',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_calendar` (`user_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_calendar_email` (`calendar_email`),
    CONSTRAINT `fk_google_calendar_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;
