-- ========================================
-- MIGRATION: Plans and Subscriptions
-- Date: 2025-08-20
-- Description: Adiciona sistema de planos e assinaturas
-- ========================================

-- Tabela de planos disponíveis
CREATE TABLE IF NOT EXISTS `plans` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL COMMENT 'Nome do plano',
    `slug` VARCHAR(100) NOT NULL COMMENT 'Slug único do plano',
    `description` TEXT NULL COMMENT 'Descrição do plano',
    `price` DECIMAL(10,2) NOT NULL COMMENT 'Preço do plano',
    `currency` VARCHAR(3) DEFAULT 'BRL' COMMENT 'Moeda do preço',
    `billing_period` ENUM('monthly', 'yearly') NOT NULL COMMENT 'Período de cobrança',
    `stripe_price_id` VARCHAR(255) NULL COMMENT 'ID do preço no Stripe',
    `features` JSON NULL COMMENT 'Features do plano em JSON',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT 'Se o plano está ativo',
    `sort_order` INT DEFAULT 0 COMMENT 'Ordem de exibição',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_slug` (`slug`),
    KEY `idx_active` (`is_active`),
    KEY `idx_billing_period` (`billing_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

-- Tabela de assinaturas dos usuários
CREATE TABLE IF NOT EXISTS `user_subscriptions` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) UNSIGNED NOT NULL,
    `plan_id` BIGINT(20) UNSIGNED NOT NULL,
    `stripe_subscription_id` VARCHAR(255) NULL COMMENT 'ID da assinatura no Stripe',
    `stripe_customer_id` VARCHAR(255) NULL COMMENT 'ID do cliente no Stripe',
    `status` ENUM('active', 'inactive', 'canceled', 'past_due', 'unpaid') NOT NULL DEFAULT 'active',
    `current_period_start` TIMESTAMP NULL COMMENT 'Início do período atual',
    `current_period_end` TIMESTAMP NULL COMMENT 'Fim do período atual',
    `canceled_at` TIMESTAMP NULL COMMENT 'Data do cancelamento',
    `ends_at` TIMESTAMP NULL COMMENT 'Data de término efetivo',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_active_subscription` (`user_id`, `status`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_status` (`status`),
    KEY `idx_stripe_subscription` (`stripe_subscription_id`),
    KEY `idx_stripe_customer` (`stripe_customer_id`),
    CONSTRAINT `fk_subscription_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_subscription_plan` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

-- Inserir planos padrão
INSERT INTO `plans` (`name`, `slug`, `description`, `price`, `billing_period`, `features`, `sort_order`) VALUES
('Plano Mensal', 'monthly', 'Plano com cobrança mensal - Acesso completo a todas as funcionalidades', 29.90, 'monthly', JSON_OBJECT(
    'whatsapp_integration', true,
    'google_calendar', true,
    'google_sheets', true,
    'unlimited_tasks', true,
    'unlimited_finances', true,
    'unlimited_ideas', true,
    'ai_assistant', true,
    'priority_support', true
), 1),
('Plano Anual', 'yearly', 'Plano com cobrança anual - Acesso completo com desconto', 299.00, 'yearly', JSON_OBJECT(
    'whatsapp_integration', true,
    'google_calendar', true,
    'google_sheets', true,
    'unlimited_tasks', true,
    'unlimited_finances', true,
    'unlimited_ideas', true,
    'ai_assistant', true,
    'priority_support', true,
    'discount_percentage', 16.72
), 2);
