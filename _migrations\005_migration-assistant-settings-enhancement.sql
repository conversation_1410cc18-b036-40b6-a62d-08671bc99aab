-- <PERSON><PERSON>r tabela user_assistant_settings se não existir
CREATE TABLE IF NOT EXISTS user_assistant_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ai_humor ENUM('formal', 'friendly', 'casual', 'professional') DEFAULT 'friendly',
    response_size ENUM('short', 'medium', 'long', 'detailed') DEFAULT 'medium',
    reminder_time TIME DEFAULT '09:00:00',
    reminder_interval VARCHAR(10) DEFAULT '30',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
);

-- Adicionar novos campos se a tabela já existir (verificação segura)
ALTER TABLE user_assistant_settings 
ADD COLUMN IF NOT EXISTS reminder_time TIME DEFAULT '09:00:00',
ADD COLUMN IF NOT EXISTS reminder_interval VARCHAR(10) DEFAULT '30';
