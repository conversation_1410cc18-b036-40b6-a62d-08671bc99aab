# Database Configuration for Remote MySQL
DB_HOST=your-remote-mysql-host.com
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database_name

# Database Pool Configuration (optimized for remote connections)
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_IDLE_TIMEOUT=300000
DB_MAX_IDLE=5
DB_TIMEZONE=+00:00

# MySQL Logging Configuration
# Set to 'true' to enable verbose MySQL2 connection logs (for debugging)
# Set to 'false' or omit to suppress verbose logs (recommended for development)
MYSQL_VERBOSE_LOGS=false

# SSL Configuration (recommended for remote connections)
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=true

# Application Configuration
NODE_ENV=development
JWT_SECRET=your-jwt-secret-here
JWT_REFRESH_SECRET=your-jwt-refresh-secret-here

# Server Configuration
PORT=3001
