# Melhorias para Conexão MySQL Remoto

## Problema Resolvido

O erro `read ECONNRESET` é comum ao usar bancos MySQL remotos e geralmente está relacionado a:
- Timeouts de conexão
- Perda de conexão de rede
- Limitações do pool de conexões
- Falta de keep-alive em conexões remotas

## Soluções Implementadas

### 1. **Pool de Conexões Otimizado** (`database.provider.ts`)

Configurações específicas para MySQL remoto:

```typescript
// Pool configuration optimized for remote MySQL
connectionLimit: 10,           // Limite menor para evitar sobrecarregar servidor remoto
acquireTimeout: 60000,        // 60s para adquirir conexão
timeout: 60000,              // 60s timeout para queries

// Keep-alive settings for remote connections
reconnect: true,             // Reconexão automática
enableKeepAlive: true,       // Keep-alive TCP
keepAliveInitialDelay: 300000, // 5 minutos

// Idle connection management
idleTimeout: 300000,         // 5 minutos timeout para conexões idle
maxIdle: 5,                  // Máximo de 5 conexões idle

// SSL support for remote connections
ssl: {
  rejectUnauthorized: true   // Configurable via env
}
```

### 2. **Health Check Service** (`database-health.service.ts`)

Sistema de monitoramento automático:

```typescript
// Health check a cada 2 minutos
@Cron(CronExpression.EVERY_2_MINUTES)
async scheduledHealthCheck()

// Retry logic com exponential backoff
async executeWithRetry<T>(operation, maxRetries = 3, retryDelay = 1000)
```

### 3. **Error Interceptor Global** (`database-error.interceptor.ts`)

Intercepta e trata erros de conexão automaticamente:
- Identifica erros `ECONNRESET`
- Retorna mensagens user-friendly
- Log detalhado para debug

### 4. **Health Check Endpoint** (`/health`)

Endpoints para monitoramento:
- `GET /health` - Status geral da aplicação
- `GET /health/database` - Status específico do banco

## Configuração

### 1. Variáveis de Ambiente

Copie o arquivo `.env.example` e configure:

```bash
# Database Configuration
DB_HOST=your-remote-mysql-host.com
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database_name

# Pool Configuration (otimizado para MySQL remoto)
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_IDLE_TIMEOUT=300000
DB_MAX_IDLE=5

# SSL Configuration
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=true
```

### 2. Instalação de Dependências

```bash
npm install @nestjs/schedule
```

## Como Usar

### 1. **Usar Retry Logic em Services**

```typescript
// Em qualquer service, inject o DatabaseHealthService
constructor(
  private readonly databaseHealthService: DatabaseHealthService,
) {}

// Use o retry automático para operações críticas
async criticalOperation() {
  return this.databaseHealthService.executeWithRetry(
    async () => {
      // Sua operação de banco aqui
      return await this.db.selectFrom('table').execute();
    },
    3,     // máximo 3 tentativas
    1000   // delay inicial de 1s
  );
}
```

### 2. **Monitoramento**

```bash
# Verificar status do banco
curl http://localhost:3001/health/database

# Status geral da aplicação
curl http://localhost:3001/health
```

## Benefícios

1. **Maior Estabilidade**: Reconexão automática em caso de perda
2. **Melhor Performance**: Pool otimizado para conexões remotas
3. **Monitoramento**: Health checks automáticos
4. **Error Handling**: Tratamento inteligente de erros
5. **Debugging**: Logs detalhados para investigação
6. **User Experience**: Mensagens de erro mais amigáveis

## Logs de Debug

Com `NODE_ENV=development`, você verá logs detalhados:

```
[DatabaseProvider] Connecting to MySQL at remote-host:3306/database
[DatabaseProvider] Pool configuration: connectionLimit=10, acquireTimeout=60000ms
[DatabaseProvider] New database connection established as id 123
[DatabaseHealthService] Database health check successful (45ms)
```

## Troubleshooting

### Se ainda ocorrerem erros ECONNRESET:

1. **Aumentar timeouts**:
   ```
   DB_ACQUIRE_TIMEOUT=120000  # 2 minutos
   DB_TIMEOUT=120000          # 2 minutos
   ```

2. **Reduzir pool size**:
   ```
   DB_CONNECTION_LIMIT=5
   DB_MAX_IDLE=2
   ```

3. **Verificar SSL**:
   ```
   DB_SSL=false  # Temporariamente para debug
   ```

4. **Check logs** em `/health/database` para mais informações

## Monitoramento em Produção

Recomendações:
- Configure alertas baseados no endpoint `/health`
- Monitor logs para `ECONNRESET` patterns
- Use ferramentas como New Relic/DataDog para métricas de conexão
- Configure load balancer health checks no endpoint `/health`
