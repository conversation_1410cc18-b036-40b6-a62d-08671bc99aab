# MySQL2 Logging Configuration

This document explains how the MySQL2 logging configuration works in this NestJS application and how to control the verbosity of database connection logs.

## Problem

By default, MySQL2 outputs very verbose connection logs during startup, including:
- Raw packet data traces
- ClientHandshake debug messages  
- Server hello packet capability flags details
- Connection handshake hex dumps
- Packet-level communication logs

These logs clutter the console output and make it difficult to read actual application logs during development.

## Solution

We've implemented a custom logging configuration that:
1. ✅ Suppresses verbose MySQL2 connection logs
2. ✅ Keeps essential database connection status messages
3. ✅ Preserves important error messages
4. ✅ Can be easily toggled for debugging

## Configuration

### Environment Variable

Control MySQL2 logging verbosity with the `MYSQL_VERBOSE_LOGS` environment variable:

```bash
# Suppress verbose logs (default, recommended for development)
MYSQL_VERBOSE_LOGS=false

# Enable verbose logs (for debugging connection issues)
MYSQL_VERBOSE_LOGS=true
```

### Database Connection Settings

The following MySQL2 options are configured to reduce verbosity:

```typescript
{
  debug: false,        // Disable debug mode
  trace: false,        // Disable packet tracing
  charset: 'utf8mb4',  // Explicit charset to avoid warnings
  supportBigNumbers: true,
  bigNumberStrings: true,
}
```

## What Gets Suppressed

When `MYSQL_VERBOSE_LOGS=false` (default), the following log patterns are suppressed:

- **Raw packet data**: `raw: 0a31312e382e322d4d617269614442...`
- **Trace information**: Stack traces from mysql2 internal files
- **ClientHandshake messages**: `ClientHandshake#unknown name(0,,94)`
- **Server hello packets**: `Server hello packet: capability flags:**********=...`
- **Handshake packets**: `Sending handshake packet: flags:12252111=...`
- **Hex dumps**: Long hexadecimal strings
- **Add command messages**: `Add command: ClientHandshake`
- **Packet direction indicators**: `1 1087 <== ClientHandshake#unknown name...`

## What Gets Preserved

Important messages are always shown:

### ✅ Connection Status
```
[DatabaseProvider] Connecting to MySQL at *************:3306/dupli
[DatabaseProvider] Pool configuration: connectionLimit=10, acquireTimeout=60000ms
[MySQL2ConnectionProvider] MySQL2 direct connection established successfully
[DatabaseProvider] Database connection test successful
```

### ✅ Important Errors
- Connection refused
- Access denied
- Unknown database
- Table doesn't exist
- Syntax errors
- Duplicate entry
- Foreign key constraints

### ✅ Application Logs
All your application logs remain unchanged and visible.

## Usage

### For Development (Default)
No configuration needed. Verbose logs are suppressed by default.

### For Debugging Connection Issues
Set the environment variable to enable verbose logs:

```bash
# In .env file
MYSQL_VERBOSE_LOGS=true

# Or when starting the application
MYSQL_VERBOSE_LOGS=true npm run start:dev
```

### Programmatic Control
You can also control logging programmatically:

```typescript
import { MySQLLoggerConfig } from './database/mysql-logger.config';

// Suppress verbose logs
MySQLLoggerConfig.configure();

// Restore original logging
MySQLLoggerConfig.restore();
```

## Files Modified

1. **`src/database/mysql-logger.config.ts`** - Custom logger configuration
2. **`src/database/database.provider.ts`** - Updated database providers
3. **`src/database.types.ts`** - Updated standalone Kysely instance
4. **`src/main.ts`** - Early logger configuration
5. **`.env.example`** - Added MYSQL_VERBOSE_LOGS documentation

## Benefits

- ✅ **Cleaner console output** during development
- ✅ **Faster startup** (less console I/O)
- ✅ **Better debugging experience** (focus on application logs)
- ✅ **Configurable** (can be enabled when needed)
- ✅ **Preserves important information** (errors and connection status)

## Troubleshooting

If you need to see the full MySQL2 logs for debugging:

1. Set `MYSQL_VERBOSE_LOGS=true` in your `.env` file
2. Restart the application
3. The full verbose logs will be displayed
4. Set back to `false` when debugging is complete

## Example Output

### Before (Verbose)
```
raw: 0a31312e382e322d4d6172696144422d75627532343034003f040000576576407c703c7a00feff2d0200ff81150000000000003d000000714c64326d4d793b57255046006d7973716c5f6e61746976655f70617373776f726400
Trace
    at PoolConnection.handlePacket (C:\...\mysql2\lib\base\connection.js:429:17)
    at PacketParser.onPacket (C:\...\mysql2\lib\base\connection.js:93:12)
1 undefined ==> ClientHandshake#unknown name(0,,94)
Server hello packet: capability flags:**********=(found rows, long flag, connect with db...)
Sending handshake packet: flags:12252111=(long password, found rows, long flag...)
1 1087 <== ClientHandshake#unknown name(1,,141)
```

### After (Clean)
```
[DatabaseProvider] Connecting to MySQL at *************:3306/dupli
[DatabaseProvider] Pool configuration: connectionLimit=10, acquireTimeout=60000ms
[MySQL2ConnectionProvider] MySQL2 direct connection established successfully
[DatabaseProvider] Database connection test successful
🚀 Backend rodando na porta 3000
📡 CORS habilitado para: http://localhost:5173
```
