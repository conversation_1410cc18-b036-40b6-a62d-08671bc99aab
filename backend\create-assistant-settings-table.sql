-- <PERSON><PERSON><PERSON> para criar tabela user_assistant_settings com todos os campos necessários
CREATE TABLE IF NOT EXISTS user_assistant_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ai_humor ENUM('formal', 'friendly', 'casual', 'professional') DEFAULT 'friendly',
    response_size ENUM('short', 'medium', 'long', 'detailed') DEFAULT 'medium',
    reminder_time TIME DEFAULT '09:00:00',
    reminder_interval VARCHAR(10) DEFAULT '30',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
);
