import { AdminService } from './admin.service';
import { AdminUserListResponseDto, AdminDashboardStatsDto, UpdatePlanDto, UpdatePaymentSettingsDto, UpdateUserDto } from './dto/admin.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getDashboardStats(): Promise<AdminDashboardStatsDto>;
    getUsers(page?: number, limit?: number): Promise<{
        users: AdminUserListResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    updateUser(userId: number, updateDto: UpdateUserDto): Promise<SuccessResponseDto>;
    getPlans(): Promise<any[]>;
    updatePlan(planId: number, updateDto: UpdatePlanDto): Promise<SuccessResponseDto>;
    getPaymentSettings(): Promise<{
        id: any;
        stripe_public_key: any;
        stripe_secret_key: string | null;
        stripe_webhook_secret: string | null;
        is_active: boolean;
        created_at: any;
        updated_at: any;
    }>;
    updatePaymentSettings(updateDto: UpdatePaymentSettingsDto): Promise<SuccessResponseDto>;
    getStripeConfig(): Promise<{
        public_key: any;
        secret_key: string | null;
        webhook_endpoint_secret: string | null;
        test_mode: boolean;
    }>;
    saveStripeConfig(config: {
        public_key: string;
        secret_key: string;
        webhook_endpoint_secret?: string;
        test_mode: boolean;
    }): Promise<SuccessResponseDto>;
    testStripeConnection(config: {
        secret_key: string;
        test_mode: boolean;
    }): Promise<{
        success: true;
        message: string;
    }>;
}
