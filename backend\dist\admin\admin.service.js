"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const database_provider_1 = require("../database/database.provider");
let AdminService = class AdminService {
    connection;
    constructor(connection) {
        this.connection = connection;
    }
    async getDashboardStats() {
        const [totalUsersResult] = await this.connection.execute('SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL');
        const totalUsers = totalUsersResult[0].count;
        const [activeSubsResult] = await this.connection.execute('SELECT COUNT(*) as count FROM user_subscriptions WHERE status = ?', ['active']);
        const activeSubscriptions = activeSubsResult[0].count;
        const [revenueResult] = await this.connection.execute(`SELECT SUM(p.price) as total
       FROM user_subscriptions us
       INNER JOIN plans p ON us.plan_id = p.id
       WHERE us.status = 'active'`);
        const totalRevenue = parseFloat(revenueResult[0]?.total || '0');
        const [monthlyRevenueResult] = await this.connection.execute(`SELECT SUM(p.price) as monthly
       FROM user_subscriptions us
       INNER JOIN plans p ON us.plan_id = p.id
       WHERE us.status = 'active' AND us.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)`);
        const monthlyRevenue = parseFloat(monthlyRevenueResult[0]?.monthly || '0');
        const [canceledSubsResult] = await this.connection.execute('SELECT COUNT(*) as count FROM user_subscriptions WHERE status = ?', ['canceled']);
        const cancelledSubscriptions = canceledSubsResult[0].count;
        return {
            total_users: totalUsers,
            active_subscriptions: activeSubscriptions,
            total_revenue: totalRevenue,
            monthly_revenue: monthlyRevenue,
            cancelled_subscriptions: cancelledSubscriptions,
        };
    }
    async getUsers(page = 1, limit = 20) {
        const offset = (page - 1) * limit;
        const [rows] = await this.connection.execute(`SELECT 
         u.id, u.name, u.email, u.phone, u.timezone, u.is_admin, u.created_at,
         us.id as subscription_id, p.name as plan_name, us.status as subscription_status,
         us.current_period_end
       FROM users u
       LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
       LEFT JOIN plans p ON us.plan_id = p.id
       WHERE u.deleted_at IS NULL
       ORDER BY u.created_at DESC
       LIMIT ? OFFSET ?`, [limit, offset]);
        const [countResult] = await this.connection.execute('SELECT COUNT(*) as total FROM users WHERE deleted_at IS NULL');
        const total = countResult[0].total;
        const users = rows.map(row => ({
            id: row.id,
            name: row.name,
            email: row.email,
            phone: row.phone,
            timezone: row.timezone,
            is_admin: row.is_admin === 1,
            created_at: row.created_at,
            subscription: row.subscription_id ? {
                id: row.subscription_id,
                plan_name: row.plan_name,
                status: row.subscription_status,
                current_period_end: row.current_period_end,
            } : null,
        }));
        return {
            users,
            total,
            page,
            limit,
        };
    }
    async updateUser(userId, updateDto) {
        const updateFields = [];
        const values = [];
        if (updateDto.name) {
            updateFields.push('name = ?');
            values.push(updateDto.name);
        }
        if (updateDto.email) {
            updateFields.push('email = ?');
            values.push(updateDto.email);
        }
        if (updateDto.phone) {
            updateFields.push('phone = ?');
            values.push(updateDto.phone);
        }
        if (typeof updateDto.is_admin === 'boolean') {
            updateFields.push('is_admin = ?');
            values.push(updateDto.is_admin ? 1 : 0);
        }
        if (updateFields.length === 0) {
            throw new common_1.BadRequestException('Nenhum campo para atualizar');
        }
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(userId);
        const [result] = await this.connection.execute(`UPDATE users SET ${updateFields.join(', ')} WHERE id = ? AND deleted_at IS NULL`, values);
        if (result.affectedRows === 0) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
    }
    async getPlans() {
        const [rows] = await this.connection.execute(`SELECT id, name, slug, description, price, currency, billing_period,
              stripe_price_id, features, is_active, sort_order, created_at
       FROM plans
       ORDER BY sort_order ASC, price ASC`);
        return rows.map(row => ({
            ...row,
            price: parseFloat(row.price),
            features: row.features ? (typeof row.features === 'string' ? JSON.parse(row.features) : row.features) : [],
            is_active: row.is_active === 1,
        }));
    }
    async updatePlan(planId, updateDto) {
        const updateFields = [];
        const values = [];
        if (updateDto.name) {
            updateFields.push('name = ?');
            values.push(updateDto.name);
        }
        if (updateDto.description) {
            updateFields.push('description = ?');
            values.push(updateDto.description);
        }
        if (updateDto.price) {
            updateFields.push('price = ?');
            values.push(updateDto.price);
        }
        if (updateDto.stripe_price_id) {
            updateFields.push('stripe_price_id = ?');
            values.push(updateDto.stripe_price_id);
        }
        if (updateDto.features) {
            updateFields.push('features = ?');
            values.push(JSON.stringify(updateDto.features));
        }
        if (typeof updateDto.is_active === 'boolean') {
            updateFields.push('is_active = ?');
            values.push(updateDto.is_active ? 1 : 0);
        }
        if (updateDto.sort_order) {
            updateFields.push('sort_order = ?');
            values.push(updateDto.sort_order);
        }
        if (updateFields.length === 0) {
            throw new common_1.BadRequestException('Nenhum campo para atualizar');
        }
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(planId);
        const [result] = await this.connection.execute(`UPDATE plans SET ${updateFields.join(', ')} WHERE id = ?`, values);
        if (result.affectedRows === 0) {
            throw new common_1.NotFoundException('Plano não encontrado');
        }
    }
    async getPaymentSettings() {
        try {
            const [rows] = await this.connection.execute('SELECT * FROM payment_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1');
            if (!Array.isArray(rows) || rows.length === 0) {
                return {
                    id: null,
                    stripe_public_key: null,
                    stripe_secret_key: null,
                    stripe_webhook_secret: null,
                    is_active: false,
                    created_at: null,
                    updated_at: null,
                };
            }
            const settings = rows[0];
            return {
                id: settings.id,
                stripe_public_key: settings.stripe_public_key,
                stripe_secret_key: settings.stripe_secret_key ? '***' : null,
                stripe_webhook_secret: settings.stripe_webhook_secret ? '***' : null,
                is_active: settings.is_active === 1,
                created_at: settings.created_at,
                updated_at: settings.updated_at,
            };
        }
        catch (error) {
            return {
                id: null,
                stripe_public_key: null,
                stripe_secret_key: null,
                stripe_webhook_secret: null,
                is_active: false,
                created_at: null,
                updated_at: null,
            };
        }
    }
    async updatePaymentSettings(updateDto) {
        await this.connection.execute('UPDATE payment_settings SET is_active = 0');
        const [result] = await this.connection.execute(`INSERT INTO payment_settings 
       (stripe_public_key, stripe_secret_key, stripe_webhook_secret, is_active)
       VALUES (?, ?, ?, ?)`, [
            updateDto.stripe_public_key || null,
            updateDto.stripe_secret_key || null,
            updateDto.stripe_webhook_secret || null,
            updateDto.is_active !== false ? 1 : 0,
        ]);
    }
    async getStripeConfig() {
        try {
            const [rows] = await this.connection.execute('SELECT * FROM payment_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1');
            if (!Array.isArray(rows) || rows.length === 0) {
                return {
                    public_key: null,
                    secret_key: null,
                    webhook_endpoint_secret: null,
                    test_mode: true,
                };
            }
            const settings = rows[0];
            return {
                public_key: settings.stripe_public_key,
                secret_key: settings.stripe_secret_key ? '***' : null,
                webhook_endpoint_secret: settings.stripe_webhook_secret ? '***' : null,
                test_mode: true,
            };
        }
        catch (error) {
            return {
                public_key: null,
                secret_key: null,
                webhook_endpoint_secret: null,
                test_mode: true,
            };
        }
    }
    async saveStripeConfig(config) {
        const publicKeyPrefix = config.test_mode ? 'pk_test_' : 'pk_live_';
        const secretKeyPrefix = config.test_mode ? 'sk_test_' : 'sk_live_';
        if (!config.public_key.startsWith(publicKeyPrefix)) {
            throw new common_1.BadRequestException(`Chave pública deve começar com '${publicKeyPrefix}'`);
        }
        if (!config.secret_key.startsWith(secretKeyPrefix)) {
            throw new common_1.BadRequestException(`Chave secreta deve começar com '${secretKeyPrefix}'`);
        }
        await this.connection.execute('UPDATE payment_settings SET is_active = 0');
        await this.connection.execute(`INSERT INTO payment_settings 
       (stripe_public_key, stripe_secret_key, stripe_webhook_secret, is_active)
       VALUES (?, ?, ?, ?)`, [
            config.public_key,
            config.secret_key,
            config.webhook_endpoint_secret || null,
            1,
        ]);
    }
    async testStripeConnection(config) {
        try {
            const secretKeyPrefix = config.test_mode ? 'sk_test_' : 'sk_live_';
            if (!config.secret_key.startsWith(secretKeyPrefix)) {
                throw new common_1.BadRequestException(`Chave secreta deve começar com '${secretKeyPrefix}' para o modo ${config.test_mode ? 'teste' : 'produção'}`);
            }
            return {
                message: `Conexão com Stripe testada com sucesso (modo ${config.test_mode ? 'teste' : 'produção'})!`
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('Erro ao conectar com Stripe: ' + error.message);
        }
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.MYSQL2_CONNECTION)),
    __metadata("design:paramtypes", [Object])
], AdminService);
//# sourceMappingURL=admin.service.js.map