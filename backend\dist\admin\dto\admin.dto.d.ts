export declare class AdminUserListResponseDto {
    id: number;
    name: string;
    email: string;
    phone?: string;
    timezone: string;
    is_admin: boolean;
    created_at: Date;
    subscription?: {
        id: number;
        plan_name: string;
        status: string;
        current_period_end?: Date;
    } | null;
}
export declare class AdminDashboardStatsDto {
    total_users: number;
    active_subscriptions: number;
    total_revenue: number;
    monthly_revenue: number;
    cancelled_subscriptions: number;
}
export declare class UpdatePlanDto {
    name?: string;
    description?: string;
    price?: string;
    stripe_price_id?: string;
    features?: any;
    is_active?: boolean;
    sort_order?: number;
}
export declare class UpdatePaymentSettingsDto {
    stripe_public_key?: string;
    stripe_secret_key?: string;
    stripe_webhook_secret?: string;
    is_active?: boolean;
}
export declare class UpdateUserDto {
    name?: string;
    email?: string;
    phone?: string;
    is_admin?: boolean;
}
