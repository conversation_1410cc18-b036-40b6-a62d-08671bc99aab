import { AgentWppService } from './agentwpp.service';
import { CheckIntegrationResponseDto, AgentWppDashboardResponseDto, CreateTaskAgentWppDto, UpdateTaskAgentWppDto, CreateTaskCategoryAgentWppDto, CreateFinanceAgentWppDto, UpdateFinanceAgentWppDto, CreateFinanceCategoryAgentWppDto, CreateIdeaAgentWppDto, UpdateIdeaAgentWppDto, CreateIdeaCategoryAgentWppDto } from './dto/agentwpp.dto';
import { ValidateWhatsAppIntegrationDto } from '../integrations/dto/whatsapp-integration.dto';
export declare class AgentWppController {
    private readonly agentWppService;
    private readonly logger;
    constructor(agentWppService: AgentWppService);
    checkIntegration(phone: string): Promise<CheckIntegrationResponseDto>;
    validateNumber(validateDto: ValidateWhatsAppIntegrationDto): Promise<{
        success: boolean;
        message: string;
        phone: string;
        user_id: number;
    }>;
    getDashboard(phone: string): Promise<AgentWppDashboardResponseDto>;
    createTask(createTaskDto: CreateTaskAgentWppDto): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    findAllTasks(phone: string, page?: number, limit?: number): Promise<import("../tasks/dto/task-response.dto").TaskListResponseDto>;
    findAllTaskCategories(phone: string): Promise<import("../tasks/dto/task-category-response.dto").TaskCategoryResponseDto[]>;
    getRecentTasks(phone: string, limit?: number, days?: number): Promise<{
        recent_tasks: {
            id: number;
            type: "appointment" | "task";
            name: string;
            description: string | null | undefined;
            task_date: Date | null | undefined;
            completed: boolean;
            category_name: string | null;
        }[];
        total_in_period: number;
        period_days: number;
    }>;
    createQuickTask(createTaskDto: {
        phone: string;
        name: string;
        description?: string;
    }): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    findOneTask(id: number, phone: string): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    updateTask(id: number, updateTaskDto: UpdateTaskAgentWppDto): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    removeTask(id: number, phone: string): Promise<void>;
    completeTask(id: number, phone: string): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    createTaskCategory(createCategoryDto: CreateTaskCategoryAgentWppDto): Promise<import("../tasks/dto/task-category-response.dto").TaskCategoryResponseDto>;
    createFinance(createFinanceDto: CreateFinanceAgentWppDto): Promise<import("../finances/dto/finance-response.dto").FinanceResponseDto>;
    findAllFinances(phone: string, page?: number, limit?: number): Promise<import("../common/dto/common-response.dto").PaginatedFinancialResponseDto<import("../finances/dto/finance-response.dto").FinanceResponseDto>>;
    findAllFinanceCategories(phone: string): Promise<import("../finances/dto/finance-category-response.dto").FinanceCategoryResponseDto[]>;
    getFinanceSummary(phone: string, startDate?: string, endDate?: string): Promise<import("../finances/dto/finance-response.dto").FinanceSummaryDto>;
    getRecentFinances(phone: string, limit?: number, days?: number): Promise<{
        recent_transactions: {
            id: number;
            type: "income" | "expense";
            amount: string;
            description: string | null | undefined;
            category_name: string | null;
            date: Date;
        }[];
        total_in_period: number;
        period_days: number;
    }>;
    findOneFinance(id: number, phone: string): Promise<import("../finances/dto/finance-response.dto").FinanceResponseDto>;
    updateFinance(id: number, updateFinanceDto: UpdateFinanceAgentWppDto): Promise<import("../finances/dto/finance-response.dto").FinanceResponseDto>;
    removeFinance(id: number, phone: string): Promise<void>;
    createFinanceCategory(createCategoryDto: CreateFinanceCategoryAgentWppDto): Promise<import("../finances/dto/finance-category-response.dto").FinanceCategoryResponseDto>;
    createIdea(createIdeaDto: CreateIdeaAgentWppDto): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    findAllIdeas(phone: string, page?: number, limit?: number): Promise<import("../ideas/dto/idea-response.dto").IdeaListResponseDto>;
    findAllIdeaCategories(phone: string): Promise<import("../ideas/dto/idea-category-response.dto").IdeaCategoryResponseDto[]>;
    getRecentIdeas(phone: string, limit?: number, days?: number): Promise<{
        recent_ideas: {
            id: number;
            name: string;
            description: string | null | undefined;
            is_favorite: boolean | null | undefined;
            category_name: string | null;
        }[];
        total_in_period: number;
        period_days: number;
    }>;
    findOneIdea(id: number, phone: string): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    updateIdea(id: number, updateIdeaDto: UpdateIdeaAgentWppDto): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    removeIdea(id: number, phone: string): Promise<void>;
    toggleIdeaFavorite(id: number, phone: string): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    createIdeaCategory(createCategoryDto: CreateIdeaCategoryAgentWppDto): Promise<import("../ideas/dto/idea-category-response.dto").IdeaCategoryResponseDto>;
}
