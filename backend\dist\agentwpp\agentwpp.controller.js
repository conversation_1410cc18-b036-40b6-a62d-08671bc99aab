"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AgentWppController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentWppController = void 0;
const common_1 = require("@nestjs/common");
const agentwpp_service_1 = require("./agentwpp.service");
const api_key_guard_1 = require("./guards/api-key.guard");
const agentwpp_dto_1 = require("./dto/agentwpp.dto");
const whatsapp_integration_dto_1 = require("../integrations/dto/whatsapp-integration.dto");
const validation_error_interceptor_1 = require("../common/interceptors/validation-error.interceptor");
const phone_param_decorator_1 = require("./decorators/phone-param.decorator");
let AgentWppController = AgentWppController_1 = class AgentWppController {
    agentWppService;
    logger = new common_1.Logger(AgentWppController_1.name);
    constructor(agentWppService) {
        this.agentWppService = agentWppService;
    }
    async checkIntegration(phone) {
        this.logger.log(`[CHECK_INTEGRATION] Verificando integração para telefone: ${phone}`);
        const startTime = Date.now();
        try {
            const result = await this.agentWppService.checkIntegration(phone);
            const duration = Date.now() - startTime;
            this.logger.log(`[CHECK_INTEGRATION] Sucesso em ${duration}ms para telefone: ${phone}`);
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`[CHECK_INTEGRATION] Erro em ${duration}ms para telefone: ${phone}`, error);
            throw error;
        }
    }
    async validateNumber(validateDto) {
        this.logger.log(`[VALIDATE] Validando número com código de ativação: ${validateDto.activation_code}`);
        const startTime = Date.now();
        try {
            const result = await this.agentWppService.validateNumber(validateDto);
            const duration = Date.now() - startTime;
            this.logger.log(`[VALIDATE] Sucesso em ${duration}ms para telefone: ${validateDto.phone}`);
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`[VALIDATE] Erro em ${duration}ms para telefone: ${validateDto.phone}`, error);
            throw error;
        }
    }
    async getDashboard(phone) {
        this.logger.log(`[DASHBOARD] Buscando dashboard para telefone: ${phone}`);
        const startTime = Date.now();
        try {
            const result = await this.agentWppService.getDashboard(phone);
            const duration = Date.now() - startTime;
            this.logger.log(`[DASHBOARD] Sucesso em ${duration}ms para telefone: ${phone}`);
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`[DASHBOARD] Erro em ${duration}ms para telefone: ${phone}`, error);
            throw error;
        }
    }
    async createTask(createTaskDto) {
        this.logger.log(`Criando tarefa para telefone: ${createTaskDto.phone}`);
        return this.agentWppService.createTask(createTaskDto);
    }
    async findAllTasks(phone, page, limit) {
        this.logger.log(`Buscando tarefas para telefone: ${phone}`);
        return this.agentWppService.findAllTasks(phone, page, limit);
    }
    async findAllTaskCategories(phone) {
        this.logger.log(`Buscando categorias de tarefas para telefone: ${phone}`);
        return this.agentWppService.findAllTaskCategories(phone);
    }
    async getRecentTasks(phone, limit, days) {
        this.logger.log(`Buscando tarefas recentes para telefone: ${phone}`);
        return this.agentWppService.getRecentTasks(phone, limit, days);
    }
    async createQuickTask(createTaskDto) {
        this.logger.log(`Criando tarefa rápida para telefone: ${createTaskDto.phone}`);
        return this.agentWppService.createQuickTask(createTaskDto);
    }
    async findOneTask(id, phone) {
        this.logger.log(`Buscando tarefa ${id} para telefone: ${phone}`);
        return this.agentWppService.findOneTask(id, phone);
    }
    async updateTask(id, updateTaskDto) {
        this.logger.log(`Atualizando tarefa ${id} para telefone: ${updateTaskDto.phone}`);
        return this.agentWppService.updateTask(id, updateTaskDto);
    }
    async removeTask(id, phone) {
        this.logger.log(`Removendo tarefa ${id} para telefone: ${phone}`);
        return this.agentWppService.removeTask(id, phone);
    }
    async completeTask(id, phone) {
        this.logger.log(`Completando tarefa ${id} para telefone: ${phone}`);
        return this.agentWppService.completeTask(id, phone);
    }
    async createTaskCategory(createCategoryDto) {
        this.logger.log(`Criando categoria de tarefa para telefone: ${createCategoryDto.phone}`);
        return this.agentWppService.createTaskCategory(createCategoryDto);
    }
    async createFinance(createFinanceDto) {
        this.logger.log(`Criando registro financeiro para telefone: ${createFinanceDto.phone}`);
        return this.agentWppService.createFinance(createFinanceDto);
    }
    async findAllFinances(phone, page, limit) {
        this.logger.log(`Buscando registros financeiros para telefone: ${phone}`);
        return this.agentWppService.findAllFinances(phone, page, limit);
    }
    async findAllFinanceCategories(phone) {
        this.logger.log(`Buscando categorias financeiras para telefone: ${phone}`);
        return this.agentWppService.findAllFinanceCategories(phone);
    }
    async getFinanceSummary(phone, startDate, endDate) {
        this.logger.log(`Buscando resumo financeiro para telefone: ${phone}`);
        const startDateObj = startDate ? new Date(startDate) : undefined;
        const endDateObj = endDate ? new Date(endDate) : undefined;
        return this.agentWppService.getFinanceSummary(phone, startDateObj, endDateObj);
    }
    async getRecentFinances(phone, limit, days) {
        this.logger.log(`Buscando transações recentes para telefone: ${phone}`);
        return this.agentWppService.getRecentFinances(phone, limit, days);
    }
    async findOneFinance(id, phone) {
        this.logger.log(`Buscando registro financeiro ${id} para telefone: ${phone}`);
        return this.agentWppService.findOneFinance(id, phone);
    }
    async updateFinance(id, updateFinanceDto) {
        this.logger.log(`Atualizando registro financeiro ${id} para telefone: ${updateFinanceDto.phone}`);
        return this.agentWppService.updateFinance(id, updateFinanceDto);
    }
    async removeFinance(id, phone) {
        this.logger.log(`Removendo registro financeiro ${id} para telefone: ${phone}`);
        return this.agentWppService.removeFinance(id, phone);
    }
    async createFinanceCategory(createCategoryDto) {
        this.logger.log(`Criando categoria financeira para telefone: ${createCategoryDto.phone}`);
        return this.agentWppService.createFinanceCategory(createCategoryDto);
    }
    async createIdea(createIdeaDto) {
        this.logger.log(`Criando ideia para telefone: ${createIdeaDto.phone}`);
        return this.agentWppService.createIdea(createIdeaDto);
    }
    async findAllIdeas(phone, page, limit) {
        this.logger.log(`Buscando ideias para telefone: ${phone}`);
        return this.agentWppService.findAllIdeas(phone, page, limit);
    }
    async findAllIdeaCategories(phone) {
        this.logger.log(`Buscando categorias de ideias para telefone: ${phone}`);
        return this.agentWppService.findAllIdeaCategories(phone);
    }
    async getRecentIdeas(phone, limit, days) {
        this.logger.log(`Buscando ideias recentes para telefone: ${phone}`);
        return this.agentWppService.getRecentIdeas(phone, limit, days);
    }
    async findOneIdea(id, phone) {
        this.logger.log(`Buscando ideia ${id} para telefone: ${phone}`);
        return this.agentWppService.findOneIdea(id, phone);
    }
    async updateIdea(id, updateIdeaDto) {
        this.logger.log(`Atualizando ideia ${id} para telefone: ${updateIdeaDto.phone}`);
        return this.agentWppService.updateIdea(id, updateIdeaDto);
    }
    async removeIdea(id, phone) {
        this.logger.log(`Removendo ideia ${id} para telefone: ${phone}`);
        return this.agentWppService.removeIdea(id, phone);
    }
    async toggleIdeaFavorite(id, phone) {
        this.logger.log(`Alternando favorito da ideia ${id} para telefone: ${phone}`);
        return this.agentWppService.toggleIdeaFavorite(id, phone);
    }
    async createIdeaCategory(createCategoryDto) {
        this.logger.log(`Criando categoria de ideia para telefone: ${createCategoryDto.phone}`);
        return this.agentWppService.createIdeaCategory(createCategoryDto);
    }
};
exports.AgentWppController = AgentWppController;
__decorate([
    (0, common_1.Get)('check-integration/:phone'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "checkIntegration", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [whatsapp_integration_dto_1.ValidateWhatsAppIntegrationDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "validateNumber", null);
__decorate([
    (0, common_1.Get)('dashboard/:phone'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Post)('tasks'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agentwpp_dto_1.CreateTaskAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createTask", null);
__decorate([
    (0, common_1.Get)('tasks/:phone'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findAllTasks", null);
__decorate([
    (0, common_1.Get)('tasks/:phone/categories'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findAllTaskCategories", null);
__decorate([
    (0, common_1.Get)('tasks/:phone/recent'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "getRecentTasks", null);
__decorate([
    (0, common_1.Post)('tasks/quick'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createQuickTask", null);
__decorate([
    (0, common_1.Get)('tasks/:phone/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findOneTask", null);
__decorate([
    (0, common_1.Patch)('tasks/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, agentwpp_dto_1.UpdateTaskAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "updateTask", null);
__decorate([
    (0, common_1.Delete)('tasks/:phone/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "removeTask", null);
__decorate([
    (0, common_1.Patch)('tasks/:phone/:id/complete'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "completeTask", null);
__decorate([
    (0, common_1.Post)('tasks/categories'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agentwpp_dto_1.CreateTaskCategoryAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createTaskCategory", null);
__decorate([
    (0, common_1.Post)('finances'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agentwpp_dto_1.CreateFinanceAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createFinance", null);
__decorate([
    (0, common_1.Get)('finances/:phone'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findAllFinances", null);
__decorate([
    (0, common_1.Get)('finances/:phone/categories'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findAllFinanceCategories", null);
__decorate([
    (0, common_1.Get)('finances/:phone/summary'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "getFinanceSummary", null);
__decorate([
    (0, common_1.Get)('finances/:phone/recent'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "getRecentFinances", null);
__decorate([
    (0, common_1.Get)('finances/:phone/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findOneFinance", null);
__decorate([
    (0, common_1.Patch)('finances/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, agentwpp_dto_1.UpdateFinanceAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "updateFinance", null);
__decorate([
    (0, common_1.Delete)('finances/:phone/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "removeFinance", null);
__decorate([
    (0, common_1.Post)('finances/categories'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agentwpp_dto_1.CreateFinanceCategoryAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createFinanceCategory", null);
__decorate([
    (0, common_1.Post)('ideas'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agentwpp_dto_1.CreateIdeaAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createIdea", null);
__decorate([
    (0, common_1.Get)('ideas/:phone'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findAllIdeas", null);
__decorate([
    (0, common_1.Get)('ideas/:phone/categories'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findAllIdeaCategories", null);
__decorate([
    (0, common_1.Get)('ideas/:phone/recent'),
    __param(0, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "getRecentIdeas", null);
__decorate([
    (0, common_1.Get)('ideas/:phone/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "findOneIdea", null);
__decorate([
    (0, common_1.Patch)('ideas/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, agentwpp_dto_1.UpdateIdeaAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "updateIdea", null);
__decorate([
    (0, common_1.Delete)('ideas/:phone/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "removeIdea", null);
__decorate([
    (0, common_1.Patch)('ideas/:phone/:id/favorite'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, phone_param_decorator_1.PhoneParam)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "toggleIdeaFavorite", null);
__decorate([
    (0, common_1.Post)('ideas/categories'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [agentwpp_dto_1.CreateIdeaCategoryAgentWppDto]),
    __metadata("design:returntype", Promise)
], AgentWppController.prototype, "createIdeaCategory", null);
exports.AgentWppController = AgentWppController = AgentWppController_1 = __decorate([
    (0, common_1.Controller)('agentwpp'),
    (0, common_1.UseGuards)(api_key_guard_1.ApiKeyGuard),
    (0, common_1.UseInterceptors)(validation_error_interceptor_1.ValidationErrorInterceptor),
    __metadata("design:paramtypes", [agentwpp_service_1.AgentWppService])
], AgentWppController);
//# sourceMappingURL=agentwpp.controller.js.map