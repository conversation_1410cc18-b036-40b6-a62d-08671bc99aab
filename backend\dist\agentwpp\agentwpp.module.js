"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentWppModule = void 0;
const common_1 = require("@nestjs/common");
const agentwpp_controller_1 = require("./agentwpp.controller");
const agentwpp_service_1 = require("./agentwpp.service");
const tasks_module_1 = require("../tasks/tasks.module");
const finances_module_1 = require("../finances/finances.module");
const ideas_module_1 = require("../ideas/ideas.module");
const dashboard_module_1 = require("../dashboard/dashboard.module");
let AgentWppModule = class AgentWppModule {
};
exports.AgentWppModule = AgentWppModule;
exports.AgentWppModule = AgentWppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            tasks_module_1.TasksModule,
            finances_module_1.FinancesModule,
            ideas_module_1.IdeasModule,
            dashboard_module_1.DashboardModule,
        ],
        controllers: [agentwpp_controller_1.AgentWppController],
        providers: [agentwpp_service_1.AgentWppService],
        exports: [agentwpp_service_1.AgentWppService],
    })
], AgentWppModule);
//# sourceMappingURL=agentwpp.module.js.map