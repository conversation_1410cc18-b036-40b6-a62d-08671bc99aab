import { PaginatedFinancialResponseDto } from '../common/dto/common-response.dto';
import { TasksService } from '../tasks/tasks.service';
import { FinancesService } from '../finances/finances.service';
import { IdeasService } from '../ideas/ideas.service';
import { DashboardService } from '../dashboard/dashboard.service';
import { CheckIntegrationResponseDto, AgentWppDashboardResponseDto, CreateTaskAgentWppDto, UpdateTaskAgentWppDto, CreateTaskCategoryAgentWppDto, CreateFinanceAgentWppDto, UpdateFinanceAgentWppDto, CreateFinanceCategoryAgentWppDto, CreateIdeaAgentWppDto, UpdateIdeaAgentWppDto, CreateIdeaCategoryAgentWppDto } from './dto/agentwpp.dto';
export declare class AgentWppService {
    private tasksService;
    private financesService;
    private ideasService;
    private dashboardService;
    private readonly logger;
    private db;
    private readonly cache;
    private readonly CACHE_TTL;
    constructor(tasksService: TasksService, financesService: FinancesService, ideasService: IdeasService, dashboardService: DashboardService);
    private validateAndNormalizePhone;
    private getFromCache;
    private setCache;
    private getUserIdByPhone;
    private getUserTimezone;
    checkIntegration(phone: string): Promise<CheckIntegrationResponseDto>;
    getDashboard(phone: string): Promise<AgentWppDashboardResponseDto>;
    createTask(createTaskDto: CreateTaskAgentWppDto): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    findAllTasks(phone: string, page?: number, limit?: number): Promise<import("../tasks/dto/task-response.dto").TaskListResponseDto>;
    findOneTask(id: number, phone: string): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    updateTask(id: number, updateTaskDto: UpdateTaskAgentWppDto): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    removeTask(id: number, phone: string): Promise<void>;
    completeTask(id: number, phone: string): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    createTaskCategory(createCategoryDto: CreateTaskCategoryAgentWppDto): Promise<import("../tasks/dto/task-category-response.dto").TaskCategoryResponseDto>;
    findAllTaskCategories(phone: string): Promise<import("../tasks/dto/task-category-response.dto").TaskCategoryResponseDto[]>;
    createFinance(createFinanceDto: CreateFinanceAgentWppDto): Promise<import("../finances/dto/finance-response.dto").FinanceResponseDto>;
    findAllFinances(phone: string, page?: number, limit?: number): Promise<PaginatedFinancialResponseDto<import("../finances/dto/finance-response.dto").FinanceResponseDto>>;
    findOneFinance(id: number, phone: string): Promise<import("../finances/dto/finance-response.dto").FinanceResponseDto>;
    updateFinance(id: number, updateFinanceDto: UpdateFinanceAgentWppDto): Promise<import("../finances/dto/finance-response.dto").FinanceResponseDto>;
    removeFinance(id: number, phone: string): Promise<void>;
    getFinanceSummary(phone: string, startDate?: Date, endDate?: Date): Promise<import("../finances/dto/finance-response.dto").FinanceSummaryDto>;
    createFinanceCategory(createCategoryDto: CreateFinanceCategoryAgentWppDto): Promise<import("../finances/dto/finance-category-response.dto").FinanceCategoryResponseDto>;
    findAllFinanceCategories(phone: string): Promise<import("../finances/dto/finance-category-response.dto").FinanceCategoryResponseDto[]>;
    createIdea(createIdeaDto: CreateIdeaAgentWppDto): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    findAllIdeas(phone: string, page?: number, limit?: number): Promise<import("../ideas/dto/idea-response.dto").IdeaListResponseDto>;
    findOneIdea(id: number, phone: string): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    updateIdea(id: number, updateIdeaDto: UpdateIdeaAgentWppDto): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    removeIdea(id: number, phone: string): Promise<void>;
    toggleIdeaFavorite(id: number, phone: string): Promise<import("../ideas/dto/idea-response.dto").IdeaResponseDto>;
    createIdeaCategory(createCategoryDto: CreateIdeaCategoryAgentWppDto): Promise<import("../ideas/dto/idea-category-response.dto").IdeaCategoryResponseDto>;
    findAllIdeaCategories(phone: string): Promise<import("../ideas/dto/idea-category-response.dto").IdeaCategoryResponseDto[]>;
    getRecentFinances(phone: string, limit?: number, days?: number): Promise<{
        recent_transactions: {
            id: number;
            type: "income" | "expense";
            amount: string;
            description: string | null | undefined;
            category_name: string | null;
            date: Date;
        }[];
        total_in_period: number;
        period_days: number;
    }>;
    getRecentTasks(phone: string, limit?: number, days?: number): Promise<{
        recent_tasks: {
            id: number;
            type: "appointment" | "task";
            name: string;
            description: string | null | undefined;
            task_date: Date | null | undefined;
            completed: boolean;
            category_name: string | null;
        }[];
        total_in_period: number;
        period_days: number;
    }>;
    getRecentIdeas(phone: string, limit?: number, days?: number): Promise<{
        recent_ideas: {
            id: number;
            name: string;
            description: string | null | undefined;
            is_favorite: boolean | null | undefined;
            category_name: string | null;
        }[];
        total_in_period: number;
        period_days: number;
    }>;
    createQuickTask(createTaskDto: {
        phone: string;
        name: string;
        description?: string;
    }): Promise<import("../tasks/dto/task-response.dto").TaskResponseDto>;
    validateNumber(validateDto: {
        activation_code: string;
        phone: string;
    }): Promise<{
        success: boolean;
        message: string;
        phone: string;
        user_id: number;
    }>;
}
