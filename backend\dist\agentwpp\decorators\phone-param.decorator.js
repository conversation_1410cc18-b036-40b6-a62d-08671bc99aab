"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhoneParam = void 0;
const common_1 = require("@nestjs/common");
exports.PhoneParam = (0, common_1.createParamDecorator)((parameterName, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const param = request.params[parameterName];
    if (param === undefined) {
        throw new common_1.BadRequestException(`Parameter ${parameterName} is required`);
    }
    return String(param);
});
//# sourceMappingURL=phone-param.decorator.js.map