"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentWppDashboardResponseDto = exports.CheckIntegrationResponseDto = exports.CreateIdeaCategoryAgentWppDto = exports.UpdateIdeaAgentWppDto = exports.CreateIdeaAgentWppDto = exports.CreateFinanceCategoryAgentWppDto = exports.UpdateFinanceAgentWppDto = exports.CreateFinanceAgentWppDto = exports.CreateTaskCategoryAgentWppDto = exports.UpdateTaskAgentWppDto = exports.CreateTaskAgentWppDto = exports.AgentWppPaginationDto = exports.BaseAgentWppDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const common_response_dto_1 = require("../../common/dto/common-response.dto");
class BaseAgentWppDto {
    phone;
}
exports.BaseAgentWppDto = BaseAgentWppDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({
        message: 'Campo "phone" é obrigatório. Forneça um número de telefone válido para identificar o usuário.'
    }),
    (0, class_validator_1.IsString)({
        message: 'Campo "phone" deve ser uma string. Exemplo: "+5511999999999" ou "11999999999"'
    }),
    (0, class_validator_1.Matches)(/^\+?[\d\s\-\(\)]{10,20}$/, {
        message: 'Formato de telefone inválido. Use apenas números, espaços, hífens, parênteses e + (opcional). Exemplos válidos: "+5511999999999", "11999999999", "(11) 99999-9999"'
    }),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            const normalized = value.replace(/\s+/g, '').replace(/[^\d+]/g, '');
            return normalized;
        }
        return value;
    }),
    __metadata("design:type", String)
], BaseAgentWppDto.prototype, "phone", void 0);
class AgentWppPaginationDto extends common_response_dto_1.PaginationDto {
    search;
    startDate;
    endDate;
    categoryId;
}
exports.AgentWppPaginationDto = AgentWppPaginationDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AgentWppPaginationDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AgentWppPaginationDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AgentWppPaginationDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], AgentWppPaginationDto.prototype, "categoryId", void 0);
class CreateTaskAgentWppDto extends BaseAgentWppDto {
    task_type;
    category_id;
    name;
    description;
    task_date;
}
exports.CreateTaskAgentWppDto = CreateTaskAgentWppDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(['appointment', 'task']),
    __metadata("design:type", String)
], CreateTaskAgentWppDto.prototype, "task_type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateTaskAgentWppDto.prototype, "category_id", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskAgentWppDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskAgentWppDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateTaskAgentWppDto.prototype, "task_date", void 0);
class UpdateTaskAgentWppDto extends BaseAgentWppDto {
    task_type;
    category_id;
    name;
    description;
    task_date;
}
exports.UpdateTaskAgentWppDto = UpdateTaskAgentWppDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['appointment', 'task']),
    __metadata("design:type", String)
], UpdateTaskAgentWppDto.prototype, "task_type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateTaskAgentWppDto.prototype, "category_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateTaskAgentWppDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateTaskAgentWppDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateTaskAgentWppDto.prototype, "task_date", void 0);
class CreateTaskCategoryAgentWppDto extends BaseAgentWppDto {
    name;
}
exports.CreateTaskCategoryAgentWppDto = CreateTaskCategoryAgentWppDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskCategoryAgentWppDto.prototype, "name", void 0);
class CreateFinanceAgentWppDto extends BaseAgentWppDto {
    transaction_type;
    category_id;
    is_saving;
    description;
    amount;
    transaction_date;
}
exports.CreateFinanceAgentWppDto = CreateFinanceAgentWppDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(['income', 'expense']),
    __metadata("design:type", String)
], CreateFinanceAgentWppDto.prototype, "transaction_type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateFinanceAgentWppDto.prototype, "category_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateFinanceAgentWppDto.prototype, "is_saving", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinanceAgentWppDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '0,2' }),
    __metadata("design:type", String)
], CreateFinanceAgentWppDto.prototype, "amount", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateFinanceAgentWppDto.prototype, "transaction_date", void 0);
class UpdateFinanceAgentWppDto extends BaseAgentWppDto {
    transaction_type;
    category_id;
    is_saving;
    description;
    amount;
    transaction_date;
}
exports.UpdateFinanceAgentWppDto = UpdateFinanceAgentWppDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['income', 'expense']),
    __metadata("design:type", String)
], UpdateFinanceAgentWppDto.prototype, "transaction_type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateFinanceAgentWppDto.prototype, "category_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateFinanceAgentWppDto.prototype, "is_saving", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFinanceAgentWppDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsDecimal)({ decimal_digits: '0,2' }),
    __metadata("design:type", String)
], UpdateFinanceAgentWppDto.prototype, "amount", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateFinanceAgentWppDto.prototype, "transaction_date", void 0);
class CreateFinanceCategoryAgentWppDto extends BaseAgentWppDto {
    name;
    transaction_type;
    color;
}
exports.CreateFinanceCategoryAgentWppDto = CreateFinanceCategoryAgentWppDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinanceCategoryAgentWppDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(['income', 'expense']),
    __metadata("design:type", String)
], CreateFinanceCategoryAgentWppDto.prototype, "transaction_type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinanceCategoryAgentWppDto.prototype, "color", void 0);
class CreateIdeaAgentWppDto extends BaseAgentWppDto {
    category_id;
    name;
    description;
    content;
    is_favorite;
}
exports.CreateIdeaAgentWppDto = CreateIdeaAgentWppDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateIdeaAgentWppDto.prototype, "category_id", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateIdeaAgentWppDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateIdeaAgentWppDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateIdeaAgentWppDto.prototype, "content", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateIdeaAgentWppDto.prototype, "is_favorite", void 0);
class UpdateIdeaAgentWppDto extends BaseAgentWppDto {
    category_id;
    name;
    description;
    content;
    is_favorite;
}
exports.UpdateIdeaAgentWppDto = UpdateIdeaAgentWppDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateIdeaAgentWppDto.prototype, "category_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateIdeaAgentWppDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateIdeaAgentWppDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateIdeaAgentWppDto.prototype, "content", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateIdeaAgentWppDto.prototype, "is_favorite", void 0);
class CreateIdeaCategoryAgentWppDto extends BaseAgentWppDto {
    name;
}
exports.CreateIdeaCategoryAgentWppDto = CreateIdeaCategoryAgentWppDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateIdeaCategoryAgentWppDto.prototype, "name", void 0);
class CheckIntegrationResponseDto {
    hasIntegration;
    status;
    userId;
    timezone;
    assistantSettings;
}
exports.CheckIntegrationResponseDto = CheckIntegrationResponseDto;
class AgentWppDashboardResponseDto {
    user;
    tasks;
    finances;
    ideas;
}
exports.AgentWppDashboardResponseDto = AgentWppDashboardResponseDto;
//# sourceMappingURL=agentwpp.dto.js.map