import { CanActivate, ExecutionContext } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export declare class ApiKeyGuard implements CanActivate {
    private configService;
    private readonly logger;
    private readonly requestCounts;
    private readonly RATE_LIMIT_WINDOW;
    private readonly RATE_LIMIT_MAX_REQUESTS;
    constructor(configService: ConfigService);
    canActivate(context: ExecutionContext): boolean;
    private getClientIp;
    private checkRateLimit;
}
