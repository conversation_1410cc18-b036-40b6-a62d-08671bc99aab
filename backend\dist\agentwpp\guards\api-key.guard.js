"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiKeyGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyGuard = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let ApiKeyGuard = ApiKeyGuard_1 = class ApiKeyGuard {
    configService;
    logger = new common_1.Logger(ApiKeyGuard_1.name);
    requestCounts = new Map();
    RATE_LIMIT_WINDOW = 60 * 1000;
    RATE_LIMIT_MAX_REQUESTS = 100;
    constructor(configService) {
        this.configService = configService;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const apiKey = request.headers['x-api-key'];
        const validApiKey = this.configService.get('N8N_API_KEY');
        const clientIp = this.getClientIp(request);
        this.logger.debug(`[API_KEY] Tentativa de acesso de ${clientIp} para ${request.method} ${request.url}`);
        if (!apiKey) {
            this.logger.warn(`[API_KEY] Acesso negado - API Key não fornecida de ${clientIp}`);
            throw new common_1.UnauthorizedException('API Key é obrigatória');
        }
        if (apiKey !== validApiKey) {
            this.logger.warn(`[API_KEY] Acesso negado - API Key inválida de ${clientIp}`);
            throw new common_1.UnauthorizedException('API Key inválida');
        }
        if (!this.checkRateLimit(clientIp)) {
            this.logger.warn(`[API_KEY] Rate limit excedido para ${clientIp}`);
            throw new common_1.UnauthorizedException('Rate limit excedido. Tente novamente em alguns minutos.');
        }
        this.logger.debug(`[API_KEY] Acesso autorizado para ${clientIp}`);
        return true;
    }
    getClientIp(request) {
        return request.ip ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            request.connection?.socket?.remoteAddress ||
            'unknown';
    }
    checkRateLimit(clientIp) {
        const now = Date.now();
        const clientData = this.requestCounts.get(clientIp);
        if (!clientData || now > clientData.resetTime) {
            this.requestCounts.set(clientIp, {
                count: 1,
                resetTime: now + this.RATE_LIMIT_WINDOW
            });
            return true;
        }
        if (clientData.count >= this.RATE_LIMIT_MAX_REQUESTS) {
            return false;
        }
        clientData.count++;
        return true;
    }
};
exports.ApiKeyGuard = ApiKeyGuard;
exports.ApiKeyGuard = ApiKeyGuard = ApiKeyGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ApiKeyGuard);
//# sourceMappingURL=api-key.guard.js.map