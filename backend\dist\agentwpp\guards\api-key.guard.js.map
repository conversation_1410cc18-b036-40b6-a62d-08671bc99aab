{"version": 3, "file": "api-key.guard.js", "sourceRoot": "", "sources": ["../../../src/agentwpp/guards/api-key.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAA0G;AAC1G,2CAA+C;AAGxC,IAAM,WAAW,mBAAjB,MAAM,WAAW;IAMF;IALH,MAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IACtC,aAAa,GAAG,IAAI,GAAG,EAAgD,CAAC;IACxE,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC;IAC9B,uBAAuB,GAAG,GAAG,CAAC;IAE/C,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAEpD,WAAW,CAAC,OAAyB;QACnC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAG3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,SAAS,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAGxG,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,QAAQ,EAAE,CAAC,CAAC;YACnF,MAAM,IAAI,8BAAqB,CAAC,uBAAuB,CAAC,CAAC;QAC3D,CAAC;QAGD,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iDAAiD,QAAQ,EAAE,CAAC,CAAC;YAC9E,MAAM,IAAI,8BAAqB,CAAC,kBAAkB,CAAC,CAAC;QACtD,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;YACnE,MAAM,IAAI,8BAAqB,CAAC,yDAAyD,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,WAAW,CAAC,OAAY;QAC9B,OAAO,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,UAAU,EAAE,aAAa;YACjC,OAAO,CAAC,MAAM,EAAE,aAAa;YAC7B,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa;YACzC,SAAS,CAAC;IACnB,CAAC;IAEO,cAAc,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAGpD,IAAI,CAAC,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC/B,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB;aACxC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AApEY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAOwB,sBAAa;GANrC,WAAW,CAoEvB"}