"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoTransformValidationPipe = void 0;
const common_1 = require("@nestjs/common");
let NoTransformValidationPipe = class NoTransformValidationPipe extends common_1.ValidationPipe {
    constructor() {
        super({
            transform: false,
            whitelist: true,
            forbidNonWhitelisted: true,
        });
    }
    async transform(value, metadata) {
        console.log(`[NoTransformValidationPipe] Type: ${metadata.type}, Data: ${metadata.data}, Value: ${value} (${typeof value})`);
        if (metadata.type === 'param') {
            if (metadata.data === 'phone') {
                const phoneString = String(value);
                console.log(`[NoTransformValidationPipe] Phone param: ${value} -> ${phoneString}`);
                return phoneString;
            }
            return value;
        }
        try {
            return await super.transform(value, metadata);
        }
        catch (error) {
            console.error(`[NoTransformValidationPipe] Validation error:`, error);
            throw error;
        }
    }
};
exports.NoTransformValidationPipe = NoTransformValidationPipe;
exports.NoTransformValidationPipe = NoTransformValidationPipe = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], NoTransformValidationPipe);
//# sourceMappingURL=no-transform-validation.pipe.js.map