"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const app_controller_1 = require("./app.controller");
const database_module_1 = require("./database.module");
const auth_module_1 = require("./auth/auth.module");
const app_service_1 = require("./app.service");
const dashboard_module_1 = require("./dashboard/dashboard.module");
const tasks_module_1 = require("./tasks/tasks.module");
const finances_module_1 = require("./finances/finances.module");
const ideas_module_1 = require("./ideas/ideas.module");
const config_module_1 = require("./config/config.module");
const integrations_module_1 = require("./integrations/integrations.module");
const agentwpp_module_1 = require("./agentwpp/agentwpp.module");
const health_module_1 = require("./health/health.module");
const profile_module_1 = require("./profile/profile.module");
const plans_module_1 = require("./plans/plans.module");
const admin_module_1 = require("./admin/admin.module");
const google_calendar_module_1 = require("./google-calendar/google-calendar.module");
const database_error_interceptor_1 = require("./database/database-error.interceptor");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({ isGlobal: true }),
            database_module_1.DatabaseModule,
            auth_module_1.AuthModule,
            dashboard_module_1.DashboardModule,
            tasks_module_1.TasksModule,
            finances_module_1.FinancesModule,
            ideas_module_1.IdeasModule,
            config_module_1.ConfigModule,
            integrations_module_1.IntegrationsModule,
            agentwpp_module_1.AgentWppModule,
            health_module_1.HealthModule,
            profile_module_1.ProfileModule,
            plans_module_1.PlansModule,
            admin_module_1.AdminModule,
            google_calendar_module_1.GoogleCalendarModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: database_error_interceptor_1.DatabaseErrorInterceptor,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map