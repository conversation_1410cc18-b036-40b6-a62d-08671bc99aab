"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DevAwareAuthGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevAwareAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const passport_1 = require("@nestjs/passport");
let DevAwareAuthGuard = DevAwareAuthGuard_1 = class DevAwareAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
    reflector;
    logger = new common_1.Logger(DevAwareAuthGuard_1.name);
    constructor(reflector) {
        super();
        this.reflector = reflector;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const isPublic = this.reflector.getAllAndOverride('isPublic', [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        if (process.env.NODE_ENV === 'development' && process.env.DEV_MODE_ENABLED === 'true') {
            const devMode = request.headers['x-dev-mode'];
            const devUserId = request.headers['x-dev-user-id'];
            if (devMode === 'true' && devUserId) {
                request.user = {
                    userId: parseInt(devUserId),
                    email: '<EMAIL>'
                };
                return true;
            }
        }
        return super.canActivate(context);
    }
};
exports.DevAwareAuthGuard = DevAwareAuthGuard;
exports.DevAwareAuthGuard = DevAwareAuthGuard = DevAwareAuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], DevAwareAuthGuard);
//# sourceMappingURL=dev-auth.guard.js.map