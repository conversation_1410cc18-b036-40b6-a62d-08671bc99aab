{"version": 3, "file": "cors.interceptor.js", "sourceRoot": "", "sources": ["../../src/common/cors.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAKwB;AACxB,+BAAsC;AAG/B,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QAGtD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,QAAQ,CAAC,MAAM,CAAC,6BAA6B,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,uBAAuB,CAAC,CAAC;YACnG,QAAQ,CAAC,MAAM,CAAC,8BAA8B,EAAE,wCAAwC,CAAC,CAAC;YAC1F,QAAQ,CAAC,MAAM,CAAC,8BAA8B,EAAE,kGAAkG,CAAC,CAAC;YACpJ,QAAQ,CAAC,MAAM,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;YAC5D,QAAQ,CAAC,MAAM,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;YAClD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5B,OAAO,IAAA,SAAE,EAAC,IAAI,CAAC,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;CACF,CAAA;AAlBY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;GACA,eAAe,CAkB3B"}