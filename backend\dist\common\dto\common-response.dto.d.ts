export declare class PaginationDto {
    page?: number;
    limit?: number;
}
export declare class PaginatedResponseDto<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
    constructor(data: T[], total: number, page: number, limit: number);
}
export declare class SuccessResponseDto<T = any> {
    success: boolean;
    message?: string;
    data?: T;
    timestamp: string;
    constructor(data?: T, message?: string);
}
export declare class ErrorResponseDto {
    success: boolean;
    message: string;
    error?: string;
    timestamp: string;
    statusCode?: number;
    constructor(message: string, error?: string, statusCode?: number);
}
export declare class FinancialSummary {
    totalAmount: number;
    totalIncome: number;
    totalExpenses: number;
    balance: number;
    constructor(totalAmount?: number, totalIncome?: number, totalExpenses?: number);
}
export declare class PaginatedFinancialResponseDto<T> extends PaginatedResponseDto<T> {
    summary: FinancialSummary;
    constructor(data: T[], total: number, page: number, limit: number, summary: FinancialSummary);
}
