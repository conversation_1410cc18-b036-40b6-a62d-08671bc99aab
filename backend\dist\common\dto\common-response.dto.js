"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedFinancialResponseDto = exports.FinancialSummary = exports.ErrorResponseDto = exports.SuccessResponseDto = exports.PaginatedResponseDto = exports.PaginationDto = void 0;
const class_validator_1 = require("class-validator");
class PaginationDto {
    page = 1;
    limit = 50;
}
exports.PaginationDto = PaginationDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PaginationDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PaginationDto.prototype, "limit", void 0);
class PaginatedResponseDto {
    data;
    total;
    page;
    limit;
    totalPages;
    hasNext;
    hasPrevious;
    constructor(data, total, page, limit) {
        this.data = data;
        this.total = total;
        this.page = page;
        this.limit = limit;
        this.totalPages = Math.ceil(total / limit);
        this.hasNext = page < this.totalPages;
        this.hasPrevious = page > 1;
    }
}
exports.PaginatedResponseDto = PaginatedResponseDto;
class SuccessResponseDto {
    success = true;
    message;
    data;
    timestamp = new Date().toISOString();
    constructor(data, message) {
        this.data = data;
        this.message = message;
    }
}
exports.SuccessResponseDto = SuccessResponseDto;
class ErrorResponseDto {
    success = false;
    message;
    error;
    timestamp = new Date().toISOString();
    statusCode;
    constructor(message, error, statusCode) {
        this.message = message;
        this.error = error;
        this.statusCode = statusCode;
    }
}
exports.ErrorResponseDto = ErrorResponseDto;
class FinancialSummary {
    totalAmount;
    totalIncome;
    totalExpenses;
    balance;
    constructor(totalAmount = 0, totalIncome = 0, totalExpenses = 0) {
        this.totalAmount = totalAmount;
        this.totalIncome = totalIncome;
        this.totalExpenses = totalExpenses;
        this.balance = totalIncome - totalExpenses;
    }
}
exports.FinancialSummary = FinancialSummary;
class PaginatedFinancialResponseDto extends PaginatedResponseDto {
    summary;
    constructor(data, total, page, limit, summary) {
        super(data, total, page, limit);
        this.summary = summary;
    }
}
exports.PaginatedFinancialResponseDto = PaginatedFinancialResponseDto;
//# sourceMappingURL=common-response.dto.js.map