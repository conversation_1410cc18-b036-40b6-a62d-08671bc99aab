"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const common_response_dto_1 = require("../dto/common-response.dto");
let GlobalExceptionFilter = class GlobalExceptionFilter {
    logger = new common_1.Logger('GlobalExceptionFilter');
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let error = '';
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const errorResponse = exception.getResponse();
            if (typeof errorResponse === 'string') {
                message = errorResponse;
            }
            else if (typeof errorResponse === 'object' && errorResponse !== null) {
                const errorObj = errorResponse;
                message = errorObj.message || errorObj.error || message;
                error = errorObj.error || '';
                if (errorObj.message && Array.isArray(errorObj.message)) {
                    message = errorObj.message.join(', ');
                }
            }
        }
        else if (exception instanceof Error) {
            message = exception.message;
            error = exception.name;
            this.logger.error(`Unhandled exception: ${exception.message}`, exception.stack);
        }
        else {
            this.logger.error('Unknown exception type', exception);
        }
        const errorResponse = new common_response_dto_1.ErrorResponseDto(message, error, status);
        this.logger.error({
            message: 'Exception thrown',
            path: request.url,
            method: request.method,
            statusCode: status,
            error: message,
            stack: exception instanceof Error ? exception.stack : 'Unknown stack',
            timestamp: new Date().toISOString(),
        });
        response.status(status).json(errorResponse);
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = __decorate([
    (0, common_1.Catch)()
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map