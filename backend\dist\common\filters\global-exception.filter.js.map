{"version": 3, "file": "global-exception.filter.js", "sourceRoot": "", "sources": ["../../../src/common/filters/global-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAOwB;AAExB,oEAA8D;AAGvD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IACf,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAuB,CAAC,CAAC;IAE9D,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAE1C,IAAI,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC9C,IAAI,OAAO,GAAG,uBAAuB,CAAC;QACtC,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAE9C,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;gBACtC,OAAO,GAAG,aAAa,CAAC;YAC1B,CAAC;iBAAM,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBACvE,MAAM,QAAQ,GAAG,aAAoB,CAAC;gBACtC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC;gBACxD,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;gBAG7B,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxD,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE,CAAC;YACtC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC5B,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QAClF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,sCAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAGnE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAChB,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,UAAU,EAAE,MAAM;YAClB,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe;YACrE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;CACF,CAAA;AAnDY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,cAAK,GAAE;GACK,qBAAqB,CAmDjC"}