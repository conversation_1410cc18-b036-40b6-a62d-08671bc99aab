"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ValidationErrorInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationErrorInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
let ValidationErrorInterceptor = ValidationErrorInterceptor_1 = class ValidationErrorInterceptor {
    logger = new common_1.Logger(ValidationErrorInterceptor_1.name);
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const userAgent = request.headers['user-agent'] || '';
        const isN8NRequest = userAgent.toLowerCase().includes('n8n') ||
            request.headers['x-n8n'] ||
            request.url?.includes('/mcp');
        return next.handle().pipe((0, operators_1.catchError)((error) => {
            if (error instanceof common_1.BadRequestException && error.getResponse && typeof error.getResponse === 'object') {
                const response = error.getResponse();
                if (response.message && Array.isArray(response.message)) {
                    const improvedMessages = response.message.map((msg) => {
                        if (msg.includes('phone') && msg.includes('should not be empty')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "phone" é obrigatório. Para usar o AgentWPP, você deve fornecer um número de telefone válido. Exemplo: {"phone": "+5511999999999"}';
                        }
                        if (msg.includes('phone') && msg.includes('must be a string')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "phone" deve ser uma string. Forneça o telefone como texto. Exemplo: {"phone": "11999999999"}';
                        }
                        if (msg.includes('task_type') && msg.includes('should not be empty')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "task_type" é obrigatório. Valores aceitos: "task" ou "appointment". Exemplo: {"task_type": "task"}';
                        }
                        if (msg.includes('name') && msg.includes('should not be empty')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "name" é obrigatório. Forneça um nome/título. Exemplo: {"name": "Minha tarefa"}';
                        }
                        if (msg.includes('transaction_type') && msg.includes('should not be empty')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "transaction_type" é obrigatório. Valores aceitos: "income" ou "expense". Exemplo: {"transaction_type": "expense"}';
                        }
                        if (msg.includes('amount') && msg.includes('should not be empty')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "amount" é obrigatório. Forneça o valor como string. Exemplo: {"amount": "50.00"}';
                        }
                        if (msg.includes('activation_code') && msg.includes('should not be empty')) {
                            return 'ERRO DE VALIDAÇÃO: Campo "activation_code" é obrigatório. Forneça o código de ativação recebido. Exemplo: {"activation_code": "ABC123"}';
                        }
                        return msg;
                    });
                    if (isN8NRequest) {
                        this.logger.warn(`[N8N_VALIDATION_ERROR] Erro de validação em requisição do n8n/MCP:`);
                        this.logger.warn(`[N8N_VALIDATION_ERROR] URL: ${request.method} ${request.url}`);
                        this.logger.warn(`[N8N_VALIDATION_ERROR] Body: ${JSON.stringify(request.body)}`);
                        this.logger.warn(`[N8N_VALIDATION_ERROR] Erros: ${JSON.stringify(improvedMessages)}`);
                        this.logger.warn(`[N8N_VALIDATION_ERROR] Dica: Certifique-se de que todos os campos obrigatórios estão sendo enviados no formato correto.`);
                    }
                    const improvedError = new common_1.BadRequestException({
                        error: 'Validation failed',
                        message: improvedMessages,
                        statusCode: 400,
                        timestamp: new Date().toISOString(),
                        path: request.url,
                        help: 'Verifique se todos os campos obrigatórios estão sendo enviados corretamente. Para o AgentWPP, o campo "phone" é sempre necessário.'
                    });
                    return (0, rxjs_1.throwError)(() => improvedError);
                }
            }
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
};
exports.ValidationErrorInterceptor = ValidationErrorInterceptor;
exports.ValidationErrorInterceptor = ValidationErrorInterceptor = ValidationErrorInterceptor_1 = __decorate([
    (0, common_1.Injectable)()
], ValidationErrorInterceptor);
//# sourceMappingURL=validation-error.interceptor.js.map