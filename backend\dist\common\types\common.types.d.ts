export interface ApiResponse<T = any> {
    status: 'success' | 'error';
    data?: T;
    message?: string;
    errors?: string[];
}
export interface TimestampFields {
    created_at: Date;
    updated_at: Date;
}
export interface UserOwnedEntity extends TimestampFields {
    id: number;
    user_id: number;
}
export interface CategoryEntity extends UserOwnedEntity {
    name: string;
}
export type TransactionType = 'income' | 'expense';
export type TaskType = 'appointment' | 'task';
export type IntegrationStatus = 'pending' | 'active' | 'inactive';
