"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseUtils = void 0;
class DatabaseUtils {
    static async executeWithTransaction(db, callback) {
        return await db.transaction().execute(callback);
    }
    static buildPaginationQuery(query, page = 1, limit = 50) {
        const offset = (page - 1) * limit;
        return query.limit(limit).offset(offset);
    }
}
exports.DatabaseUtils = DatabaseUtils;
//# sourceMappingURL=database.utils.js.map