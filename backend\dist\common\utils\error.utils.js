"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorUtils = void 0;
const common_1 = require("@nestjs/common");
class ErrorUtils {
    static handleServiceError(logger, error, operation, userId) {
        const userInfo = userId ? ` para usuário ${userId}` : '';
        logger.error(`Erro ao ${operation}${userInfo}:`, error);
        if (error instanceof common_1.HttpException) {
            throw error;
        }
        if (error.code === 'ER_DUP_ENTRY') {
            throw new common_1.HttpException('Registro duplicado', common_1.HttpStatus.CONFLICT);
        }
        if (error.code === 'ER_NO_REFERENCED_ROW_2') {
            throw new common_1.HttpException('Referência inválida', common_1.HttpStatus.BAD_REQUEST);
        }
        throw new common_1.HttpException(`Erro ao ${operation}: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
    }
    static validateOwnership(entity, userId, entityName) {
        if (!entity) {
            throw new common_1.HttpException(`${entityName} não encontrado`, common_1.HttpStatus.NOT_FOUND);
        }
        if (entity.user_id !== userId) {
            throw new common_1.HttpException('Acesso negado', common_1.HttpStatus.FORBIDDEN);
        }
    }
}
exports.ErrorUtils = ErrorUtils;
//# sourceMappingURL=error.utils.js.map