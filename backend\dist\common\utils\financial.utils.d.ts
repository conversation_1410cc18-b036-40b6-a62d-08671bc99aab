import { FinancialSummary } from '../dto/common-response.dto';
export interface FinanceRecord {
    transaction_type: 'income' | 'expense';
    amount: string | number;
    is_saving?: boolean;
}
export declare class FinancialUtils {
    static calculateSummary(finances: FinanceRecord[]): FinancialSummary;
    static formatCurrency(value: number, currency?: string): string;
    static parseDecimal(value: string): number;
    static isValidAmount(amount: string | number): boolean;
    static calculatePercentageChange(oldValue: number, newValue: number): number;
}
