"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancialUtils = void 0;
const common_response_dto_1 = require("../dto/common-response.dto");
class FinancialUtils {
    static calculateSummary(finances) {
        let totalIncome = 0;
        let totalExpenses = 0;
        let totalAmount = 0;
        for (const finance of finances) {
            const amount = typeof finance.amount === 'string'
                ? parseFloat(finance.amount)
                : finance.amount;
            if (isNaN(amount))
                continue;
            totalAmount += Math.abs(amount);
            if (finance.transaction_type === 'income') {
                totalIncome += amount;
            }
            else if (finance.transaction_type === 'expense') {
                totalExpenses += Math.abs(amount);
            }
        }
        return new common_response_dto_1.FinancialSummary(totalAmount, totalIncome, totalExpenses);
    }
    static formatCurrency(value, currency = 'BRL') {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: currency,
        }).format(value);
    }
    static parseDecimal(value) {
        if (!value)
            return 0;
        const cleaned = value.replace(/[^\d.,]/g, '');
        const normalized = cleaned.replace(',', '.');
        return parseFloat(normalized) || 0;
    }
    static isValidAmount(amount) {
        const numValue = typeof amount === 'string'
            ? this.parseDecimal(amount)
            : amount;
        return !isNaN(numValue) && numValue >= 0;
    }
    static calculatePercentageChange(oldValue, newValue) {
        if (oldValue === 0)
            return newValue > 0 ? 100 : 0;
        return ((newValue - oldValue) / oldValue) * 100;
    }
}
exports.FinancialUtils = FinancialUtils;
//# sourceMappingURL=financial.utils.js.map