export declare class TimezoneUtils {
    static fromUserTimezone(date: Date | string, timezone: string): Date;
    static toUserTimezone(date: Date | string, timezone: string): Date;
    static formatInTimezone(date: Date | string, timezone: string, formatStr?: string): string;
    static prepareDateForDatabase(date: Date | string | null | undefined, timezone: string): Date | null;
}
export declare const fromUserTimezone: typeof TimezoneUtils.fromUserTimezone;
export declare const toUserTimezone: typeof TimezoneUtils.toUserTimezone;
export declare const formatForUser: typeof TimezoneUtils.formatInTimezone;
