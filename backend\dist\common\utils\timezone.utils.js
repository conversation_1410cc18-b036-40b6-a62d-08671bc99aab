"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatForUser = exports.toUserTimezone = exports.fromUserTimezone = exports.TimezoneUtils = void 0;
class TimezoneUtils {
    static fromUserTimezone(date, timezone) {
        if (date instanceof Date)
            return date;
        return new Date(date);
    }
    static toUserTimezone(date, timezone) {
        if (date instanceof Date)
            return date;
        return new Date(date);
    }
    static formatInTimezone(date, timezone, formatStr = 'yyyy-MM-dd HH:mm:ss') {
        const dateObj = date instanceof Date ? date : new Date(date);
        return dateObj.toISOString().replace('T', ' ').substring(0, 19);
    }
    static prepareDateForDatabase(date, timezone) {
        if (!date)
            return null;
        if (date instanceof Date)
            return date;
        return new Date(date);
    }
}
exports.TimezoneUtils = TimezoneUtils;
exports.fromUserTimezone = TimezoneUtils.fromUserTimezone;
exports.toUserTimezone = TimezoneUtils.toUserTimezone;
exports.formatForUser = TimezoneUtils.formatInTimezone;
//# sourceMappingURL=timezone.utils.js.map