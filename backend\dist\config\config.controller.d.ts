import { ConfigService } from './config.service';
import { CreateAnnualSavingsGoalDto, AnnualSavingsGoalResponseDto } from './dto/annual-savings-goal.dto';
import { UpdateUserConfigDto, UserConfigResponseDto } from './dto/user-config.dto';
export declare class ConfigController {
    private readonly configService;
    private readonly logger;
    constructor(configService: ConfigService);
    getAnnualSavingsGoal(req: any, year?: number): Promise<AnnualSavingsGoalResponseDto | {
        message: string;
    }>;
    createOrUpdateAnnualSavingsGoal(createGoalDto: CreateAnnualSavingsGoalDto, req: any): Promise<AnnualSavingsGoalResponseDto>;
    getUserConfig(req: any): Promise<UserConfigResponseDto>;
    updateUserConfig(updateUserDto: UpdateUserConfigDto, req: any): Promise<UserConfigResponseDto>;
}
