"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ConfigController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const config_service_1 = require("./config.service");
const annual_savings_goal_dto_1 = require("./dto/annual-savings-goal.dto");
const user_config_dto_1 = require("./dto/user-config.dto");
let ConfigController = ConfigController_1 = class ConfigController {
    configService;
    logger = new common_1.Logger(ConfigController_1.name);
    constructor(configService) {
        this.configService = configService;
    }
    async getAnnualSavingsGoal(req, year) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            const goal = await this.configService.getAnnualSavingsGoal(userId, year);
            if (!goal) {
                return { message: 'Meta anual de economia não encontrada' };
            }
            return goal;
        }
        catch (error) {
            this.logger.error('Erro ao buscar meta anual de economia:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createOrUpdateAnnualSavingsGoal(createGoalDto, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.configService.createOrUpdateAnnualSavingsGoal(createGoalDto, userId);
        }
        catch (error) {
            this.logger.error('Erro ao criar/atualizar meta anual de economia:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserConfig(req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.configService.getUserConfig(userId);
        }
        catch (error) {
            this.logger.error('Erro ao buscar configuração do usuário:', error);
            if (error.message.includes('não encontrado')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateUserConfig(updateUserDto, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.configService.updateUserConfig(userId, updateUserDto);
        }
        catch (error) {
            this.logger.error('Erro ao atualizar configuração do usuário:', error);
            if (error.message.includes('não encontrado')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ConfigController = ConfigController;
__decorate([
    (0, common_1.Get)('annual-savings-goal'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "getAnnualSavingsGoal", null);
__decorate([
    (0, common_1.Post)('annual-savings-goal'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [annual_savings_goal_dto_1.CreateAnnualSavingsGoalDto, Object]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "createOrUpdateAnnualSavingsGoal", null);
__decorate([
    (0, common_1.Get)('user'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "getUserConfig", null);
__decorate([
    (0, common_1.Put)('user'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_config_dto_1.UpdateUserConfigDto, Object]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "updateUserConfig", null);
exports.ConfigController = ConfigController = ConfigController_1 = __decorate([
    (0, common_1.Controller)('config'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], ConfigController);
//# sourceMappingURL=config.controller.js.map