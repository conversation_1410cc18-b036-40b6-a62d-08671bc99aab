import { CreateAnnualSavingsGoalDto, AnnualSavingsGoalResponseDto } from './dto/annual-savings-goal.dto';
import { UpdateUserConfigDto, UserConfigResponseDto } from './dto/user-config.dto';
export declare class ConfigService {
    private readonly logger;
    private db;
    getAnnualSavingsGoal(userId: number, year?: number): Promise<AnnualSavingsGoalResponseDto | null>;
    createOrUpdateAnnualSavingsGoal(createGoalDto: CreateAnnualSavingsGoalDto, userId: number): Promise<AnnualSavingsGoalResponseDto>;
    getUserConfig(userId: number): Promise<UserConfigResponseDto>;
    updateUserConfig(userId: number, updateUserDto: UpdateUserConfigDto): Promise<UserConfigResponseDto>;
}
