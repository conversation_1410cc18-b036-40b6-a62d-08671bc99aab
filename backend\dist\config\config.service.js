"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const common_1 = require("@nestjs/common");
const database_types_1 = require("../database.types");
let ConfigService = ConfigService_1 = class ConfigService {
    logger = new common_1.Logger(ConfigService_1.name);
    db = database_types_1.db;
    async getAnnualSavingsGoal(userId, year) {
        try {
            const currentYear = year || new Date().getFullYear();
            const goal = await this.db
                .selectFrom('config_annual_savings_goal')
                .selectAll()
                .where('user_id', '=', userId)
                .where('year', '=', currentYear)
                .executeTakeFirst();
            if (!goal) {
                return null;
            }
            this.logger.debug(`Meta anual de economia encontrada para usuário ${userId}, ano ${currentYear}`);
            return goal;
        }
        catch (error) {
            this.logger.error(`Erro ao buscar meta anual de economia para usuário ${userId}:`, error);
            throw new Error(`Erro ao buscar meta anual de economia: ${error.message}`);
        }
    }
    async createOrUpdateAnnualSavingsGoal(createGoalDto, userId) {
        try {
            const existingGoal = await this.getAnnualSavingsGoal(userId, createGoalDto.year);
            if (existingGoal) {
                await this.db
                    .updateTable('config_annual_savings_goal')
                    .set({
                    amount: createGoalDto.amount,
                    updated_at: new Date()
                })
                    .where('id', '=', existingGoal.id)
                    .where('user_id', '=', userId)
                    .execute();
                this.logger.log(`Meta anual de economia atualizada para usuário ${userId}, ano ${createGoalDto.year}`);
                const updatedGoal = await this.getAnnualSavingsGoal(userId, createGoalDto.year);
                return updatedGoal;
            }
            else {
                const goalData = {
                    ...createGoalDto,
                    user_id: userId,
                    created_at: new Date(),
                    updated_at: new Date()
                };
                const result = await this.db
                    .insertInto('config_annual_savings_goal')
                    .values(goalData)
                    .executeTakeFirst();
                if (!result.insertId) {
                    throw new Error('Falha ao criar meta anual de economia');
                }
                this.logger.log(`Meta anual de economia criada com ID ${result.insertId} para usuário ${userId}`);
                const newGoal = await this.getAnnualSavingsGoal(userId, createGoalDto.year);
                return newGoal;
            }
        }
        catch (error) {
            this.logger.error(`Erro ao criar/atualizar meta anual de economia para usuário ${userId}:`, error);
            throw new Error(`Erro ao criar/atualizar meta anual de economia: ${error.message}`);
        }
    }
    async getUserConfig(userId) {
        try {
            const user = await this.db
                .selectFrom('users')
                .select(['id', 'name', 'email', 'phone', 'timezone', 'created_at', 'updated_at'])
                .where('id', '=', userId)
                .where('deleted_at', 'is', null)
                .executeTakeFirst();
            if (!user) {
                throw new common_1.NotFoundException(`Usuário com ID ${userId} não encontrado`);
            }
            return {
                id: user.id,
                name: user.name,
                email: user.email,
                phone: user.phone || undefined,
                timezone: user.timezone,
                created_at: user.created_at,
                updated_at: user.updated_at
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao buscar configuração do usuário ${userId}:`, error);
            throw new Error(`Erro ao buscar configuração do usuário: ${error.message}`);
        }
    }
    async updateUserConfig(userId, updateUserDto) {
        try {
            await this.getUserConfig(userId);
            const updateData = {
                ...updateUserDto,
                updated_at: new Date()
            };
            Object.keys(updateData).forEach(key => {
                if (updateData[key] === undefined) {
                    delete updateData[key];
                }
            });
            await this.db
                .updateTable('users')
                .set(updateData)
                .where('id', '=', userId)
                .where('deleted_at', 'is', null)
                .execute();
            this.logger.log(`Configuração do usuário ${userId} atualizada`);
            return this.getUserConfig(userId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao atualizar configuração do usuário ${userId}:`, error);
            throw new Error(`Erro ao atualizar configuração do usuário: ${error.message}`);
        }
    }
};
exports.ConfigService = ConfigService;
exports.ConfigService = ConfigService = ConfigService_1 = __decorate([
    (0, common_1.Injectable)()
], ConfigService);
//# sourceMappingURL=config.service.js.map