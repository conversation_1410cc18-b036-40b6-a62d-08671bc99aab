"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnnualSavingsGoalResponseDto = exports.UpdateAnnualSavingsGoalDto = exports.CreateAnnualSavingsGoalDto = void 0;
const class_validator_1 = require("class-validator");
class CreateAnnualSavingsGoalDto {
    year;
    amount;
}
exports.CreateAnnualSavingsGoalDto = CreateAnnualSavingsGoalDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateAnnualSavingsGoalDto.prototype, "year", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAnnualSavingsGoalDto.prototype, "amount", void 0);
class UpdateAnnualSavingsGoalDto {
    amount;
}
exports.UpdateAnnualSavingsGoalDto = UpdateAnnualSavingsGoalDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAnnualSavingsGoalDto.prototype, "amount", void 0);
class AnnualSavingsGoalResponseDto {
    id;
    year;
    amount;
    user_id;
    created_at;
    updated_at;
}
exports.AnnualSavingsGoalResponseDto = AnnualSavingsGoalResponseDto;
//# sourceMappingURL=annual-savings-goal.dto.js.map