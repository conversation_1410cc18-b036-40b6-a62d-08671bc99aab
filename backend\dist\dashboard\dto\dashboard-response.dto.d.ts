export declare class TasksSummaryDto {
    completed: number;
    total: number;
}
export declare class FinancesSummaryDto {
    spent: number;
    budget: number;
    income: number;
    expenses: number;
    savings: number;
    hasAnnualGoal: boolean;
}
export declare class IdeasSummaryDto {
    today: number;
    total: number;
    favorites: number;
}
export declare class MonthlyProgressDto {
    appointments: {
        completed: number;
        total: number;
    };
    savings: number;
    ideas: number;
    tasksCompleted: number;
    financialGoalProgress: number;
}
export declare class UserSummaryDto {
    name: string;
    timezone: string;
}
export declare class DashboardResponseDto {
    user: UserSummaryDto;
    tasks: TasksSummaryDto;
    finances: FinancesSummaryDto;
    ideas: IdeasSummaryDto;
    monthlyProgress: MonthlyProgressDto;
    currentMonth: string;
    currentYear: number;
}
