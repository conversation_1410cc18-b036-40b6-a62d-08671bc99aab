"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardResponseDto = exports.UserSummaryDto = exports.MonthlyProgressDto = exports.IdeasSummaryDto = exports.FinancesSummaryDto = exports.TasksSummaryDto = void 0;
class TasksSummaryDto {
    completed;
    total;
}
exports.TasksSummaryDto = TasksSummaryDto;
class FinancesSummaryDto {
    spent;
    budget;
    income;
    expenses;
    savings;
    hasAnnualGoal;
}
exports.FinancesSummaryDto = FinancesSummaryDto;
class IdeasSummaryDto {
    today;
    total;
    favorites;
}
exports.IdeasSummaryDto = IdeasSummaryDto;
class MonthlyProgressDto {
    appointments;
    savings;
    ideas;
    tasksCompleted;
    financialGoalProgress;
}
exports.MonthlyProgressDto = MonthlyProgressDto;
class UserSummaryDto {
    name;
    timezone;
}
exports.UserSummaryDto = UserSummaryDto;
class DashboardResponseDto {
    user;
    tasks;
    finances;
    ideas;
    monthlyProgress;
    currentMonth;
    currentYear;
}
exports.DashboardResponseDto = DashboardResponseDto;
//# sourceMappingURL=dashboard-response.dto.js.map