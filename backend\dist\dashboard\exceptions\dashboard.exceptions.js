"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvalidUserTokenException = exports.DashboardDataException = exports.UserNotFoundException = void 0;
const common_1 = require("@nestjs/common");
class UserNotFoundException extends common_1.HttpException {
    constructor(userId) {
        super(`Usuário com ID ${userId} não encontrado`, common_1.HttpStatus.NOT_FOUND);
    }
}
exports.UserNotFoundException = UserNotFoundException;
class DashboardDataException extends common_1.HttpException {
    constructor(message, status = common_1.HttpStatus.INTERNAL_SERVER_ERROR) {
        super(`Erro ao buscar dados do dashboard: ${message}`, status);
    }
}
exports.DashboardDataException = DashboardDataException;
class InvalidUserTokenException extends common_1.HttpException {
    constructor() {
        super('Token de usuário inválido ou não fornecido', common_1.HttpStatus.UNAUTHORIZED);
    }
}
exports.InvalidUserTokenException = InvalidUserTokenException;
//# sourceMappingURL=dashboard.exceptions.js.map