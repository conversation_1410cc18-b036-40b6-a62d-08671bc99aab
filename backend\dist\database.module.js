"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const schedule_1 = require("@nestjs/schedule");
const database_provider_1 = require("./database/database.provider");
const database_health_service_1 = require("./database/database-health.service");
const database_error_interceptor_1 = require("./database/database-error.interceptor");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            schedule_1.ScheduleModule.forRoot(),
        ],
        providers: [
            ...database_provider_1.databaseProviders,
            database_health_service_1.DatabaseHealthService,
            database_error_interceptor_1.DatabaseErrorInterceptor,
        ],
        exports: [
            ...database_provider_1.databaseProviders,
            database_health_service_1.DatabaseHealthService,
            database_error_interceptor_1.DatabaseErrorInterceptor,
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map