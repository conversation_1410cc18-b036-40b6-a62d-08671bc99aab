import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { DatabaseHealthService } from './database-health.service';
export declare class DatabaseErrorInterceptor implements NestInterceptor {
    private readonly databaseHealthService;
    private readonly logger;
    constructor(databaseHealthService: DatabaseHealthService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private isDatabaseError;
    private isConnectionError;
    private isQueryError;
}
