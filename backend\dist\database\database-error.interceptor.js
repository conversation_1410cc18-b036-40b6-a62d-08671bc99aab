"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DatabaseErrorInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseErrorInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const database_health_service_1 = require("./database-health.service");
let DatabaseErrorInterceptor = DatabaseErrorInterceptor_1 = class DatabaseErrorInterceptor {
    databaseHealthService;
    logger = new common_1.Logger(DatabaseErrorInterceptor_1.name);
    constructor(databaseHealthService) {
        this.databaseHealthService = databaseHealthService;
    }
    intercept(context, next) {
        return next.handle().pipe((0, operators_1.catchError)((error) => {
            if (this.isDatabaseError(error)) {
                this.logger.error('Database error intercepted:', error.message);
                const request = context.switchToHttp().getRequest();
                this.logger.error(`Error occurred in ${request.method} ${request.url}`);
                if (this.isConnectionError(error)) {
                    this.logger.warn('Connection error detected, health service will handle reconnection');
                    return (0, rxjs_1.throwError)(() => new common_1.HttpException({
                        statusCode: common_1.HttpStatus.SERVICE_UNAVAILABLE,
                        message: 'Serviço temporariamente indisponível. Tente novamente em alguns momentos.',
                        error: 'Service Unavailable',
                        timestamp: new Date().toISOString(),
                        path: context.switchToHttp().getRequest().url,
                    }, common_1.HttpStatus.SERVICE_UNAVAILABLE));
                }
                if (this.isQueryError(error)) {
                    this.logger.error('Query error:', error.message);
                    return (0, rxjs_1.throwError)(() => new common_1.HttpException({
                        statusCode: common_1.HttpStatus.BAD_REQUEST,
                        message: 'Erro na consulta ao banco de dados',
                        error: 'Bad Request',
                        timestamp: new Date().toISOString(),
                        path: context.switchToHttp().getRequest().url,
                    }, common_1.HttpStatus.BAD_REQUEST));
                }
                return (0, rxjs_1.throwError)(() => new common_1.HttpException({
                    statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Erro interno do servidor',
                    error: 'Internal Server Error',
                    timestamp: new Date().toISOString(),
                    path: context.switchToHttp().getRequest().url,
                }, common_1.HttpStatus.INTERNAL_SERVER_ERROR));
            }
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
    isDatabaseError(error) {
        if (!error)
            return false;
        const databaseErrorIndicators = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
            'PROTOCOL_CONNECTION_LOST',
            'ER_',
            'WARN_DATA_TRUNCATED',
            'ER_NO_SUCH_TABLE',
            'ER_BAD_FIELD_ERROR',
            'ER_DUP_ENTRY',
            'Connection',
            'Pool',
            'mysql',
            'Kysely',
        ];
        return databaseErrorIndicators.some(indicator => error.code?.includes(indicator) ||
            error.message?.includes(indicator) ||
            error.name?.includes(indicator) ||
            error.errno?.toString().includes(indicator));
    }
    isConnectionError(error) {
        if (!error)
            return false;
        const connectionErrorCodes = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
            'PROTOCOL_CONNECTION_LOST',
            'ER_GET_CONNECTION_TIMEOUT',
            'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR',
            'PROTOCOL_ENQUEUE_AFTER_QUIT',
            'Connection lost',
            'Connection timeout',
        ];
        return connectionErrorCodes.some(code => error.code === code ||
            error.message?.includes(code) ||
            error.errno === code);
    }
    isQueryError(error) {
        if (!error)
            return false;
        const queryErrorCodes = [
            'ER_NO_SUCH_TABLE',
            'ER_BAD_FIELD_ERROR',
            'ER_DUP_ENTRY',
            'ER_SYNTAX_ERROR',
            'ER_UNKNOWN_COLUMN',
            'ER_NON_UNIQ_ERROR',
            'ER_TABLE_EXISTS_ERROR',
            'ER_PARSE_ERROR',
        ];
        return queryErrorCodes.some(code => error.code === code ||
            error.message?.includes(code) ||
            error.errno === code);
    }
};
exports.DatabaseErrorInterceptor = DatabaseErrorInterceptor;
exports.DatabaseErrorInterceptor = DatabaseErrorInterceptor = DatabaseErrorInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_health_service_1.DatabaseHealthService])
], DatabaseErrorInterceptor);
//# sourceMappingURL=database-error.interceptor.js.map