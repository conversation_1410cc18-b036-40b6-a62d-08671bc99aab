{"version": 3, "file": "database-error.interceptor.js", "sourceRoot": "", "sources": ["../../src/database/database-error.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAQwB;AACxB,+BAA8C;AAC9C,8CAA4C;AAC5C,uEAAkE;AAG3D,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGN;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAEpE,YAA6B,qBAA4C;QAA5C,0BAAqB,GAArB,qBAAqB,CAAuB;IAAG,CAAC;IAE7E,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YACnB,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBAGhE,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAGxE,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;oBAGvF,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,IAAI,sBAAa,CACvC;wBACE,UAAU,EAAE,mBAAU,CAAC,mBAAmB;wBAC1C,OAAO,EAAE,2EAA2E;wBACpF,KAAK,EAAE,qBAAqB;wBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC,GAAG;qBAC9C,EACD,mBAAU,CAAC,mBAAmB,CAC/B,CAAC,CAAC;gBACL,CAAC;gBAGD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;oBACjD,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,IAAI,sBAAa,CACvC;wBACE,UAAU,EAAE,mBAAU,CAAC,WAAW;wBAClC,OAAO,EAAE,oCAAoC;wBAC7C,KAAK,EAAE,aAAa;wBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,IAAI,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC,GAAG;qBAC9C,EACD,mBAAU,CAAC,WAAW,CACvB,CAAC,CAAC;gBACL,CAAC;gBAGD,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,IAAI,sBAAa,CACvC;oBACE,UAAU,EAAE,mBAAU,CAAC,qBAAqB;oBAC5C,OAAO,EAAE,0BAA0B;oBACnC,KAAK,EAAE,uBAAuB;oBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,IAAI,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC,GAAG;iBAC9C,EACD,mBAAU,CAAC,qBAAqB,CACjC,CAAC,CAAC;YACL,CAAC;YAGD,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,KAAU;QAChC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QAGzB,MAAM,uBAAuB,GAAG;YAC9B,YAAY;YACZ,cAAc;YACd,WAAW;YACX,WAAW;YACX,0BAA0B;YAC1B,KAAK;YACL,qBAAqB;YACrB,kBAAkB;YAClB,oBAAoB;YACpB,cAAc;YACd,YAAY;YACZ,MAAM;YACN,OAAO;YACP,QAAQ;SACT,CAAC;QAEF,OAAO,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAC9C,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC;YAC/B,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC;YAClC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC;YAC/B,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAC5C,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,KAAU;QAClC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QAEzB,MAAM,oBAAoB,GAAG;YAC3B,YAAY;YACZ,cAAc;YACd,WAAW;YACX,WAAW;YACX,0BAA0B;YAC1B,2BAA2B;YAC3B,oCAAoC;YACpC,6BAA6B;YAC7B,iBAAiB;YACjB,oBAAoB;SACrB,CAAC;QAEF,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACtC,KAAK,CAAC,IAAI,KAAK,IAAI;YACnB,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC;YAC7B,KAAK,CAAC,KAAK,KAAK,IAAI,CACrB,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,KAAU;QAC7B,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QAEzB,MAAM,eAAe,GAAG;YACtB,kBAAkB;YAClB,oBAAoB;YACpB,cAAc;YACd,iBAAiB;YACjB,mBAAmB;YACnB,mBAAmB;YACnB,uBAAuB;YACvB,gBAAgB;SACjB,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACjC,KAAK,CAAC,IAAI,KAAK,IAAI;YACnB,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC;YAC7B,KAAK,CAAC,KAAK,KAAK,IAAI,CACrB,CAAC;IACJ,CAAC;CACF,CAAA;AA1IY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAIyC,+CAAqB;GAH9D,wBAAwB,CA0IpC"}