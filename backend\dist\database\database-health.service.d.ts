import { OnModuleInit } from '@nestjs/common';
import { Connection } from 'mysql2/promise';
export declare class DatabaseHealthService implements OnModuleInit {
    private readonly connection;
    private readonly logger;
    private isHealthy;
    private lastHealthCheck;
    private consecutiveFailures;
    private readonly maxConsecutiveFailures;
    constructor(connection: Connection);
    onModuleInit(): Promise<void>;
    scheduledHealthCheck(): Promise<void>;
    private performHealthCheck;
    executeWithRetry<T>(operation: () => Promise<T>, maxRetries?: number, retryDelay?: number): Promise<T>;
    private isConnectionError;
    private sleep;
    getHealthStatus(): {
        isHealthy: boolean;
        lastHealthCheck: Date | null;
        consecutiveFailures: number;
    };
    testConnection(): Promise<{
        success: boolean;
        duration: number;
        error?: string;
    }>;
}
