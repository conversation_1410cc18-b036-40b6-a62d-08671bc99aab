"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseHealthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseHealthService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const database_provider_1 = require("./database.provider");
let DatabaseHealthService = DatabaseHealthService_1 = class DatabaseHealthService {
    connection;
    logger = new common_1.Logger(DatabaseHealthService_1.name);
    isHealthy = false;
    lastHealthCheck = null;
    consecutiveFailures = 0;
    maxConsecutiveFailures = 3;
    constructor(connection) {
        this.connection = connection;
    }
    async onModuleInit() {
        await this.performHealthCheck();
    }
    async scheduledHealthCheck() {
        await this.performHealthCheck();
    }
    async performHealthCheck() {
        try {
            const startTime = Date.now();
            await this.connection.execute('SELECT 1 as test');
            const duration = Date.now() - startTime;
            this.isHealthy = true;
            this.lastHealthCheck = new Date();
            this.consecutiveFailures = 0;
        }
        catch (error) {
            this.consecutiveFailures++;
            this.isHealthy = false;
            this.logger.error(`Database health check failed (attempt ${this.consecutiveFailures}/${this.maxConsecutiveFailures}):`, error.message);
            if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
                this.logger.error('Database appears to be unhealthy after multiple consecutive failures');
            }
        }
    }
    async executeWithRetry(operation, maxRetries = 3, retryDelay = 1000) {
        let lastError = new Error('Unknown error');
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (this.isConnectionError(error)) {
                    this.logger.warn(`Database operation failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
                    if (attempt < maxRetries) {
                        this.logger.debug(`Retrying in ${retryDelay}ms...`);
                        await this.sleep(retryDelay);
                        retryDelay *= 2;
                        await this.performHealthCheck();
                        continue;
                    }
                }
                throw error;
            }
        }
        throw lastError;
    }
    isConnectionError(error) {
        if (!error)
            return false;
        const connectionErrorCodes = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
            'PROTOCOL_CONNECTION_LOST',
            'ER_GET_CONNECTION_TIMEOUT',
            'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR',
            'PROTOCOL_ENQUEUE_AFTER_QUIT',
        ];
        return connectionErrorCodes.some(code => error.code === code ||
            error.message?.includes(code) ||
            error.errno === code);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    getHealthStatus() {
        return {
            isHealthy: this.isHealthy,
            lastHealthCheck: this.lastHealthCheck,
            consecutiveFailures: this.consecutiveFailures,
        };
    }
    async testConnection() {
        const startTime = Date.now();
        try {
            await this.connection.execute('SELECT 1 as test');
            return {
                success: true,
                duration: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                success: false,
                duration: Date.now() - startTime,
                error: error.message,
            };
        }
    }
};
exports.DatabaseHealthService = DatabaseHealthService;
__decorate([
    (0, schedule_1.Cron)('0 */2 * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseHealthService.prototype, "scheduledHealthCheck", null);
exports.DatabaseHealthService = DatabaseHealthService = DatabaseHealthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.MYSQL2_CONNECTION)),
    __metadata("design:paramtypes", [Object])
], DatabaseHealthService);
//# sourceMappingURL=database-health.service.js.map