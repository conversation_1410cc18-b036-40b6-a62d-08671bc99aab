{"version": 3, "file": "database-health.service.js", "sourceRoot": "", "sources": ["../../src/database/database-health.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAA0E;AAC1E,+CAAwD;AAExD,2DAAwD;AAGjD,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IASb;IARF,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IACzD,SAAS,GAAG,KAAK,CAAC;IAClB,eAAe,GAAgB,IAAI,CAAC;IACpC,mBAAmB,GAAG,CAAC,CAAC;IACf,sBAAsB,GAAG,CAAC,CAAC;IAE5C,YAEmB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAEJ,KAAK,CAAC,YAAY;QAEhB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAGK,AAAN,KAAK,CAAC,oBAAoB;QACxB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAEvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,sBAAsB,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAEvI,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,SAA2B,EAC3B,aAAqB,CAAC,EACtB,aAAqB,IAAI;QAEzB,IAAI,SAAS,GAAU,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QAElD,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,OAAO,IAAI,UAAU,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAEnG,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;wBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,UAAU,OAAO,CAAC,CAAC;wBACpD,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBAG7B,UAAU,IAAI,CAAC,CAAC;wBAGhB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBAEhC,SAAS;oBACX,CAAC;gBACH,CAAC;gBAGD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,KAAU;QAClC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QAEzB,MAAM,oBAAoB,GAAG;YAC3B,YAAY;YACZ,cAAc;YACd,WAAW;YACX,WAAW;YACX,0BAA0B;YAC1B,2BAA2B;YAC3B,oCAAoC;YACpC,6BAA6B;SAC9B,CAAC;QAEF,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACtC,KAAK,CAAC,IAAI,KAAK,IAAI;YACnB,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC;YAC7B,KAAK,CAAC,KAAK,KAAK,IAAI,CACrB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,eAAe;QACb,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAElD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACjC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBAChC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAtIY,sDAAqB;AAkB1B;IADL,IAAA,eAAI,EAAC,eAAe,CAAC;;;;iEAGrB;gCApBU,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IASR,WAAA,IAAA,eAAM,EAAC,qCAAiB,CAAC,CAAA;;GARjB,qBAAqB,CAsIjC"}