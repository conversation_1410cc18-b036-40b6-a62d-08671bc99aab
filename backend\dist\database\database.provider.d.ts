import { Kysely } from 'kysely';
import { Connection } from 'mysql2/promise';
import { ConfigService } from '@nestjs/config';
import { Database } from '../database.types';
export declare const DATABASE_CONNECTION = "DATABASE_CONNECTION";
export declare const MYSQL2_CONNECTION = "MYSQL2_CONNECTION";
export declare const databaseProviders: ({
    provide: string;
    useFactory: (configService: ConfigService) => Kysely<Database>;
    inject: (typeof ConfigService)[];
} | {
    provide: string;
    useFactory: (configService: ConfigService) => Promise<Connection>;
    inject: (typeof ConfigService)[];
})[];
