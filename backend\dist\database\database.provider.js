"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseProviders = exports.MYSQL2_CONNECTION = exports.DATABASE_CONNECTION = void 0;
const kysely_1 = require("kysely");
const promise_1 = require("mysql2/promise");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
exports.DATABASE_CONNECTION = 'DATABASE_CONNECTION';
exports.MYSQL2_CONNECTION = 'MYSQL2_CONNECTION';
exports.databaseProviders = [
    {
        provide: exports.DATABASE_CONNECTION,
        useFactory: (configService) => {
            const logger = new common_1.Logger('DatabaseProvider');
            if (configService.get('MYSQL_VERBOSE_LOGS') !== 'true') {
                process.env.DEBUG = '';
                process.env.NODE_DEBUG = '';
            }
            const poolConfig = {
                host: configService.get('DB_HOST') || 'localhost',
                port: parseInt(configService.get('DB_PORT') || '3306'),
                user: configService.get('DB_USER') || 'root',
                password: configService.get('DB_PASSWORD'),
                database: configService.get('DB_DATABASE'),
                connectionLimit: parseInt(configService.get('DB_CONNECTION_LIMIT') || '10'),
                keepAliveInitialDelay: 300000,
                idleTimeout: parseInt(configService.get('DB_IDLE_TIMEOUT') || '300000'),
                maxIdle: parseInt(configService.get('DB_MAX_IDLE') || '5'),
                timezone: configService.get('DB_TIMEZONE') || '+00:00',
                ssl: configService.get('DB_SSL') === 'true' ? {
                    rejectUnauthorized: configService.get('DB_SSL_REJECT_UNAUTHORIZED') !== 'false'
                } : undefined,
                supportBigNumbers: true,
                bigNumberStrings: true,
                dateStrings: false,
                debug: configService.get('MYSQL_VERBOSE_LOGS') === 'true',
                trace: false,
                multipleStatements: false,
                charset: 'utf8mb4',
            };
            logger.log(`Connecting to MySQL at ${poolConfig.host}:${poolConfig.port}/${poolConfig.database}`);
            logger.log(`Pool configuration: connectionLimit=${poolConfig.connectionLimit}, idleTimeout=${poolConfig.idleTimeout}ms, maxIdle=${poolConfig.maxIdle}`);
            const pool = (0, promise_1.createPool)(poolConfig);
            const dialect = new kysely_1.MysqlDialect({
                pool,
            });
            const kysely = new kysely_1.Kysely({
                dialect,
                log: configService.get('NODE_ENV') === 'development' ? ['query', 'error'] : ['error'],
            });
            kysely.selectFrom('users')
                .select(['id'])
                .limit(1)
                .execute()
                .then(() => {
                logger.log('Database connection test successful');
            })
                .catch((error) => {
                logger.error('Database connection test failed:', error.message);
            });
            return kysely;
        },
        inject: [config_1.ConfigService],
    },
    {
        provide: exports.MYSQL2_CONNECTION,
        useFactory: async (configService) => {
            const logger = new common_1.Logger('MySQL2ConnectionProvider');
            if (configService.get('MYSQL_VERBOSE_LOGS') !== 'true') {
                process.env.DEBUG = '';
                process.env.NODE_DEBUG = '';
            }
            const connectionConfig = {
                host: configService.get('DB_HOST') || 'localhost',
                port: parseInt(configService.get('DB_PORT') || '3306'),
                user: configService.get('DB_USER') || 'root',
                password: configService.get('DB_PASSWORD'),
                database: configService.get('DB_DATABASE'),
                timezone: configService.get('DB_TIMEZONE') || '+00:00',
                charset: 'utf8mb4',
                supportBigNumbers: true,
                bigNumberStrings: true,
                dateStrings: false,
                debug: configService.get('MYSQL_VERBOSE_LOGS') === 'true',
                trace: false,
                multipleStatements: false,
            };
            try {
                const connection = await (0, promise_1.createConnection)(connectionConfig);
                logger.log('MySQL2 direct connection established successfully');
                return connection;
            }
            catch (error) {
                logger.error('Failed to create MySQL2 direct connection:', error.message);
                throw error;
            }
        },
        inject: [config_1.ConfigService],
    },
];
//# sourceMappingURL=database.provider.js.map