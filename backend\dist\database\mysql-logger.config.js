"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MySQLLoggerConfig = void 0;
const common_1 = require("@nestjs/common");
class MySQLLoggerConfig {
    static logger = new common_1.Logger('MySQL2');
    static originalConsoleLog;
    static originalConsoleError;
    static originalConsoleWarn;
    static isConfigured = false;
    static configure() {
        if (this.isConfigured) {
            return;
        }
        this.originalConsoleLog = console.log;
        this.originalConsoleError = console.error;
        this.originalConsoleWarn = console.warn;
        const originalStdoutWrite = process.stdout.write;
        process.stdout.write = function (chunk, encoding, callback) {
            const message = chunk.toString();
            if (MySQLLoggerConfig.shouldSuppressLog(message)) {
                return true;
            }
            return originalStdoutWrite.call(process.stdout, chunk, encoding, callback);
        };
        console.log = (...args) => {
            const message = args.join(' ');
            if (this.shouldSuppressLog(message)) {
                return;
            }
            this.originalConsoleLog.apply(console, args);
        };
        console.error = (...args) => {
            const message = args.join(' ');
            if (this.isImportantError(message)) {
                this.originalConsoleError.apply(console, args);
                return;
            }
            if (this.shouldSuppressLog(message)) {
                return;
            }
            this.originalConsoleError.apply(console, args);
        };
        console.warn = (...args) => {
            const message = args.join(' ');
            if (this.isConfigurationWarning(message)) {
                if (message.includes('acquireTimeout') || message.includes('timeout') || message.includes('reconnect')) {
                    this.logger.debug('MySQL2 configuration warning suppressed (non-critical)');
                }
                return;
            }
            this.originalConsoleWarn.apply(console, args);
        };
        this.isConfigured = true;
        this.logger.debug('MySQL2 verbose logging suppression configured');
    }
    static restore() {
        if (!this.isConfigured) {
            return;
        }
        console.log = this.originalConsoleLog;
        console.error = this.originalConsoleError;
        console.warn = this.originalConsoleWarn;
        this.isConfigured = false;
        this.logger.debug('MySQL2 logging configuration restored');
    }
    static shouldSuppressLog(message) {
        const suppressPatterns = [
            /raw:\s+[0-9a-f]+/i,
            /at\s+.*mysql2.*connection\.js/i,
            /at\s+.*mysql2.*packet_parser\.js/i,
            /ClientHandshake#unknown\s+name/i,
            /Server\s+hello\s+packet:\s+capability\s+flags/i,
            /Sending\s+handshake\s+packet:\s+flags/i,
            /^[0-9a-f\s]+$/i,
            /Add\s+command:\s+ClientHandshake/i,
            /^\d+\s+\d+\s+[<>=]+\s+/i,
        ];
        return suppressPatterns.some(pattern => pattern.test(message));
    }
    static isImportantError(message) {
        const importantPatterns = [
            /connection\s+refused/i,
            /access\s+denied/i,
            /unknown\s+database/i,
            /table.*doesn't\s+exist/i,
            /syntax\s+error/i,
            /duplicate\s+entry/i,
            /foreign\s+key\s+constraint/i,
        ];
        return importantPatterns.some(pattern => pattern.test(message));
    }
    static isConfigurationWarning(message) {
        const configWarningPatterns = [
            /Ignoring\s+invalid\s+configuration\s+option/i,
            /acquireTimeout.*warning/i,
            /timeout.*warning/i,
            /reconnect.*warning/i,
        ];
        return configWarningPatterns.some(pattern => pattern.test(message));
    }
}
exports.MySQLLoggerConfig = MySQLLoggerConfig;
//# sourceMappingURL=mysql-logger.config.js.map