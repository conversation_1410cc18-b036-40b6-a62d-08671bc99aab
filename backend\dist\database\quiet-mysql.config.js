"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.configureQuietMySQL = configureQuietMySQL;
exports.restoreOriginalLogging = restoreOriginalLogging;
exports.isQuietModeEnabled = isQuietModeEnabled;
let isConfigured = false;
let originalConsoleLog;
let originalStdoutWrite;
function configureQuietMySQL() {
    if (isConfigured) {
        return;
    }
    originalConsoleLog = console.log;
    originalStdoutWrite = process.stdout.write;
    console.log = (...args) => {
        const message = args.join(' ');
        if (shouldSuppressMessage(message)) {
            return;
        }
        originalConsoleLog.apply(console, args);
    };
    try {
        process.stdout.write = function (chunk, encoding, callback) {
            const message = chunk.toString();
            if (shouldSuppressMessage(message)) {
                if (typeof callback === 'function') {
                    setImmediate(callback);
                }
                return true;
            }
            return originalStdoutWrite.call(process.stdout, chunk, encoding, callback);
        };
    }
    catch (error) {
        console.warn('Could not override stdout.write for MySQL logging suppression');
    }
    process.env.DEBUG = '';
    process.env.NODE_DEBUG = '';
    isConfigured = true;
}
function restoreOriginalLogging() {
    if (!isConfigured) {
        return;
    }
    console.log = originalConsoleLog;
    process.stdout.write = originalStdoutWrite;
    isConfigured = false;
}
function shouldSuppressMessage(message) {
    const suppressPatterns = [
        /raw:\s+[0-9a-f]+/i,
        /at\s+.*mysql2.*\.js:\d+:\d+/i,
        /ClientHandshake#unknown\s+name/i,
        /Add\s+command:\s+ClientHandshake/i,
        /Server\s+hello\s+packet:\s+capability\s+flags/i,
        /Sending\s+handshake\s+packet:\s+flags/i,
        /^[0-9a-f\s]{50,}$/i,
        /^\d+\s+\d+\s+[<>=]+\s+/i,
        /Ignoring\s+invalid\s+configuration\s+option.*acquireTimeout/i,
        /Ignoring\s+invalid\s+configuration\s+option.*timeout/i,
        /Ignoring\s+invalid\s+configuration\s+option.*reconnect/i,
    ];
    return suppressPatterns.some(pattern => pattern.test(message));
}
function isQuietModeEnabled() {
    return isConfigured;
}
//# sourceMappingURL=quiet-mysql.config.js.map