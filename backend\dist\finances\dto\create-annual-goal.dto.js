"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAnnualGoalDto = void 0;
const class_validator_1 = require("class-validator");
class CreateAnnualGoalDto {
    goalAmount;
    year;
}
exports.CreateAnnualGoalDto = CreateAnnualGoalDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'O valor da meta anual é obrigatório' }),
    (0, class_validator_1.IsNumber)({}, { message: 'O valor da meta anual deve ser um número' }),
    (0, class_validator_1.Min)(0.01, { message: 'O valor da meta anual deve ser maior que zero' }),
    __metadata("design:type", Number)
], CreateAnnualGoalDto.prototype, "goalAmount", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: 'O ano deve ser um número inteiro' }),
    (0, class_validator_1.Min)(2000, { message: 'O ano deve ser maior que 2000' }),
    __metadata("design:type", Number)
], CreateAnnualGoalDto.prototype, "year", void 0);
//# sourceMappingURL=create-annual-goal.dto.js.map