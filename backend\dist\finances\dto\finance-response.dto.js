"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceSummaryDto = exports.FinanceListResponseDto = exports.FinanceResponseDto = void 0;
class FinanceResponseDto {
    id;
    transaction_type;
    category_id;
    category_name;
    is_saving;
    description;
    amount;
    transaction_date;
    user_id;
    created_at;
    updated_at;
}
exports.FinanceResponseDto = FinanceResponseDto;
class FinanceListResponseDto {
    finances;
    total;
    page;
    limit;
}
exports.FinanceListResponseDto = FinanceListResponseDto;
class FinanceSummaryDto {
    totalIncome;
    totalExpenses;
    totalSavings;
    balance;
    period;
}
exports.FinanceSummaryDto = FinanceSummaryDto;
//# sourceMappingURL=finance-response.dto.js.map