import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
import { CreateFinanceCategoryDto } from './dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from './dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from './dto/finance-category-response.dto';
import { FinanceRepository } from '../repositories/finance.repository';
import { FinanceCategoryRepository } from '../repositories/finance-category.repository';
export declare class FinancesService {
    private readonly financeRepository;
    private readonly financeCategoryRepository;
    private readonly logger;
    constructor(financeRepository: FinanceRepository, financeCategoryRepository: FinanceCategoryRepository);
    create(createFinanceDto: CreateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    findAll(userId: number, userTimezone: string, page?: number, limit?: number): Promise<FinanceListResponseDto>;
    findOne(id: number, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    update(id: number, updateFinanceDto: UpdateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto>;
    getExpensesByCategory(userId: number, startDate?: Date, endDate?: Date): Promise<{
        category: string;
        amount: number;
        count: number;
    }[]>;
    getAnnualSummary(userId: number, year: number, userTimezone: string): Promise<{
        year: number;
        monthlyData: {
            month: string;
            monthNumber: number;
            income: number;
            expenses: number;
            savings: number;
            balance: number;
        }[];
        totals: {
            income: number;
            expenses: number;
            savings: number;
            balance: number;
        };
    }>;
    getAnnualCategoryDistribution(userId: number, year: number, userTimezone: string): Promise<{
        name: string;
        value: number;
        color: string;
        percentage: number;
        count: number;
    }[]>;
    createCategory(createCategoryDto: CreateFinanceCategoryDto, userId: number): Promise<FinanceCategoryResponseDto>;
    findAllCategories(userId: number): Promise<FinanceCategoryResponseDto[]>;
    findOneCategory(id: number, userId: number): Promise<FinanceCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateFinanceCategoryDto, userId: number): Promise<FinanceCategoryResponseDto>;
    removeCategory(id: number, userId: number): Promise<void>;
}
