export declare class UpdateGoogleCalendarIntegrationDto {
    calendar_email: string;
    is_active?: boolean;
    sync_tasks?: boolean;
    sync_appointments?: boolean;
    default_reminder_minutes?: number;
}
export declare class GoogleCalendarIntegrationResponseDto {
    id: number;
    calendar_email: string;
    is_active: boolean;
    sync_tasks: boolean;
    sync_appointments: boolean;
    default_reminder_minutes: number;
    created_at: Date;
    updated_at: Date;
}
