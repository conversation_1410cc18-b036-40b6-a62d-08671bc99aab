"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleCalendarIntegrationResponseDto = exports.UpdateGoogleCalendarIntegrationDto = void 0;
const class_validator_1 = require("class-validator");
class UpdateGoogleCalendarIntegrationDto {
    calendar_email;
    is_active;
    sync_tasks;
    sync_appointments;
    default_reminder_minutes;
}
exports.UpdateGoogleCalendarIntegrationDto = UpdateGoogleCalendarIntegrationDto;
__decorate([
    (0, class_validator_1.IsEmail)({}, { message: 'Email deve ter um formato válido' }),
    __metadata("design:type", String)
], UpdateGoogleCalendarIntegrationDto.prototype, "calendar_email", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateGoogleCalendarIntegrationDto.prototype, "is_active", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateGoogleCalendarIntegrationDto.prototype, "sync_tasks", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateGoogleCalendarIntegrationDto.prototype, "sync_appointments", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Lembrete deve ser um número' }),
    (0, class_validator_1.Min)(0, { message: 'Lembrete deve ser no mínimo 0 minutos' }),
    (0, class_validator_1.Max)(10080, { message: 'Lembrete deve ser no máximo 7 dias (10080 minutos)' }),
    __metadata("design:type", Number)
], UpdateGoogleCalendarIntegrationDto.prototype, "default_reminder_minutes", void 0);
class GoogleCalendarIntegrationResponseDto {
    id;
    calendar_email;
    is_active;
    sync_tasks;
    sync_appointments;
    default_reminder_minutes;
    created_at;
    updated_at;
}
exports.GoogleCalendarIntegrationResponseDto = GoogleCalendarIntegrationResponseDto;
//# sourceMappingURL=google-calendar.dto.js.map