import { GoogleCalendarService } from './google-calendar.service';
import { UpdateGoogleCalendarIntegrationDto, GoogleCalendarIntegrationResponseDto } from './dto/google-calendar.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';
export declare class GoogleCalendarController {
    private readonly googleCalendarService;
    constructor(googleCalendarService: GoogleCalendarService);
    getIntegration(req: any): Promise<GoogleCalendarIntegrationResponseDto | null>;
    updateIntegration(req: any, updateDto: UpdateGoogleCalendarIntegrationDto): Promise<GoogleCalendarIntegrationResponseDto>;
    deleteIntegration(req: any): Promise<SuccessResponseDto>;
}
