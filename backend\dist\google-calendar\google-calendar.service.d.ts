import { Connection } from 'mysql2/promise';
import { UpdateGoogleCalendarIntegrationDto, GoogleCalendarIntegrationResponseDto } from './dto/google-calendar.dto';
export declare class GoogleCalendarService {
    private connection;
    constructor(connection: Connection);
    getIntegration(userId: number): Promise<GoogleCalendarIntegrationResponseDto | null>;
    updateIntegration(userId: number, updateDto: UpdateGoogleCalendarIntegrationDto): Promise<GoogleCalendarIntegrationResponseDto>;
    private getIntegrationById;
    deleteIntegration(userId: number): Promise<void>;
}
