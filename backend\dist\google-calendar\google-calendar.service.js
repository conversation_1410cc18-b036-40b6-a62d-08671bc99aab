"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleCalendarService = void 0;
const common_1 = require("@nestjs/common");
const database_provider_1 = require("../database/database.provider");
let GoogleCalendarService = class GoogleCalendarService {
    connection;
    constructor(connection) {
        this.connection = connection;
    }
    async getIntegration(userId) {
        const [rows] = await this.connection.execute(`SELECT id, calendar_email, is_active, sync_tasks, sync_appointments,
              default_reminder_minutes, created_at, updated_at
       FROM google_calendar_integration 
       WHERE user_id = ?`, [userId]);
        if (!Array.isArray(rows) || rows.length === 0) {
            return null;
        }
        const integration = rows[0];
        return {
            id: integration.id,
            calendar_email: integration.calendar_email,
            is_active: integration.is_active === 1,
            sync_tasks: integration.sync_tasks === 1,
            sync_appointments: integration.sync_appointments === 1,
            default_reminder_minutes: integration.default_reminder_minutes,
            created_at: integration.created_at,
            updated_at: integration.updated_at,
        };
    }
    async updateIntegration(userId, updateDto) {
        const existing = await this.getIntegration(userId);
        if (!existing) {
            const [result] = await this.connection.execute(`INSERT INTO google_calendar_integration 
         (user_id, calendar_email, is_active, sync_tasks, sync_appointments, default_reminder_minutes)
         VALUES (?, ?, ?, ?, ?, ?)`, [
                userId,
                updateDto.calendar_email,
                updateDto.is_active !== false ? 1 : 0,
                updateDto.sync_tasks !== false ? 1 : 0,
                updateDto.sync_appointments !== false ? 1 : 0,
                updateDto.default_reminder_minutes || 15,
            ]);
            const insertId = result.insertId;
            return this.getIntegrationById(insertId);
        }
        else {
            const updateFields = [];
            const values = [];
            updateFields.push('calendar_email = ?');
            values.push(updateDto.calendar_email);
            if (typeof updateDto.is_active === 'boolean') {
                updateFields.push('is_active = ?');
                values.push(updateDto.is_active ? 1 : 0);
            }
            if (typeof updateDto.sync_tasks === 'boolean') {
                updateFields.push('sync_tasks = ?');
                values.push(updateDto.sync_tasks ? 1 : 0);
            }
            if (typeof updateDto.sync_appointments === 'boolean') {
                updateFields.push('sync_appointments = ?');
                values.push(updateDto.sync_appointments ? 1 : 0);
            }
            if (updateDto.default_reminder_minutes !== undefined) {
                updateFields.push('default_reminder_minutes = ?');
                values.push(updateDto.default_reminder_minutes);
            }
            updateFields.push('updated_at = CURRENT_TIMESTAMP');
            values.push(userId);
            await this.connection.execute(`UPDATE google_calendar_integration 
         SET ${updateFields.join(', ')} 
         WHERE user_id = ?`, values);
            const updatedIntegration = await this.getIntegration(userId);
            return updatedIntegration;
        }
    }
    async getIntegrationById(id) {
        const [rows] = await this.connection.execute(`SELECT id, calendar_email, is_active, sync_tasks, sync_appointments,
              default_reminder_minutes, created_at, updated_at
       FROM google_calendar_integration 
       WHERE id = ?`, [id]);
        if (!Array.isArray(rows) || rows.length === 0) {
            throw new common_1.NotFoundException('Integração não encontrada');
        }
        const integration = rows[0];
        return {
            id: integration.id,
            calendar_email: integration.calendar_email,
            is_active: integration.is_active === 1,
            sync_tasks: integration.sync_tasks === 1,
            sync_appointments: integration.sync_appointments === 1,
            default_reminder_minutes: integration.default_reminder_minutes,
            created_at: integration.created_at,
            updated_at: integration.updated_at,
        };
    }
    async deleteIntegration(userId) {
        const [result] = await this.connection.execute('DELETE FROM google_calendar_integration WHERE user_id = ?', [userId]);
        if (result.affectedRows === 0) {
            throw new common_1.NotFoundException('Integração não encontrada');
        }
    }
};
exports.GoogleCalendarService = GoogleCalendarService;
exports.GoogleCalendarService = GoogleCalendarService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.MYSQL2_CONNECTION)),
    __metadata("design:paramtypes", [Object])
], GoogleCalendarService);
//# sourceMappingURL=google-calendar.service.js.map