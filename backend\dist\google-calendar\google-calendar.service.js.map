{"version": 3, "file": "google-calendar.service.js", "sourceRoot": "", "sources": ["../../src/google-calendar/google-calendar.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AAMxB,qEAAkE;AAG3D,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IACe;IAA/C,YAA+C,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAEzE,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAC1C;;;yBAGmB,EACnB,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAQ,CAAC;QACnC,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,SAAS,EAAE,WAAW,CAAC,SAAS,KAAK,CAAC;YACtC,UAAU,EAAE,WAAW,CAAC,UAAU,KAAK,CAAC;YACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB,KAAK,CAAC;YACtD,wBAAwB,EAAE,WAAW,CAAC,wBAAwB;YAC9D,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,UAAU,EAAE,WAAW,CAAC,UAAU;SACnC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAAc,EACd,SAA6C;QAG7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEd,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAC5C;;mCAE2B,EAC3B;gBACE,MAAM;gBACN,SAAS,CAAC,cAAc;gBACxB,SAAS,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,SAAS,CAAC,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,SAAS,CAAC,iBAAiB,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,SAAS,CAAC,wBAAwB,IAAI,EAAE;aACzC,CACF,CAAC;YAEF,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,CAAC;YAC1C,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YAEN,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,MAAM,MAAM,GAAU,EAAE,CAAC;YAEzB,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAEtC,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC7C,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC9C,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,OAAO,SAAS,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBACrD,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAC3C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,SAAS,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;gBACrD,YAAY,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAClD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAClD,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEpB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAC3B;eACO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;2BACX,EACnB,MAAM,CACP,CAAC;YAEF,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7D,OAAO,kBAAmB,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACzC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAC1C;;;oBAGc,EACd,CAAC,EAAE,CAAC,CACL,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAQ,CAAC;QACnC,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,SAAS,EAAE,WAAW,CAAC,SAAS,KAAK,CAAC;YACtC,UAAU,EAAE,WAAW,CAAC,UAAU,KAAK,CAAC;YACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB,KAAK,CAAC;YACtD,wBAAwB,EAAE,WAAW,CAAC,wBAAwB;YAC9D,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,UAAU,EAAE,WAAW,CAAC,UAAU;SACnC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAC5C,2DAA2D,EAC3D,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,IAAK,MAAc,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;CACF,CAAA;AArIY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAEE,WAAA,IAAA,eAAM,EAAC,qCAAiB,CAAC,CAAA;;GAD3B,qBAAqB,CAqIjC"}