import { DatabaseHealthService } from '../database/database-health.service';
export declare class HealthController {
    private readonly databaseHealthService;
    constructor(databaseHealthService: DatabaseHealthService);
    getHealthStatus(): Promise<{
        status: string;
        timestamp: string;
        services: {
            database: {
                status: string;
                lastCheck: Date | null;
                consecutiveFailures: number;
                connectionTest: {
                    success: boolean;
                    duration: string;
                    error: string | undefined;
                };
            };
        };
    }>;
    getDatabaseHealth(): Promise<{
        status: string;
        timestamp: string;
        lastHealthCheck: Date | null;
        consecutiveFailures: number;
        connectionTest: {
            success: boolean;
            duration: string;
            error: string | undefined;
        };
    }>;
}
