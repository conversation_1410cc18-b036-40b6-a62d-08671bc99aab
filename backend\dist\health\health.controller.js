"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const database_health_service_1 = require("../database/database-health.service");
let HealthController = class HealthController {
    databaseHealthService;
    constructor(databaseHealthService) {
        this.databaseHealthService = databaseHealthService;
    }
    async getHealthStatus() {
        const dbHealth = this.databaseHealthService.getHealthStatus();
        const connectionTest = await this.databaseHealthService.testConnection();
        const overallHealth = dbHealth.isHealthy && connectionTest.success;
        return {
            status: overallHealth ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            services: {
                database: {
                    status: dbHealth.isHealthy ? 'healthy' : 'unhealthy',
                    lastCheck: dbHealth.lastHealthCheck,
                    consecutiveFailures: dbHealth.consecutiveFailures,
                    connectionTest: {
                        success: connectionTest.success,
                        duration: `${connectionTest.duration}ms`,
                        error: connectionTest.error,
                    },
                },
            },
        };
    }
    async getDatabaseHealth() {
        const dbHealth = this.databaseHealthService.getHealthStatus();
        const connectionTest = await this.databaseHealthService.testConnection();
        return {
            status: dbHealth.isHealthy && connectionTest.success ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            lastHealthCheck: dbHealth.lastHealthCheck,
            consecutiveFailures: dbHealth.consecutiveFailures,
            connectionTest: {
                success: connectionTest.success,
                duration: `${connectionTest.duration}ms`,
                error: connectionTest.error,
            },
        };
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "getHealthStatus", null);
__decorate([
    (0, common_1.Get)('database'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "getDatabaseHealth", null);
exports.HealthController = HealthController = __decorate([
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [database_health_service_1.DatabaseHealthService])
], HealthController);
//# sourceMappingURL=health.controller.js.map