"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var IdeasService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdeasService = void 0;
const common_1 = require("@nestjs/common");
const idea_repository_1 = require("../repositories/idea.repository");
const idea_category_repository_1 = require("../repositories/idea-category.repository");
let IdeasService = IdeasService_1 = class IdeasService {
    ideaRepository;
    ideaCategoryRepository;
    logger = new common_1.Logger(IdeasService_1.name);
    constructor(ideaRepository, ideaCategoryRepository) {
        this.ideaRepository = ideaRepository;
        this.ideaCategoryRepository = ideaCategoryRepository;
    }
    async create(createIdeaDto, userId) {
        return this.ideaRepository.create(createIdeaDto, userId);
    }
    async findAll(userId, userTimezone, page = 1, limit = 50) {
        const result = await this.ideaRepository.findAllWithCategory(userId, userTimezone, page, limit);
        return {
            ideas: result.data,
            total: result.total,
            page: result.page,
            limit: result.limit
        };
    }
    async findOne(id, userId, userTimezone = 'America/Sao_Paulo') {
        return this.ideaRepository.findOne(id, userId, userTimezone);
    }
    async update(id, updateIdeaDto, userId, userTimezone = 'America/Sao_Paulo') {
        return this.ideaRepository.update(id, updateIdeaDto, userId, userTimezone);
    }
    async remove(id, userId) {
        return this.ideaRepository.remove(id, userId);
    }
    async toggleFavorite(id, userId, userTimezone = 'America/Sao_Paulo') {
        return this.ideaRepository.toggleFavorite(id, userId);
    }
    async createCategory(createCategoryDto, userId) {
        return this.ideaCategoryRepository.create(createCategoryDto, userId);
    }
    async findAllCategories(userId) {
        return this.ideaCategoryRepository.findAllOrderedByName(userId);
    }
    async findOneCategory(id, userId) {
        return this.ideaCategoryRepository.findOne(id, userId);
    }
    async updateCategory(id, updateCategoryDto, userId) {
        return this.ideaCategoryRepository.update(id, updateCategoryDto, userId);
    }
    async removeCategory(id, userId) {
        const isInUse = await this.ideaCategoryRepository.checkCategoryInUse(id, userId);
        if (isInUse) {
            throw new common_1.HttpException('Não é possível remover categoria que está sendo usada por ideias', common_1.HttpStatus.BAD_REQUEST);
        }
        return this.ideaCategoryRepository.remove(id, userId);
    }
};
exports.IdeasService = IdeasService;
exports.IdeasService = IdeasService = IdeasService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [idea_repository_1.IdeaRepository,
        idea_category_repository_1.IdeaCategoryRepository])
], IdeasService);
//# sourceMappingURL=ideas.service.js.map