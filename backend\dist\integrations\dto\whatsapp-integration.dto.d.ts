export declare class CreateWhatsAppIntegrationDto {
}
export declare class ValidateWhatsAppIntegrationDto {
    activation_code: string;
    phone: string;
}
export declare class UpdateWhatsAppIntegrationDto {
    status?: 'pending' | 'active' | 'inactive';
    phone?: string;
    is_validated?: boolean;
}
export declare class WhatsAppIntegrationResponseDto {
    id: number;
    status: 'pending' | 'active' | 'inactive';
    phone?: string | null;
    is_validated: boolean;
    activation_code?: string | null;
    user_id: number;
    created_at: Date;
    updated_at: Date;
}
