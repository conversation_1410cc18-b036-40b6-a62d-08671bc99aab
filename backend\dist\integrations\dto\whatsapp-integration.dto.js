"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppIntegrationResponseDto = exports.UpdateWhatsAppIntegrationDto = exports.ValidateWhatsAppIntegrationDto = exports.CreateWhatsAppIntegrationDto = void 0;
const class_validator_1 = require("class-validator");
class CreateWhatsAppIntegrationDto {
}
exports.CreateWhatsAppIntegrationDto = CreateWhatsAppIntegrationDto;
class ValidateWhatsAppIntegrationDto {
    activation_code;
    phone;
}
exports.ValidateWhatsAppIntegrationDto = ValidateWhatsAppIntegrationDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({
        message: 'Campo "activation_code" é obrigatório. Código de ativação é necessário para validar a integração.'
    }),
    (0, class_validator_1.IsString)({
        message: 'Campo "activation_code" deve ser uma string.'
    }),
    __metadata("design:type", String)
], ValidateWhatsAppIntegrationDto.prototype, "activation_code", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({
        message: 'Campo "phone" é obrigatório. Forneça um número de telefone válido para identificar o usuário.'
    }),
    (0, class_validator_1.IsString)({
        message: 'Campo "phone" deve ser uma string. Exemplo: "+5511999999999" ou "11999999999"'
    }),
    __metadata("design:type", String)
], ValidateWhatsAppIntegrationDto.prototype, "phone", void 0);
class UpdateWhatsAppIntegrationDto {
    status;
    phone;
    is_validated;
}
exports.UpdateWhatsAppIntegrationDto = UpdateWhatsAppIntegrationDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['pending', 'active', 'inactive']),
    __metadata("design:type", String)
], UpdateWhatsAppIntegrationDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateWhatsAppIntegrationDto.prototype, "phone", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateWhatsAppIntegrationDto.prototype, "is_validated", void 0);
class WhatsAppIntegrationResponseDto {
    id;
    status;
    phone;
    is_validated;
    activation_code;
    user_id;
    created_at;
    updated_at;
}
exports.WhatsAppIntegrationResponseDto = WhatsAppIntegrationResponseDto;
//# sourceMappingURL=whatsapp-integration.dto.js.map