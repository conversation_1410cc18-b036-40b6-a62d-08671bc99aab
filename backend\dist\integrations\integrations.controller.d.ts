import { IntegrationsService } from './integrations.service';
import { CreateWhatsAppIntegrationDto, UpdateWhatsAppIntegrationDto, ValidateWhatsAppIntegrationDto } from './dto/whatsapp-integration.dto';
export declare class IntegrationsController {
    private readonly integrationsService;
    constructor(integrationsService: IntegrationsService);
    create(req: any, createWhatsAppIntegrationDto: CreateWhatsAppIntegrationDto): Promise<import("./dto/whatsapp-integration.dto").WhatsAppIntegrationResponseDto>;
    findUserIntegration(req: any): Promise<import("./dto/whatsapp-integration.dto").WhatsAppIntegrationResponseDto | null>;
    findOne(req: any, id: number): Promise<import("./dto/whatsapp-integration.dto").WhatsAppIntegrationResponseDto>;
    update(req: any, id: number, updateWhatsAppIntegrationDto: UpdateWhatsAppIntegrationDto): Promise<import("./dto/whatsapp-integration.dto").WhatsAppIntegrationResponseDto>;
    validate(req: any, id: number): Promise<import("./dto/whatsapp-integration.dto").WhatsAppIntegrationResponseDto>;
    remove(req: any, id: number): Promise<void>;
    validateWithCode(req: any, validateDto: ValidateWhatsAppIntegrationDto): Promise<import("./dto/whatsapp-integration.dto").WhatsAppIntegrationResponseDto>;
    getWhatsAppUrl(req: any, id: number): Promise<{
        whatsapp_url: string;
        activation_code: string;
    }>;
}
