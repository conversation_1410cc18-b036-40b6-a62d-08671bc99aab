"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationsController = void 0;
const common_1 = require("@nestjs/common");
const dev_auth_guard_1 = require("../auth/dev-auth.guard");
const integrations_service_1 = require("./integrations.service");
const whatsapp_integration_dto_1 = require("./dto/whatsapp-integration.dto");
let IntegrationsController = class IntegrationsController {
    integrationsService;
    constructor(integrationsService) {
        this.integrationsService = integrationsService;
    }
    create(req, createWhatsAppIntegrationDto) {
        return this.integrationsService.createWhatsAppIntegration(createWhatsAppIntegrationDto, req.user.userId || req.user.id);
    }
    findUserIntegration(req) {
        return this.integrationsService.findWhatsAppIntegrationByUserId(req.user.userId || req.user.id);
    }
    findOne(req, id) {
        return this.integrationsService.findOneWhatsAppIntegration(id);
    }
    update(req, id, updateWhatsAppIntegrationDto) {
        return this.integrationsService.updateWhatsAppIntegration(id, updateWhatsAppIntegrationDto, req.user.userId || req.user.id);
    }
    validate(req, id) {
        return this.integrationsService.validateWhatsAppIntegration(id);
    }
    remove(req, id) {
        return this.integrationsService.removeWhatsAppIntegration(id, req.user.userId || req.user.id);
    }
    validateWithCode(req, validateDto) {
        return this.integrationsService.validateWithActivationCode(validateDto);
    }
    getWhatsAppUrl(req, id) {
        return this.integrationsService.findOneWhatsAppIntegration(id)
            .then(integration => {
            if (!integration.activation_code) {
                throw new Error('Integração já foi validada ou não possui código de ativação');
            }
            return {
                whatsapp_url: this.integrationsService.getWhatsAppUrl(integration.activation_code),
                activation_code: integration.activation_code
            };
        });
    }
};
exports.IntegrationsController = IntegrationsController;
__decorate([
    (0, common_1.Post)('whatsapp'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, whatsapp_integration_dto_1.CreateWhatsAppIntegrationDto]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('whatsapp'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "findUserIntegration", null);
__decorate([
    (0, common_1.Get)('whatsapp/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)('whatsapp/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, whatsapp_integration_dto_1.UpdateWhatsAppIntegrationDto]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "update", null);
__decorate([
    (0, common_1.Post)('whatsapp/:id/validate'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "validate", null);
__decorate([
    (0, common_1.Delete)('whatsapp/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('whatsapp/validate-code'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, whatsapp_integration_dto_1.ValidateWhatsAppIntegrationDto]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "validateWithCode", null);
__decorate([
    (0, common_1.Get)('whatsapp/:id/whatsapp-url'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], IntegrationsController.prototype, "getWhatsAppUrl", null);
exports.IntegrationsController = IntegrationsController = __decorate([
    (0, common_1.Controller)('integrations'),
    (0, common_1.UseGuards)(dev_auth_guard_1.DevAwareAuthGuard),
    __metadata("design:paramtypes", [integrations_service_1.IntegrationsService])
], IntegrationsController);
//# sourceMappingURL=integrations.controller.js.map