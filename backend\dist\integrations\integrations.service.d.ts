import { CreateWhatsAppIntegrationDto, UpdateWhatsAppIntegrationDto, WhatsAppIntegrationResponseDto, ValidateWhatsAppIntegrationDto } from './dto/whatsapp-integration.dto';
export declare class IntegrationsService {
    private readonly logger;
    private db;
    createWhatsAppIntegration(createWhatsAppIntegrationDto: CreateWhatsAppIntegrationDto, userId: number): Promise<WhatsAppIntegrationResponseDto>;
    findOneWhatsAppIntegration(id: number): Promise<WhatsAppIntegrationResponseDto>;
    findWhatsAppIntegrationByUserId(userId: number): Promise<WhatsAppIntegrationResponseDto | null>;
    updateWhatsAppIntegration(id: number, updateWhatsAppIntegrationDto: UpdateWhatsAppIntegrationDto, userId?: number): Promise<WhatsAppIntegrationResponseDto>;
    removeWhatsAppIntegration(id: number, userId?: number): Promise<void>;
    validateWhatsAppIntegration(id: number): Promise<WhatsAppIntegrationResponseDto>;
    validateWithActivationCode(validateDto: ValidateWhatsAppIntegrationDto): Promise<WhatsAppIntegrationResponseDto>;
    getWhatsAppUrl(activationCode: string): string;
    private generateActivationCode;
}
