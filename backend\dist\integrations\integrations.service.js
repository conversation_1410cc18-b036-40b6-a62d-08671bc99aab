"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var IntegrationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationsService = void 0;
const common_1 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const crypto = require("crypto");
let IntegrationsService = IntegrationsService_1 = class IntegrationsService {
    logger = new common_1.Logger(IntegrationsService_1.name);
    db = database_types_1.db;
    async createWhatsAppIntegration(createWhatsAppIntegrationDto, userId) {
        try {
            this.logger.log(`Criando integração WhatsApp para usuário ${userId}`);
            const existingIntegration = await this.db
                .selectFrom('integrations_whatsapp')
                .selectAll()
                .where('user_id', '=', userId)
                .where('status', 'in', ['pending', 'active'])
                .executeTakeFirst();
            if (existingIntegration) {
                throw new common_1.ConflictException('Usuário já possui uma integração WhatsApp ativa ou pendente');
            }
            const activationCode = this.generateActivationCode(userId);
            const insertResult = await this.db
                .insertInto('integrations_whatsapp')
                .values({
                phone: null,
                status: 'pending',
                is_validated: false,
                activation_code: activationCode,
                user_id: userId,
            })
                .executeTakeFirst();
            const integrationId = Number(insertResult.insertId);
            const newIntegration = await this.findOneWhatsAppIntegration(integrationId);
            this.logger.log(`Integração WhatsApp criada com sucesso - ID: ${integrationId}, Código: ${activationCode}`);
            return newIntegration;
        }
        catch (error) {
            this.logger.error(`Erro ao criar integração WhatsApp:`, error);
            throw error;
        }
    }
    async findOneWhatsAppIntegration(id) {
        try {
            const integration = await this.db
                .selectFrom('integrations_whatsapp')
                .selectAll()
                .where('id', '=', id)
                .executeTakeFirst();
            if (!integration) {
                throw new common_1.NotFoundException(`Integração WhatsApp com ID ${id} não encontrada`);
            }
            return {
                id: integration.id,
                status: integration.status,
                phone: integration.phone,
                is_validated: Boolean(integration.is_validated),
                activation_code: integration.activation_code,
                user_id: integration.user_id,
                created_at: new Date(),
                updated_at: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Erro ao buscar integração WhatsApp ${id}:`, error);
            throw error;
        }
    }
    async findWhatsAppIntegrationByUserId(userId) {
        try {
            const integration = await this.db
                .selectFrom('integrations_whatsapp')
                .selectAll()
                .where('user_id', '=', userId)
                .where('status', '!=', 'inactive')
                .executeTakeFirst();
            if (!integration) {
                return null;
            }
            return {
                id: integration.id,
                status: integration.status,
                phone: integration.phone,
                is_validated: Boolean(integration.is_validated),
                activation_code: integration.activation_code,
                user_id: integration.user_id,
                created_at: new Date(),
                updated_at: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Erro ao buscar integração WhatsApp do usuário ${userId}:`, error);
            throw error;
        }
    }
    async updateWhatsAppIntegration(id, updateWhatsAppIntegrationDto, userId) {
        try {
            this.logger.log(`Atualizando integração WhatsApp ${id}`);
            const existingIntegration = await this.findOneWhatsAppIntegration(id);
            if (userId && existingIntegration.user_id !== userId) {
                throw new common_1.NotFoundException('Integração não encontrada para este usuário');
            }
            if (updateWhatsAppIntegrationDto.phone) {
                const phoneRegex = /^\+?[\d\s\-\(\)]{10,20}$/;
                if (!phoneRegex.test(updateWhatsAppIntegrationDto.phone)) {
                    throw new Error('Formato de telefone inválido');
                }
            }
            await this.db
                .updateTable('integrations_whatsapp')
                .set(updateWhatsAppIntegrationDto)
                .where('id', '=', id)
                .execute();
            const updatedIntegration = await this.findOneWhatsAppIntegration(id);
            this.logger.log(`Integração WhatsApp ${id} atualizada com sucesso`);
            return updatedIntegration;
        }
        catch (error) {
            this.logger.error(`Erro ao atualizar integração WhatsApp ${id}:`, error);
            throw error;
        }
    }
    async removeWhatsAppIntegration(id, userId) {
        try {
            this.logger.log(`Removendo integração WhatsApp ${id}`);
            const existingIntegration = await this.findOneWhatsAppIntegration(id);
            if (userId && existingIntegration.user_id !== userId) {
                throw new common_1.NotFoundException('Integração não encontrada para este usuário');
            }
            await this.db
                .updateTable('integrations_whatsapp')
                .set({ status: 'inactive' })
                .where('id', '=', id)
                .execute();
            this.logger.log(`Integração WhatsApp ${id} removida com sucesso`);
        }
        catch (error) {
            this.logger.error(`Erro ao remover integração WhatsApp ${id}:`, error);
            throw error;
        }
    }
    async validateWhatsAppIntegration(id) {
        try {
            this.logger.log(`Validando integração WhatsApp ${id}`);
            const updatedIntegration = await this.updateWhatsAppIntegration(id, {
                status: 'active',
                is_validated: true,
            });
            this.logger.log(`Integração WhatsApp ${id} validada com sucesso`);
            return updatedIntegration;
        }
        catch (error) {
            this.logger.error(`Erro ao validar integração WhatsApp ${id}:`, error);
            throw error;
        }
    }
    async validateWithActivationCode(validateDto) {
        try {
            this.logger.log(`Validando integração com código: ${validateDto.activation_code}`);
            const integration = await this.db
                .selectFrom('integrations_whatsapp')
                .selectAll()
                .where('activation_code', '=', validateDto.activation_code)
                .where('status', '=', 'pending')
                .executeTakeFirst();
            if (!integration) {
                throw new common_1.NotFoundException('Código de ativação inválido ou expirado');
            }
            const phoneRegex = /^\+?[\d\s\-\(\)]{10,20}$/;
            if (!phoneRegex.test(validateDto.phone)) {
                throw new Error('Formato de telefone inválido');
            }
            await this.db
                .updateTable('integrations_whatsapp')
                .set({
                phone: validateDto.phone,
                status: 'active',
                is_validated: true,
                activation_code: null
            })
                .where('id', '=', integration.id)
                .execute();
            const updatedIntegration = await this.findOneWhatsAppIntegration(integration.id);
            this.logger.log(`Integração WhatsApp validada com sucesso para o número: ${validateDto.phone}`);
            return updatedIntegration;
        }
        catch (error) {
            this.logger.error(`Erro ao validar integração WhatsApp com código:`, error);
            throw error;
        }
    }
    getWhatsAppUrl(activationCode) {
        const whatsappNumber = process.env.WHATSAPP_DEFAULT_NUMBER || '5511999999999';
        const message = encodeURIComponent(activationCode);
        return `https://wa.me/${whatsappNumber}?text=${message}`;
    }
    generateActivationCode(userId = null) {
        const randomKey = crypto.randomBytes(4).toString('hex').toUpperCase();
        return `start-dupli-${userId}${randomKey}`;
    }
};
exports.IntegrationsService = IntegrationsService;
exports.IntegrationsService = IntegrationsService = IntegrationsService_1 = __decorate([
    (0, common_1.Injectable)()
], IntegrationsService);
//# sourceMappingURL=integrations.service.js.map