"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const cors_interceptor_1 = require("./common/cors.interceptor");
const response_interceptor_1 = require("./common/interceptors/response.interceptor");
const global_exception_filter_1 = require("./common/filters/global-exception.filter");
const global_validation_pipe_1 = require("./common/pipes/global-validation.pipe");
async function bootstrap() {
    if (process.env.MYSQL_VERBOSE_LOGS !== 'true') {
        process.env.DEBUG = '';
        process.env.NODE_DEBUG = '';
    }
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.use((req, res, next) => {
        if (req.method === 'OPTIONS') {
            res.header('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || 'http://localhost:5173');
            res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
            res.header('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-Device-UUID,X-Dev-Mode,X-Dev-User-Id');
            res.header('Access-Control-Allow-Credentials', 'true');
            res.header('Access-Control-Max-Age', '3600');
            return res.status(204).send();
        }
        next();
    });
    app.enableCors({
        origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
        methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
        allowedHeaders: [
            'Origin',
            'X-Requested-With',
            'Content-Type',
            'Accept',
            'Authorization',
            'X-Device-UUID',
            'X-Dev-Mode',
            'X-Dev-User-Id'
        ],
        preflightContinue: false,
        optionsSuccessStatus: 204,
        credentials: true
    });
    app.useGlobalPipes(new global_validation_pipe_1.GlobalValidationPipe());
    app.useGlobalFilters(new global_exception_filter_1.GlobalExceptionFilter());
    app.useGlobalInterceptors(new cors_interceptor_1.CorsInterceptor(), new response_interceptor_1.ResponseInterceptor());
    const port = process.env.PORT || 3000;
    await app.listen(port);
    console.log(`🚀 Backend rodando na porta ${port}`);
    console.log(`📡 CORS habilitado para: http://localhost:5173`);
}
bootstrap();
//# sourceMappingURL=main.js.map