export declare class PlanResponseDto {
    id: number;
    name: string;
    slug: string;
    description: string;
    price: number;
    currency: string;
    billing_period: string;
    features: any;
    is_active: boolean;
    sort_order: number;
    created_at: Date;
}
export declare class UserSubscriptionResponseDto {
    id: number;
    plan: PlanResponseDto;
    status: string;
    current_period_start?: Date;
    current_period_end?: Date;
    canceled_at?: Date;
    ends_at?: Date;
    created_at: Date;
}
export declare class CreateSubscriptionDto {
    plan_id: number;
    stripe_payment_method_id?: string;
}
