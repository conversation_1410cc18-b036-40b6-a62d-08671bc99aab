"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSubscriptionDto = exports.UserSubscriptionResponseDto = exports.PlanResponseDto = void 0;
const class_validator_1 = require("class-validator");
class PlanResponseDto {
    id;
    name;
    slug;
    description;
    price;
    currency;
    billing_period;
    features;
    is_active;
    sort_order;
    created_at;
}
exports.PlanResponseDto = PlanResponseDto;
class UserSubscriptionResponseDto {
    id;
    plan;
    status;
    current_period_start;
    current_period_end;
    canceled_at;
    ends_at;
    created_at;
}
exports.UserSubscriptionResponseDto = UserSubscriptionResponseDto;
class CreateSubscriptionDto {
    plan_id;
    stripe_payment_method_id;
}
exports.CreateSubscriptionDto = CreateSubscriptionDto;
__decorate([
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "plan_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "stripe_payment_method_id", void 0);
//# sourceMappingURL=plans.dto.js.map