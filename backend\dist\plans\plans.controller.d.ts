import { PlansService } from './plans.service';
import { PlanResponseDto, UserSubscriptionResponseDto, CreateSubscriptionDto } from './dto/plans.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';
export declare class PlansController {
    private readonly plansService;
    constructor(plansService: PlansService);
    getAllPlans(): Promise<PlanResponseDto[]>;
    getMySubscription(req: any): Promise<UserSubscriptionResponseDto | null>;
    subscribe(req: any, createSubscriptionDto: CreateSubscriptionDto): Promise<UserSubscriptionResponseDto>;
    cancelSubscription(req: any): Promise<SuccessResponseDto>;
}
