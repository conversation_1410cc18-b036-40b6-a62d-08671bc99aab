import { Connection } from 'mysql2/promise';
import { PlanResponseDto, UserSubscriptionResponseDto, CreateSubscriptionDto } from './dto/plans.dto';
export declare class PlansService {
    private connection;
    constructor(connection: Connection);
    getAllPlans(): Promise<PlanResponseDto[]>;
    getUserSubscription(userId: number): Promise<UserSubscriptionResponseDto | null>;
    createSubscription(userId: number, createSubscriptionDto: CreateSubscriptionDto): Promise<UserSubscriptionResponseDto>;
    cancelSubscription(userId: number): Promise<void>;
}
