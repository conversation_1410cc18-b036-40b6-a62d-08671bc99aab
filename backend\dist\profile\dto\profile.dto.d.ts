export declare class UpdatePersonalInfoDto {
    name: string;
    email: string;
    phone?: string;
    timezone?: string;
}
export declare class ChangePasswordDto {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}
export declare enum AiHumor {
    FORMAL = "formal",
    FRIENDLY = "friendly",
    CASUAL = "casual",
    PROFESSIONAL = "professional"
}
export declare enum ResponseSize {
    SHORT = "short",
    MEDIUM = "medium",
    LONG = "long",
    DETAILED = "detailed"
}
export declare class UpdateAssistantSettingsDto {
    ai_humor?: AiHumor;
    response_size?: ResponseSize;
    reminder_time?: string;
    reminder_interval?: string;
}
export declare class ProfileInfoResponseDto {
    id: number;
    name: string;
    email: string;
    phone?: string;
    timezone: string;
    is_admin: boolean;
    created_at: Date;
}
export declare class AssistantSettingsResponseDto {
    ai_humor: string;
    response_size: string;
    reminder_time: string;
    reminder_interval: string;
}
