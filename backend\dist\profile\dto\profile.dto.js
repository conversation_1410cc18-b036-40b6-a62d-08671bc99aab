"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssistantSettingsResponseDto = exports.ProfileInfoResponseDto = exports.UpdateAssistantSettingsDto = exports.ResponseSize = exports.AiHumor = exports.ChangePasswordDto = exports.UpdatePersonalInfoDto = void 0;
const class_validator_1 = require("class-validator");
class UpdatePersonalInfoDto {
    name;
    email;
    phone;
    timezone;
}
exports.UpdatePersonalInfoDto = UpdatePersonalInfoDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePersonalInfoDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], UpdatePersonalInfoDto.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePersonalInfoDto.prototype, "phone", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePersonalInfoDto.prototype, "timezone", void 0);
class ChangePasswordDto {
    currentPassword;
    newPassword;
    confirmPassword;
}
exports.ChangePasswordDto = ChangePasswordDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "currentPassword", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MinLength)(6, { message: 'A nova senha deve ter pelo menos 6 caracteres' }),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "newPassword", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "confirmPassword", void 0);
var AiHumor;
(function (AiHumor) {
    AiHumor["FORMAL"] = "formal";
    AiHumor["FRIENDLY"] = "friendly";
    AiHumor["CASUAL"] = "casual";
    AiHumor["PROFESSIONAL"] = "professional";
})(AiHumor || (exports.AiHumor = AiHumor = {}));
var ResponseSize;
(function (ResponseSize) {
    ResponseSize["SHORT"] = "short";
    ResponseSize["MEDIUM"] = "medium";
    ResponseSize["LONG"] = "long";
    ResponseSize["DETAILED"] = "detailed";
})(ResponseSize || (exports.ResponseSize = ResponseSize = {}));
class UpdateAssistantSettingsDto {
    ai_humor;
    response_size;
    reminder_time;
    reminder_interval;
}
exports.UpdateAssistantSettingsDto = UpdateAssistantSettingsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AiHumor),
    __metadata("design:type", String)
], UpdateAssistantSettingsDto.prototype, "ai_humor", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ResponseSize),
    __metadata("design:type", String)
], UpdateAssistantSettingsDto.prototype, "response_size", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAssistantSettingsDto.prototype, "reminder_time", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAssistantSettingsDto.prototype, "reminder_interval", void 0);
class ProfileInfoResponseDto {
    id;
    name;
    email;
    phone;
    timezone;
    is_admin;
    created_at;
}
exports.ProfileInfoResponseDto = ProfileInfoResponseDto;
class AssistantSettingsResponseDto {
    ai_humor;
    response_size;
    reminder_time;
    reminder_interval;
}
exports.AssistantSettingsResponseDto = AssistantSettingsResponseDto;
//# sourceMappingURL=profile.dto.js.map