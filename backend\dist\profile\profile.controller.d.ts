import { ProfileService } from './profile.service';
import { UpdatePersonalInfoDto, ChangePasswordDto, UpdateAssistantSettingsDto, ProfileInfoResponseDto, AssistantSettingsResponseDto } from './dto/profile.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';
export declare class ProfileController {
    private readonly profileService;
    constructor(profileService: ProfileService);
    getProfile(req: any): Promise<ProfileInfoResponseDto>;
    updatePersonalInfo(req: any, updatePersonalInfoDto: UpdatePersonalInfoDto): Promise<SuccessResponseDto>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<SuccessResponseDto>;
    getAssistantSettings(req: any): Promise<AssistantSettingsResponseDto>;
    updateAssistantSettings(req: any, updateAssistantSettingsDto: UpdateAssistantSettingsDto): Promise<SuccessResponseDto>;
}
