import { Connection } from 'mysql2/promise';
import { UpdatePersonalInfoDto, ChangePasswordDto, UpdateAssistantSettingsDto, ProfileInfoResponseDto, AssistantSettingsResponseDto } from './dto/profile.dto';
export declare class ProfileService {
    private connection;
    constructor(connection: Connection);
    getProfile(userId: number): Promise<ProfileInfoResponseDto>;
    updatePersonalInfo(userId: number, updateDto: UpdatePersonalInfoDto): Promise<void>;
    changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<void>;
    getAssistantSettings(userId: number): Promise<AssistantSettingsResponseDto>;
    updateAssistantSettings(userId: number, updateDto: UpdateAssistantSettingsDto): Promise<void>;
}
