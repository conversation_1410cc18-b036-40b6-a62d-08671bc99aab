"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileService = void 0;
const common_1 = require("@nestjs/common");
const bcrypt = require("bcrypt");
const database_provider_1 = require("../database/database.provider");
let ProfileService = class ProfileService {
    connection;
    constructor(connection) {
        this.connection = connection;
    }
    async getProfile(userId) {
        const [rows] = await this.connection.execute(`SELECT id, name, email, phone, timezone, is_admin, created_at 
       FROM users 
       WHERE id = ? AND deleted_at IS NULL`, [userId]);
        if (!Array.isArray(rows) || rows.length === 0) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        const user = rows[0];
        return {
            id: user.id,
            name: user.name || '',
            email: user.email || '',
            phone: user.phone || null,
            timezone: user.timezone || 'America/Sao_Paulo',
            is_admin: user.is_admin === 1,
            created_at: user.created_at,
        };
    }
    async updatePersonalInfo(userId, updateDto) {
        const [emailCheck] = await this.connection.execute('SELECT id FROM users WHERE email = ? AND id != ? AND deleted_at IS NULL', [updateDto.email, userId]);
        if (Array.isArray(emailCheck) && emailCheck.length > 0) {
            throw new common_1.BadRequestException('Este email já está sendo usado por outro usuário');
        }
        const [result] = await this.connection.execute(`UPDATE users 
       SET name = ?, email = ?, phone = ?, timezone = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ? AND deleted_at IS NULL`, [
            updateDto.name ?? null,
            updateDto.email ?? null,
            updateDto.phone ?? null,
            updateDto.timezone ?? null,
            userId
        ]);
        if (result.affectedRows === 0) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
    }
    async changePassword(userId, changePasswordDto) {
        const [rows] = await this.connection.execute('SELECT password FROM users WHERE id = ? AND deleted_at IS NULL', [userId]);
        if (!Array.isArray(rows) || rows.length === 0) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        const user = rows[0];
        const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.UnauthorizedException('Senha atual incorreta');
        }
        const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 10);
        await this.connection.execute('UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [hashedNewPassword, userId]);
    }
    async getAssistantSettings(userId) {
        try {
            const [rows] = await this.connection.execute(`SELECT ai_humor, response_size, reminder_time, reminder_interval 
         FROM user_assistant_settings 
         WHERE user_id = ?`, [userId]);
            if (!Array.isArray(rows) || rows.length === 0) {
                try {
                    await this.connection.execute(`INSERT INTO user_assistant_settings (user_id, ai_humor, response_size, reminder_time, reminder_interval) 
             VALUES (?, 'friendly', 'medium', '09:00:00', '30')`, [userId]);
                }
                catch (insertError) {
                    console.error('Error inserting default assistant settings:', insertError);
                }
                return {
                    ai_humor: 'friendly',
                    response_size: 'medium',
                    reminder_time: '09:00',
                    reminder_interval: '30',
                };
            }
            const settings = rows[0];
            return {
                ai_humor: settings.ai_humor || 'friendly',
                response_size: settings.response_size || 'medium',
                reminder_time: settings.reminder_time ? settings.reminder_time.slice(0, 5) : '09:00',
                reminder_interval: settings.reminder_interval || '30',
            };
        }
        catch (error) {
            console.error('Error getting assistant settings:', error);
            return {
                ai_humor: 'friendly',
                response_size: 'medium',
                reminder_time: '09:00',
                reminder_interval: '30',
            };
        }
    }
    async updateAssistantSettings(userId, updateDto) {
        try {
            const [existingRows] = await this.connection.execute('SELECT id FROM user_assistant_settings WHERE user_id = ?', [userId]);
            if (!Array.isArray(existingRows) || existingRows.length === 0) {
                const reminderTime = updateDto.reminder_time ? `${updateDto.reminder_time}:00` : '09:00:00';
                await this.connection.execute(`INSERT INTO user_assistant_settings (user_id, ai_humor, response_size, reminder_time, reminder_interval) 
           VALUES (?, ?, ?, ?, ?)`, [
                    userId,
                    updateDto.ai_humor || 'friendly',
                    updateDto.response_size || 'medium',
                    reminderTime,
                    updateDto.reminder_interval || '30'
                ]);
            }
            else {
                const updateFields = [];
                const updateValues = [];
                if (updateDto.ai_humor) {
                    updateFields.push('ai_humor = ?');
                    updateValues.push(updateDto.ai_humor);
                }
                if (updateDto.response_size) {
                    updateFields.push('response_size = ?');
                    updateValues.push(updateDto.response_size);
                }
                if (updateDto.reminder_time) {
                    updateFields.push('reminder_time = ?');
                    updateValues.push(`${updateDto.reminder_time}:00`);
                }
                if (updateDto.reminder_interval) {
                    updateFields.push('reminder_interval = ?');
                    updateValues.push(updateDto.reminder_interval);
                }
                if (updateFields.length > 0) {
                    updateFields.push('updated_at = CURRENT_TIMESTAMP');
                    updateValues.push(userId);
                    await this.connection.execute(`UPDATE user_assistant_settings SET ${updateFields.join(', ')} WHERE user_id = ?`, updateValues);
                }
            }
        }
        catch (error) {
            console.error('Error updating assistant settings:', error);
            throw new common_1.BadRequestException('Erro ao salvar configurações do assistente');
        }
    }
};
exports.ProfileService = ProfileService;
exports.ProfileService = ProfileService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.MYSQL2_CONNECTION)),
    __metadata("design:paramtypes", [Object])
], ProfileService);
//# sourceMappingURL=profile.service.js.map