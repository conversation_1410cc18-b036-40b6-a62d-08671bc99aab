import { Logger } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { IBaseRepository, FindAllOptions, PaginatedResult } from '../common/interfaces/base-repository.interface';
export declare abstract class BaseRepository<T, CreateDto, UpdateDto> implements IBaseRepository<T, CreateDto, UpdateDto> {
    protected readonly logger: Logger;
    protected readonly db: Kysely<Database>;
    constructor(db: Kysely<Database>, loggerContext: string);
    abstract get tableName(): keyof Database;
    abstract get entityName(): string;
    abstract mapToResponseDto(entity: any, userTimezone?: string): T;
    abstract prepareCreateData(dto: CreateDto, userId: number, userTimezone?: string): any;
    abstract prepareUpdateData(dto: UpdateDto, userTimezone?: string): any;
    create(data: CreateDto, userId: number, userTimezone?: string): Promise<T>;
    findAll(userId: number, options?: FindAllOptions): Promise<PaginatedResult<T>>;
    findOne(id: number, userId: number, userTimezone?: string): Promise<T>;
    update(id: number, data: UpdateDto, userId: number, userTimezone?: string): Promise<T>;
    remove(id: number, userId: number): Promise<void>;
}
