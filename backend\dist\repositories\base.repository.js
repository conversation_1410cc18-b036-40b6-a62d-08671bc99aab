"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRepository = void 0;
const common_1 = require("@nestjs/common");
const kysely_1 = require("kysely");
const database_provider_1 = require("../database/database.provider");
const error_utils_1 = require("../common/utils/error.utils");
let BaseRepository = class BaseRepository {
    logger;
    db;
    constructor(db, loggerContext) {
        this.db = db;
        this.logger = new common_1.Logger(loggerContext);
    }
    async create(data, userId, userTimezone = 'UTC') {
        try {
            const createData = this.prepareCreateData(data, userId, userTimezone);
            const result = await this.db
                .insertInto(this.tableName)
                .values(createData)
                .executeTakeFirst();
            if (!result.insertId) {
                throw new Error(`Falha ao criar ${this.entityName}`);
            }
            this.logger.log(`${this.entityName} criado com ID ${result.insertId} para usuário ${userId}`);
            return this.findOne(Number(result.insertId), userId, userTimezone);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, `criar ${this.entityName}`, userId);
        }
    }
    async findAll(userId, options = {}) {
        try {
            const { page = 1, limit = 50, orderBy = 'created_at', orderDirection = 'desc' } = options;
            const offset = (page - 1) * limit;
            let query = this.db
                .selectFrom(this.tableName)
                .selectAll()
                .where('user_id', '=', userId);
            if (options.filters) {
                Object.entries(options.filters).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        query = query.where(key, '=', value);
                    }
                });
            }
            query = query.orderBy(orderBy, orderDirection).limit(limit).offset(offset);
            const entities = await query.execute();
            const totalResult = await this.db
                .selectFrom(this.tableName)
                .select((eb) => eb.fn.count('id').as('count'))
                .where('user_id', '=', userId)
                .executeTakeFirst();
            const total = Number(totalResult?.count || 0);
            const data = entities.map((entity) => this.mapToResponseDto(entity));
            this.logger.debug(`Listados ${entities.length} ${this.entityName}s para usuário ${userId}`);
            return { data, total, page, limit };
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, `listar ${this.entityName}s`, userId);
        }
    }
    async findOne(id, userId, userTimezone = 'UTC') {
        try {
            const entity = await this.db
                .selectFrom(this.tableName)
                .selectAll()
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .executeTakeFirst();
            error_utils_1.ErrorUtils.validateOwnership(entity, userId, this.entityName);
            return this.mapToResponseDto(entity, userTimezone);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, `buscar ${this.entityName}`, userId);
        }
    }
    async update(id, data, userId, userTimezone = 'UTC') {
        try {
            await this.findOne(id, userId, userTimezone);
            const updateData = this.prepareUpdateData(data, userTimezone);
            await this.db
                .updateTable(this.tableName)
                .set(updateData)
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`${this.entityName} ${id} atualizado para usuário ${userId}`);
            return this.findOne(id, userId, userTimezone);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, `atualizar ${this.entityName}`, userId);
        }
    }
    async remove(id, userId) {
        try {
            await this.findOne(id, userId);
            await this.db
                .deleteFrom(this.tableName)
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`${this.entityName} ${id} removido para usuário ${userId}`);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, `remover ${this.entityName}`, userId);
        }
    }
};
exports.BaseRepository = BaseRepository;
exports.BaseRepository = BaseRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.DATABASE_CONNECTION)),
    __metadata("design:paramtypes", [kysely_1.Kysely, String])
], BaseRepository);
//# sourceMappingURL=base.repository.js.map