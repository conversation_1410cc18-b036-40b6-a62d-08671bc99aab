import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { BaseRepository } from './base.repository';
import { IFinanceCategoryRepository } from './interfaces/finance-category.repository.interface';
import { CreateFinanceCategoryDto } from '../finances/dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from '../finances/dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from '../finances/dto/finance-category-response.dto';
export declare class FinanceCategoryRepository extends BaseRepository<FinanceCategoryResponseDto, CreateFinanceCategoryDto, UpdateFinanceCategoryDto> implements IFinanceCategoryRepository {
    constructor(db: Kysely<Database>);
    get tableName(): keyof Database;
    get entityName(): string;
    mapToResponseDto(entity: any, userTimezone?: string): FinanceCategoryResponseDto;
    prepareCreateData(dto: CreateFinanceCategoryDto, userId: number): any;
    prepareUpdateData(dto: UpdateFinanceCategoryDto): any;
    checkCategoryInUse(id: number, userId: number): Promise<boolean>;
    findAllOrderedByType(userId: number, userTimezone?: string): Promise<FinanceCategoryResponseDto[]>;
}
