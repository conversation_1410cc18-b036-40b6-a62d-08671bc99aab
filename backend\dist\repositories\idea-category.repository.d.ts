import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { BaseRepository } from './base.repository';
import { IIdeaCategoryRepository } from './interfaces/idea-category.repository.interface';
import { CreateIdeaCategoryDto } from '../ideas/dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from '../ideas/dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from '../ideas/dto/idea-category-response.dto';
export declare class IdeaCategoryRepository extends BaseRepository<IdeaCategoryResponseDto, CreateIdeaCategoryDto, UpdateIdeaCategoryDto> implements IIdeaCategoryRepository {
    constructor(db: Kysely<Database>);
    get tableName(): keyof Database;
    get entityName(): string;
    mapToResponseDto(entity: any, userTimezone?: string): IdeaCategoryResponseDto;
    prepareCreateData(dto: CreateIdeaCategoryDto, userId: number): any;
    prepareUpdateData(dto: UpdateIdeaCategoryDto): any;
    checkCategoryInUse(id: number, userId: number): Promise<boolean>;
    findAllOrderedByName(userId: number, userTimezone?: string): Promise<IdeaCategoryResponseDto[]>;
}
