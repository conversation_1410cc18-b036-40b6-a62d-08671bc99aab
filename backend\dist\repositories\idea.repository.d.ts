import { IIdeaRepository } from './interfaces/idea.repository.interface';
import { CreateIdeaDto } from '../ideas/dto/create-idea.dto';
import { UpdateIdeaDto } from '../ideas/dto/update-idea.dto';
import { IdeaResponseDto } from '../ideas/dto/idea-response.dto';
export declare class IdeaRepository implements IIdeaRepository {
    private readonly logger;
    private db;
    create(createIdeaDto: CreateIdeaDto, userId: number, userTimezone?: string): Promise<IdeaResponseDto>;
    findAll(userId: number, options?: any): Promise<any>;
    findOne(id: number, userId: number, userTimezone?: string): Promise<IdeaResponseDto>;
    update(id: number, updateIdeaDto: UpdateIdeaDto, userId: number, userTimezone?: string): Promise<IdeaResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    mapToResponseDto(entity: any, userTimezone?: string): IdeaResponseDto;
    prepareCreateData(dto: CreateIdeaDto, userId: number): any;
    prepareUpdateData(dto: UpdateIdeaDto): any;
    findAllWithCategory(userId: number, userTimezone: string, page?: number, limit?: number): Promise<{
        data: IdeaResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    toggleFavorite(id: number, userId: number): Promise<IdeaResponseDto>;
}
