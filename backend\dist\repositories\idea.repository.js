"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var IdeaRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdeaRepository = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const timezone_utils_1 = require("../common/utils/timezone.utils");
const error_utils_1 = require("../common/utils/error.utils");
let IdeaRepository = IdeaRepository_1 = class IdeaRepository {
    logger = new common_2.Logger(IdeaRepository_1.name);
    db = database_types_1.db;
    async create(createIdeaDto, userId, userTimezone = 'UTC') {
        const data = this.prepareCreateData(createIdeaDto, userId);
        const result = await this.db
            .insertInto('ideas')
            .values(data)
            .executeTakeFirstOrThrow();
        return this.findOne(Number(result.insertId), userId, userTimezone);
    }
    async findAll(userId, options) {
        return this.findAllWithCategory(userId, 'UTC', 1, 50);
    }
    async findOne(id, userId, userTimezone = 'UTC') {
        const idea = await this.db
            .selectFrom('ideas')
            .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
            .select([
            'ideas.id',
            'ideas.category_id',
            'ideas.name',
            'ideas.description',
            'ideas.content',
            'ideas.is_favorite',
            'ideas.user_id',
            'ideas.created_at',
            'ideas.updated_at',
            'ideas_categories.name as category_name'
        ])
            .where('ideas.id', '=', id)
            .where('ideas.user_id', '=', userId)
            .executeTakeFirst();
        if (!idea) {
            throw new Error(`Ideia com ID ${id} não encontrada`);
        }
        return this.mapToResponseDto(idea, userTimezone);
    }
    async update(id, updateIdeaDto, userId, userTimezone = 'UTC') {
        const data = this.prepareUpdateData(updateIdeaDto);
        await this.db
            .updateTable('ideas')
            .set(data)
            .where('id', '=', id)
            .where('user_id', '=', userId)
            .execute();
        return this.findOne(id, userId, userTimezone);
    }
    async remove(id, userId) {
        await this.db
            .deleteFrom('ideas')
            .where('id', '=', id)
            .where('user_id', '=', userId)
            .execute();
    }
    mapToResponseDto(entity, userTimezone = 'UTC') {
        return {
            id: entity.id,
            category_id: entity.category_id || undefined,
            category_name: entity.category_name || undefined,
            name: entity.name,
            description: entity.description || undefined,
            content: entity.content || undefined,
            is_favorite: entity.is_favorite || undefined,
            user_id: entity.user_id,
            created_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
            updated_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
        };
    }
    prepareCreateData(dto, userId) {
        return {
            ...dto,
            user_id: userId,
            created_at: new Date(),
            updated_at: new Date()
        };
    }
    prepareUpdateData(dto) {
        return {
            ...dto,
            updated_at: new Date()
        };
    }
    async findAllWithCategory(userId, userTimezone, page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            const ideas = await this.db
                .selectFrom('ideas')
                .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
                .select([
                'ideas.id',
                'ideas.category_id',
                'ideas.name',
                'ideas.description',
                'ideas.content',
                'ideas.is_favorite',
                'ideas.user_id',
                'ideas.created_at',
                'ideas.updated_at',
                'ideas_categories.name as category_name'
            ])
                .where('ideas.user_id', '=', userId)
                .orderBy('ideas.created_at', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();
            const total = await this.db
                .selectFrom('ideas')
                .select(this.db.fn.count('id').as('count'))
                .where('user_id', '=', userId)
                .executeTakeFirst();
            const data = ideas.map(idea => this.mapToResponseDto(idea, userTimezone));
            return {
                data,
                total: Number(total?.count || 0),
                page,
                limit
            };
        }
        catch (error) {
            this.logger.error(`Erro na consulta de ideias: ${error.message}`);
            throw error;
        }
    }
    async toggleFavorite(id, userId) {
        try {
            const idea = await this.findOne(id, userId);
            const newFavoriteStatus = !idea.is_favorite;
            await this.db
                .updateTable('ideas')
                .set({
                is_favorite: newFavoriteStatus,
                updated_at: new Date()
            })
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Ideia ${id} ${newFavoriteStatus ? 'marcada como favorita' : 'desmarcada como favorita'} para usuário ${userId}`);
            return this.findOne(id, userId);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'alterar status de favorito da ideia', userId);
        }
    }
};
exports.IdeaRepository = IdeaRepository;
exports.IdeaRepository = IdeaRepository = IdeaRepository_1 = __decorate([
    (0, common_1.Injectable)()
], IdeaRepository);
//# sourceMappingURL=idea.repository.js.map