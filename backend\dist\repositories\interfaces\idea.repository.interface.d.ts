import { IBaseRepository } from '../../common/interfaces/base-repository.interface';
import { CreateIdeaDto } from '../../ideas/dto/create-idea.dto';
import { UpdateIdeaDto } from '../../ideas/dto/update-idea.dto';
import { IdeaResponseDto } from '../../ideas/dto/idea-response.dto';
export interface IIdeaRepository extends IBaseRepository<IdeaResponseDto, CreateIdeaDto, UpdateIdeaDto> {
    toggleFavorite(id: number, userId: number): Promise<IdeaResponseDto>;
}
