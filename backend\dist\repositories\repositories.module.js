"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoriesModule = void 0;
const common_1 = require("@nestjs/common");
const database_module_1 = require("../database.module");
const finance_repository_1 = require("./finance.repository");
const finance_category_repository_1 = require("./finance-category.repository");
const task_repository_1 = require("./task.repository");
const task_category_repository_1 = require("./task-category.repository");
const idea_repository_1 = require("./idea.repository");
const idea_category_repository_1 = require("./idea-category.repository");
let RepositoriesModule = class RepositoriesModule {
};
exports.RepositoriesModule = RepositoriesModule;
exports.RepositoriesModule = RepositoriesModule = __decorate([
    (0, common_1.Module)({
        imports: [database_module_1.DatabaseModule],
        providers: [
            finance_repository_1.FinanceRepository,
            finance_category_repository_1.FinanceCategoryRepository,
            task_repository_1.TaskRepository,
            task_category_repository_1.TaskCategoryRepository,
            idea_repository_1.IdeaRepository,
            idea_category_repository_1.IdeaCategoryRepository,
        ],
        exports: [
            finance_repository_1.FinanceRepository,
            finance_category_repository_1.FinanceCategoryRepository,
            task_repository_1.TaskRepository,
            task_category_repository_1.TaskCategoryRepository,
            idea_repository_1.IdeaRepository,
            idea_category_repository_1.IdeaCategoryRepository,
        ],
    })
], RepositoriesModule);
//# sourceMappingURL=repositories.module.js.map