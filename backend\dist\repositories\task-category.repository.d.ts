import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { BaseRepository } from './base.repository';
import { ITaskCategoryRepository } from './interfaces/task-category.repository.interface';
import { CreateTaskCategoryDto } from '../tasks/dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from '../tasks/dto/update-task-category.dto';
import { TaskCategoryResponseDto } from '../tasks/dto/task-category-response.dto';
export declare class TaskCategoryRepository extends BaseRepository<TaskCategoryResponseDto, CreateTaskCategoryDto, UpdateTaskCategoryDto> implements ITaskCategoryRepository {
    constructor(db: Kysely<Database>);
    get tableName(): keyof Database;
    get entityName(): string;
    mapToResponseDto(entity: any, userTimezone?: string): TaskCategoryResponseDto;
    prepareCreateData(dto: CreateTaskCategoryDto, userId: number): any;
    prepareUpdateData(dto: UpdateTaskCategoryDto): any;
    checkCategoryInUse(id: number, userId: number): Promise<boolean>;
    findAllOrderedByName(userId: number, userTimezone?: string): Promise<TaskCategoryResponseDto[]>;
}
