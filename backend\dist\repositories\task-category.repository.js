"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskCategoryRepository = void 0;
const common_1 = require("@nestjs/common");
const kysely_1 = require("kysely");
const database_provider_1 = require("../database/database.provider");
const base_repository_1 = require("./base.repository");
const timezone_utils_1 = require("../common/utils/timezone.utils");
const error_utils_1 = require("../common/utils/error.utils");
let TaskCategoryRepository = class TaskCategoryRepository extends base_repository_1.BaseRepository {
    constructor(db) {
        super(db, 'TaskCategoryRepository');
    }
    get tableName() {
        return 'tasks_categories';
    }
    get entityName() {
        return 'categoria de tarefa';
    }
    mapToResponseDto(entity, userTimezone = 'UTC') {
        return {
            id: entity.id,
            name: entity.name,
            user_id: entity.user_id,
            created_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
            updated_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
        };
    }
    prepareCreateData(dto, userId) {
        return {
            ...dto,
            user_id: userId,
            created_at: new Date(),
            updated_at: new Date()
        };
    }
    prepareUpdateData(dto) {
        return {
            ...dto,
            updated_at: new Date()
        };
    }
    async checkCategoryInUse(id, userId) {
        try {
            const tasksUsingCategory = await this.db
                .selectFrom('tasks')
                .select(['id'])
                .where('category_id', '=', id)
                .where('user_id', '=', userId)
                .limit(1)
                .execute();
            return tasksUsingCategory.length > 0;
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'verificar uso da categoria', userId);
        }
    }
    async findAllOrderedByName(userId, userTimezone = 'UTC') {
        try {
            const categories = await this.db
                .selectFrom('tasks_categories')
                .selectAll()
                .where('user_id', '=', userId)
                .orderBy('name', 'asc')
                .execute();
            return categories.map(category => this.mapToResponseDto(category, userTimezone));
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'listar categorias ordenadas', userId);
        }
    }
};
exports.TaskCategoryRepository = TaskCategoryRepository;
exports.TaskCategoryRepository = TaskCategoryRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(database_provider_1.DATABASE_CONNECTION)),
    __metadata("design:paramtypes", [kysely_1.Kysely])
], TaskCategoryRepository);
//# sourceMappingURL=task-category.repository.js.map