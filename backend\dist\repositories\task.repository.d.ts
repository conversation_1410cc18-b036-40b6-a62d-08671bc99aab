import { ITaskRepository, TaskQueryResult } from './interfaces/task.repository.interface';
import { CreateTaskDto } from '../tasks/dto/create-task.dto';
import { UpdateTaskDto } from '../tasks/dto/update-task.dto';
import { TaskResponseDto } from '../tasks/dto/task-response.dto';
export declare class TaskRepository implements ITaskRepository {
    private readonly logger;
    private db;
    create(createTaskDto: CreateTaskDto, userId: number, userTimezone?: string): Promise<TaskResponseDto>;
    findOne(id: number, userId: number, userTimezone?: string): Promise<TaskResponseDto>;
    update(id: number, updateTaskDto: UpdateTaskDto, userId: number, userTimezone?: string): Promise<TaskResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    findAll(userId: number, options?: any): Promise<any>;
    mapToResponseDto(entity: any, userTimezone?: string): TaskResponseDto;
    prepareCreateData(dto: CreateTaskDto, userId: number, userTimezone?: string): any;
    prepareUpdateData(dto: UpdateTaskDto, userTimezone?: string): any;
    findAllWithCategory(userId: number, userTimezone: string, page?: number, limit?: number, startDate?: string, endDate?: string): Promise<TaskQueryResult>;
    private executeTaskQuery;
    complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto>;
}
