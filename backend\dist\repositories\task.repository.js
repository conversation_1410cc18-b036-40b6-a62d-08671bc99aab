"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var TaskRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskRepository = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const timezone_utils_1 = require("../common/utils/timezone.utils");
const error_utils_1 = require("../common/utils/error.utils");
let TaskRepository = TaskRepository_1 = class TaskRepository {
    logger = new common_2.Logger(TaskRepository_1.name);
    db = database_types_1.db;
    async create(createTaskDto, userId, userTimezone = 'UTC') {
        const data = this.prepareCreateData(createTaskDto, userId, userTimezone);
        const result = await this.db
            .insertInto('tasks')
            .values(data)
            .executeTakeFirstOrThrow();
        return this.findOne(Number(result.insertId), userId, userTimezone);
    }
    async findOne(id, userId, userTimezone = 'UTC') {
        const task = await this.db
            .selectFrom('tasks')
            .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
            .select([
            'tasks.id',
            'tasks.name',
            'tasks.description',
            'tasks.task_type',
            'tasks.task_date',
            'tasks.completed_at',
            'tasks.created_at',
            'tasks.updated_at',
            'tasks.category_id',
            'tasks.user_id',
            'tasks_categories.name as category_name'
        ])
            .where('tasks.id', '=', id)
            .where('tasks.user_id', '=', userId)
            .executeTakeFirst();
        if (!task) {
            throw new Error(`Tarefa com ID ${id} não encontrada`);
        }
        return this.mapToResponseDto(task, userTimezone);
    }
    async update(id, updateTaskDto, userId, userTimezone = 'UTC') {
        const data = this.prepareUpdateData(updateTaskDto, userTimezone);
        await this.db
            .updateTable('tasks')
            .set(data)
            .where('id', '=', id)
            .where('user_id', '=', userId)
            .execute();
        return this.findOne(id, userId, userTimezone);
    }
    async remove(id, userId) {
        await this.db
            .deleteFrom('tasks')
            .where('id', '=', id)
            .where('user_id', '=', userId)
            .execute();
    }
    async findAll(userId, options) {
        return this.findAllWithCategory(userId, 'UTC', 1, 50);
    }
    mapToResponseDto(entity, userTimezone = 'UTC') {
        return {
            id: entity.id,
            task_type: entity.task_type,
            category_id: entity.category_id || undefined,
            category_name: entity.category_name || undefined,
            name: entity.name,
            description: entity.description || undefined,
            task_date: entity.task_date ? timezone_utils_1.TimezoneUtils.toUserTimezone(entity.task_date, userTimezone) : undefined,
            user_id: entity.user_id,
            completed_at: entity.completed_at ? timezone_utils_1.TimezoneUtils.toUserTimezone(entity.completed_at, userTimezone) : undefined,
            created_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
            updated_at: timezone_utils_1.TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
        };
    }
    prepareCreateData(dto, userId, userTimezone = 'UTC') {
        return {
            ...dto,
            user_id: userId,
            task_date: timezone_utils_1.TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone),
            created_at: new Date(),
            updated_at: new Date()
        };
    }
    prepareUpdateData(dto, userTimezone = 'UTC') {
        const updateData = {
            ...dto,
            task_date: dto.task_date ?
                timezone_utils_1.TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone) :
                undefined,
            updated_at: new Date()
        };
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === undefined) {
                delete updateData[key];
            }
        });
        return updateData;
    }
    async findAllWithCategory(userId, userTimezone, page = 1, limit = 50, startDate, endDate) {
        try {
            this.logger.debug(`Iniciando consulta de tarefas para usuário ${userId}`);
            const result = await this.executeTaskQuery(userId, page, limit, startDate, endDate);
            this.logger.debug('Consulta de tarefas bem-sucedida');
            return result;
        }
        catch (error) {
            this.logger.error(`Erro na consulta de tarefas: ${error.message}`);
            throw error;
        }
    }
    async executeTaskQuery(userId, page, limit, startDate, endDate) {
        const offset = (page - 1) * limit;
        let query = this.db
            .selectFrom('tasks')
            .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
            .select([
            'tasks.id',
            'tasks.name',
            'tasks.description',
            'tasks.task_type',
            'tasks.task_date',
            'tasks.completed_at',
            'tasks.created_at',
            'tasks.updated_at',
            'tasks.category_id',
            'tasks.user_id',
            'tasks_categories.name as category_name'
        ])
            .where('tasks.user_id', '=', userId);
        if (startDate) {
            query = query.where('tasks.task_date', '>=', new Date(startDate));
        }
        if (endDate) {
            query = query.where('tasks.task_date', '<=', new Date(endDate));
        }
        query = query
            .orderBy('tasks.created_at', 'desc')
            .limit(limit)
            .offset(offset);
        this.logger.debug('Executando consulta de tarefas...');
        const tasks = await query.execute();
        let countQuery = this.db
            .selectFrom('tasks')
            .select(this.db.fn.count('id').as('count'))
            .where('user_id', '=', userId);
        if (startDate) {
            countQuery = countQuery.where('task_date', '>=', new Date(startDate));
        }
        if (endDate) {
            countQuery = countQuery.where('task_date', '<=', new Date(endDate));
        }
        const total = await countQuery.executeTakeFirst();
        const data = tasks.map(task => this.mapToResponseDto(task, 'America/Sao_Paulo'));
        return {
            data,
            total: Number(total?.count || 0),
            page,
            limit
        };
    }
    async complete(id, userId, userTimezone) {
        try {
            const task = await this.findOne(id, userId, userTimezone);
            const isCompleted = !!task.completed_at;
            await this.db
                .updateTable('tasks')
                .set({
                completed_at: isCompleted ? null : new Date(),
                updated_at: new Date()
            })
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Tarefa ${id} ${isCompleted ? 'desmarcada como incompleta' : 'marcada como completa'} para usuário ${userId}`);
            return this.findOne(id, userId, userTimezone);
        }
        catch (error) {
            error_utils_1.ErrorUtils.handleServiceError(this.logger, error, 'alterar status da tarefa', userId);
        }
    }
};
exports.TaskRepository = TaskRepository;
exports.TaskRepository = TaskRepository = TaskRepository_1 = __decorate([
    (0, common_1.Injectable)()
], TaskRepository);
//# sourceMappingURL=task.repository.js.map