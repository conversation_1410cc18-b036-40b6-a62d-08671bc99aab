export declare class TaskResponseDto {
    id: number;
    task_type: 'appointment' | 'task';
    category_id?: number;
    category_name?: string;
    name: string;
    description?: string;
    task_date?: Date;
    user_id: number;
    completed_at?: Date;
    created_at: Date;
    updated_at: Date;
}
export declare class TaskListResponseDto {
    tasks: TaskResponseDto[];
    total: number;
    page: number;
    limit: number;
}
