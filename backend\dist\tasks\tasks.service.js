"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TasksService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksService = void 0;
const common_1 = require("@nestjs/common");
const task_repository_1 = require("../repositories/task.repository");
const task_category_repository_1 = require("../repositories/task-category.repository");
let TasksService = TasksService_1 = class TasksService {
    taskRepository;
    taskCategoryRepository;
    logger = new common_1.Logger(TasksService_1.name);
    constructor(taskRepository, taskCategoryRepository) {
        this.taskRepository = taskRepository;
        this.taskCategoryRepository = taskCategoryRepository;
    }
    async create(createTaskDto, userId, userTimezone) {
        return this.taskRepository.create(createTaskDto, userId, userTimezone);
    }
    async findAll(userId, userTimezone, page = 1, limit = 50, startDate, endDate) {
        const result = await this.taskRepository.findAllWithCategory(userId, userTimezone, page, limit, startDate, endDate);
        return {
            tasks: result.data,
            total: result.total,
            page: result.page,
            limit: result.limit
        };
    }
    async findOne(id, userId, userTimezone) {
        return this.taskRepository.findOne(id, userId, userTimezone);
    }
    async update(id, updateTaskDto, userId, userTimezone) {
        return this.taskRepository.update(id, updateTaskDto, userId, userTimezone);
    }
    async remove(id, userId) {
        return this.taskRepository.remove(id, userId);
    }
    async complete(id, userId, userTimezone) {
        return this.taskRepository.complete(id, userId, userTimezone);
    }
    async createCategory(createCategoryDto, userId) {
        return this.taskCategoryRepository.create(createCategoryDto, userId);
    }
    async findAllCategories(userId) {
        return this.taskCategoryRepository.findAllOrderedByName(userId);
    }
    async findOneCategory(id, userId) {
        return this.taskCategoryRepository.findOne(id, userId);
    }
    async updateCategory(id, updateCategoryDto, userId) {
        return this.taskCategoryRepository.update(id, updateCategoryDto, userId);
    }
    async removeCategory(id, userId) {
        const isInUse = await this.taskCategoryRepository.checkCategoryInUse(id, userId);
        if (isInUse) {
            throw new common_1.HttpException('Não é possível remover categoria que está sendo usada por tarefas', common_1.HttpStatus.BAD_REQUEST);
        }
        return this.taskCategoryRepository.remove(id, userId);
    }
};
exports.TasksService = TasksService;
exports.TasksService = TasksService = TasksService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [task_repository_1.TaskRepository,
        task_category_repository_1.TaskCategoryRepository])
], TasksService);
//# sourceMappingURL=tasks.service.js.map