"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toUserTimezone = toUserTimezone;
exports.fromUserTimezone = fromUserTimezone;
exports.formatForUser = formatForUser;
const date_fns_tz_1 = require("date-fns-tz");
function toUserTimezone(date, userTimezone) {
    return (0, date_fns_tz_1.toZonedTime)(date, userTimezone);
}
function fromUserTimezone(date, userTimezone) {
    return (0, date_fns_tz_1.fromZonedTime)(date, userTimezone);
}
function formatForUser(date, userTimezone, pattern = 'yyyy-MM-dd HH:mm:ss') {
    return (0, date_fns_tz_1.format)((0, date_fns_tz_1.toZonedTime)(date, userTimezone), pattern, { timeZone: userTimezone });
}
//# sourceMappingURL=timezone.js.map