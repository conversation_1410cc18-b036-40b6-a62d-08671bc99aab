import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseIntPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AdminGuard } from './guards/admin.guard';
import { AdminService } from './admin.service';
import {
  AdminUserListResponseDto,
  AdminDashboardStatsDto,
  UpdatePlanDto,
  UpdatePaymentSettingsDto,
  UpdateUserDto,
} from './dto/admin.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';

@Controller('admin')
@UseGuards(AuthGuard('jwt'), AdminGuard)
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  // Dashboard administrativo - estatísticas
  @Get('dashboard')
  @HttpCode(HttpStatus.OK)
  async getDashboardStats(): Promise<AdminDashboardStatsDto> {
    return this.adminService.getDashboardStats();
  }

  // Listar usuários
  @Get('users')
  @HttpCode(HttpStatus.OK)
  async getUsers(
    @Query('page') page = 1,
    @Query('limit') limit = 20,
  ): Promise<{
    users: AdminUserListResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    return this.adminService.getUsers(page, limit);
  }

  // Atualizar usuário
  @Put('users/:id')
  @HttpCode(HttpStatus.OK)
  async updateUser(
    @Param('id', ParseIntPipe) userId: number,
    @Body() updateDto: UpdateUserDto,
  ): Promise<SuccessResponseDto> {
    await this.adminService.updateUser(userId, updateDto);
    return {
      success: true,
      message: 'Usuário atualizado com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

  // Listar planos (para admin)
  @Get('plans')
  @HttpCode(HttpStatus.OK)
  async getPlans() {
    return this.adminService.getPlans();
  }

  // Atualizar plano
  @Put('plans/:id')
  @HttpCode(HttpStatus.OK)
  async updatePlan(
    @Param('id', ParseIntPipe) planId: number,
    @Body() updateDto: UpdatePlanDto,
  ): Promise<SuccessResponseDto> {
    await this.adminService.updatePlan(planId, updateDto);
    return {
      success: true,
      message: 'Plano atualizado com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

  // Configurações de pagamento
  @Get('payment-settings')
  @HttpCode(HttpStatus.OK)
  async getPaymentSettings() {
    return this.adminService.getPaymentSettings();
  }

  // Atualizar configurações de pagamento
  @Put('payment-settings')
  @HttpCode(HttpStatus.OK)
  async updatePaymentSettings(
    @Body() updateDto: UpdatePaymentSettingsDto,
  ): Promise<SuccessResponseDto> {
    await this.adminService.updatePaymentSettings(updateDto);
    return {
      success: true,
      message: 'Configurações de pagamento atualizadas com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

  // Obter configurações do Stripe
  @Get('stripe-config')
  @HttpCode(HttpStatus.OK)
  async getStripeConfig() {
    return this.adminService.getStripeConfig();
  }

  // Salvar configurações do Stripe
  @Post('stripe-config')
  @HttpCode(HttpStatus.OK)
  async saveStripeConfig(
    @Body() config: {
      public_key: string;
      secret_key: string;
      webhook_endpoint_secret?: string;
      test_mode: boolean;
    },
  ): Promise<SuccessResponseDto> {
    await this.adminService.saveStripeConfig(config);
    return {
      success: true,
      message: 'Configurações do Stripe salvas com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

  // Testar conexão com Stripe
  @Post('stripe-config/test')
  @HttpCode(HttpStatus.OK)
  async testStripeConnection(
    @Body() config: {
      secret_key: string;
      test_mode: boolean;
    },
  ): Promise<{ success: true; message: string }> {
    const result = await this.adminService.testStripeConnection(config);
    return {
      success: true,
      message: result.message,
    };
  }
}
