import { IsOptional, IsString, IsN<PERSON>ber, IsBoolean, IsEnum, IsDecimal, IsEmail } from 'class-validator';

// DTO para listar usuários com paginação
export class AdminUserListResponseDto {
  id: number;
  name: string;
  email: string;
  phone?: string;
  timezone: string;
  is_admin: boolean;
  created_at: Date;
  subscription?: {
    id: number;
    plan_name: string;
    status: string;
    current_period_end?: Date;
  } | null;
}

// DTO para estatísticas gerais
export class AdminDashboardStatsDto {
  total_users: number;
  active_subscriptions: number;
  total_revenue: number;
  monthly_revenue: number;
  cancelled_subscriptions: number;
}

// DTO para atualizar plano
export class UpdatePlanDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  price?: string;

  @IsOptional()
  @IsString()
  stripe_price_id?: string;

  @IsOptional()
  features?: any;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsNumber()
  sort_order?: number;
}

// DTO para configurações de pagamento
export class UpdatePaymentSettingsDto {
  @IsOptional()
  @IsString()
  stripe_public_key?: string;

  @IsOptional()
  @IsString()
  stripe_secret_key?: string;

  @IsOptional()
  @IsString()
  stripe_webhook_secret?: string;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}

// DTO para atualizar usuário
export class UpdateUserDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsBoolean()
  is_admin?: boolean;
}
