import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Inject,
} from '@nestjs/common';
import { Connection } from 'mysql2/promise';
import { MYSQL2_CONNECTION } from '../../database/database.provider';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(@Inject(MYSQL2_CONNECTION) private connection: Connection) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // O JWT strategy retorna { userId, email }, não { sub, email }
    const userId = user?.userId || user?.sub;

    if (!user || !userId) {
      throw new ForbiddenException('Usuário não autenticado');
    }

    // Verificar se o usuário é admin
    const [rows] = await this.connection.execute(
      'SELECT is_admin FROM users WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      throw new ForbiddenException('Usuário não encontrado');
    }

    const userData = rows[0] as any;
    if (!userData.is_admin) {
      throw new ForbiddenException('Acesso negado. Apenas administradores podem acessar esta área');
    }

    return true;
  }
}
