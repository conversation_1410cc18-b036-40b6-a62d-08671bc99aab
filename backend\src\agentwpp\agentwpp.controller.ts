import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query, 
  UseGuards, 
  UseInterceptors,
  ParseIntPipe,
  Logger,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { AgentWppService } from './agentwpp.service';
import { ApiKeyGuard } from './guards/api-key.guard';
import { 
  CheckIntegrationResponseDto, 
  AgentWppDashboardResponseDto,
  CreateTaskAgentWppDto,
  UpdateTaskAgentWppDto,
  CreateTaskCategoryAgentWppDto,
  CreateFinanceAgentWppDto,
  UpdateFinanceAgentWppDto,
  CreateFinanceCategoryAgentWppDto,
  CreateIdeaAgentWppDto,
  UpdateIdeaAgentWppDto,
  CreateIdeaCategoryAgentWppDto,
  AgentWppPaginationDto
} from './dto/agentwpp.dto';
import { SuccessResponseDto, PaginatedResponseDto } from '../common/dto/common-response.dto';
import { ValidateWhatsAppIntegrationDto } from '../integrations/dto/whatsapp-integration.dto';
import { ValidationErrorInterceptor } from '../common/interceptors/validation-error.interceptor';
import { PhoneParam } from './decorators/phone-param.decorator';

@Controller('agentwpp')
@UseGuards(ApiKeyGuard)
@UseInterceptors(ValidationErrorInterceptor)
export class AgentWppController {
  private readonly logger = new Logger(AgentWppController.name);

  constructor(private readonly agentWppService: AgentWppService) {}

  // ===== VERIFICAÇÃO DE INTEGRAÇÃO =====
  @Get('check-integration/:phone')
  @HttpCode(HttpStatus.OK)
  async checkIntegration(@PhoneParam('phone') phone: string): Promise<CheckIntegrationResponseDto> {
    this.logger.log(`[CHECK_INTEGRATION] Verificando integração para telefone: ${phone}`);
    const startTime = Date.now();
    
    try {
      const result = await this.agentWppService.checkIntegration(phone);
      const duration = Date.now() - startTime;
      this.logger.log(`[CHECK_INTEGRATION] Sucesso em ${duration}ms para telefone: ${phone}`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`[CHECK_INTEGRATION] Erro em ${duration}ms para telefone: ${phone}`, error);
      throw error;
    }
  }
  
  // ===== VALIDAÇÃO DE INTEGRAÇÃO =====
  @Post('validate')
  @HttpCode(HttpStatus.OK)
  async validateNumber(@Body() validateDto: ValidateWhatsAppIntegrationDto) {
    this.logger.log(`[VALIDATE] Validando número com código de ativação: ${validateDto.activation_code}`);
    const startTime = Date.now();
    
    try {
      const result = await this.agentWppService.validateNumber(validateDto);
      const duration = Date.now() - startTime;
      this.logger.log(`[VALIDATE] Sucesso em ${duration}ms para telefone: ${validateDto.phone}`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`[VALIDATE] Erro em ${duration}ms para telefone: ${validateDto.phone}`, error);
      throw error;
    }
  }

  // ===== DASHBOARD =====
  @Get('dashboard/:phone')
  @HttpCode(HttpStatus.OK)
  async getDashboard(@PhoneParam('phone') phone: string): Promise<AgentWppDashboardResponseDto> {
    this.logger.log(`[DASHBOARD] Buscando dashboard para telefone: ${phone}`);
    const startTime = Date.now();
    
    try {
      const result = await this.agentWppService.getDashboard(phone);
      const duration = Date.now() - startTime;
      this.logger.log(`[DASHBOARD] Sucesso em ${duration}ms para telefone: ${phone}`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`[DASHBOARD] Erro em ${duration}ms para telefone: ${phone}`, error);
      throw error;
    }
  }

  // ===== TAREFAS =====
  @Post('tasks')
  async createTask(@Body() createTaskDto: CreateTaskAgentWppDto) {
    this.logger.log(`Criando tarefa para telefone: ${createTaskDto.phone}`);
    return this.agentWppService.createTask(createTaskDto);
  }

  @Get('tasks/:phone')
  async findAllTasks(
    @PhoneParam('phone') phone: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    this.logger.log(`Buscando tarefas para telefone: ${phone}`);
    return this.agentWppService.findAllTasks(phone, page, limit);
  }

  @Get('tasks/:phone/categories')
  async findAllTaskCategories(@PhoneParam('phone') phone: string) {
    this.logger.log(`Buscando categorias de tarefas para telefone: ${phone}`);
    return this.agentWppService.findAllTaskCategories(phone);
  }

  @Get('tasks/:phone/recent')
  async getRecentTasks(
    @PhoneParam('phone') phone: string,
    @Query('limit') limit?: number,
    @Query('days') days?: number
  ) {
    this.logger.log(`Buscando tarefas recentes para telefone: ${phone}`);
    return this.agentWppService.getRecentTasks(phone, limit, days);
  }

  @Post('tasks/quick')
  async createQuickTask(@Body() createTaskDto: { phone: string; name: string; description?: string }) {
    this.logger.log(`Criando tarefa rápida para telefone: ${createTaskDto.phone}`);
    return this.agentWppService.createQuickTask(createTaskDto);
  }

  @Get('tasks/:phone/:id')
  async findOneTask(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Buscando tarefa ${id} para telefone: ${phone}`);
    return this.agentWppService.findOneTask(id, phone);
  }

  @Patch('tasks/:id')
  async updateTask(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTaskDto: UpdateTaskAgentWppDto
  ) {
    this.logger.log(`Atualizando tarefa ${id} para telefone: ${updateTaskDto.phone}`);
    return this.agentWppService.updateTask(id, updateTaskDto);
  }

  @Delete('tasks/:phone/:id')
  async removeTask(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Removendo tarefa ${id} para telefone: ${phone}`);
    return this.agentWppService.removeTask(id, phone);
  }

  @Patch('tasks/:phone/:id/complete')
  async completeTask(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Completando tarefa ${id} para telefone: ${phone}`);
    return this.agentWppService.completeTask(id, phone);
  }

  @Post('tasks/categories')
  async createTaskCategory(@Body() createCategoryDto: CreateTaskCategoryAgentWppDto) {
    this.logger.log(`Criando categoria de tarefa para telefone: ${createCategoryDto.phone}`);
    return this.agentWppService.createTaskCategory(createCategoryDto);
  }

  // ===== FINANÇAS =====
  @Post('finances')
  async createFinance(@Body() createFinanceDto: CreateFinanceAgentWppDto) {
    this.logger.log(`Criando registro financeiro para telefone: ${createFinanceDto.phone}`);
    return this.agentWppService.createFinance(createFinanceDto);
  }

  @Get('finances/:phone')
  async findAllFinances(
    @PhoneParam('phone') phone: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    this.logger.log(`Buscando registros financeiros para telefone: ${phone}`);
    return this.agentWppService.findAllFinances(phone, page, limit);
  }

  @Get('finances/:phone/categories')
  async findAllFinanceCategories(@PhoneParam('phone') phone: string) {
    this.logger.log(`Buscando categorias financeiras para telefone: ${phone}`);
    return this.agentWppService.findAllFinanceCategories(phone);
  }

  @Get('finances/:phone/summary')
  async getFinanceSummary(
    @PhoneParam('phone') phone: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    this.logger.log(`Buscando resumo financeiro para telefone: ${phone}`);
    const startDateObj = startDate ? new Date(startDate) : undefined;
    const endDateObj = endDate ? new Date(endDate) : undefined;
    return this.agentWppService.getFinanceSummary(phone, startDateObj, endDateObj);
  }

  @Get('finances/:phone/recent')
  async getRecentFinances(
    @PhoneParam('phone') phone: string,
    @Query('limit') limit?: number,
    @Query('days') days?: number
  ) {
    this.logger.log(`Buscando transações recentes para telefone: ${phone}`);
    return this.agentWppService.getRecentFinances(phone, limit, days);
  }

  @Get('finances/:phone/:id')
  async findOneFinance(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Buscando registro financeiro ${id} para telefone: ${phone}`);
    return this.agentWppService.findOneFinance(id, phone);
  }

  @Patch('finances/:id')
  async updateFinance(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateFinanceDto: UpdateFinanceAgentWppDto
  ) {
    this.logger.log(`Atualizando registro financeiro ${id} para telefone: ${updateFinanceDto.phone}`);
    return this.agentWppService.updateFinance(id, updateFinanceDto);
  }

  @Delete('finances/:phone/:id')
  async removeFinance(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Removendo registro financeiro ${id} para telefone: ${phone}`);
    return this.agentWppService.removeFinance(id, phone);
  }

  @Post('finances/categories')
  async createFinanceCategory(@Body() createCategoryDto: CreateFinanceCategoryAgentWppDto) {
    this.logger.log(`Criando categoria financeira para telefone: ${createCategoryDto.phone}`);
    return this.agentWppService.createFinanceCategory(createCategoryDto);
  }

  // ===== IDEIAS =====
  @Post('ideas')
  async createIdea(@Body() createIdeaDto: CreateIdeaAgentWppDto) {
    this.logger.log(`Criando ideia para telefone: ${createIdeaDto.phone}`);
    return this.agentWppService.createIdea(createIdeaDto);
  }

  @Get('ideas/:phone')
  async findAllIdeas(
    @PhoneParam('phone') phone: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    this.logger.log(`Buscando ideias para telefone: ${phone}`);
    return this.agentWppService.findAllIdeas(phone, page, limit);
  }

  @Get('ideas/:phone/categories')
  async findAllIdeaCategories(@PhoneParam('phone') phone: string) {
    this.logger.log(`Buscando categorias de ideias para telefone: ${phone}`);
    return this.agentWppService.findAllIdeaCategories(phone);
  }

  @Get('ideas/:phone/recent')
  async getRecentIdeas(
    @PhoneParam('phone') phone: string,
    @Query('limit') limit?: number,
    @Query('days') days?: number
  ) {
    this.logger.log(`Buscando ideias recentes para telefone: ${phone}`);
    return this.agentWppService.getRecentIdeas(phone, limit, days);
  }

  @Get('ideas/:phone/:id')
  async findOneIdea(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Buscando ideia ${id} para telefone: ${phone}`);
    return this.agentWppService.findOneIdea(id, phone);
  }

  @Patch('ideas/:id')
  async updateIdea(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateIdeaDto: UpdateIdeaAgentWppDto
  ) {
    this.logger.log(`Atualizando ideia ${id} para telefone: ${updateIdeaDto.phone}`);
    return this.agentWppService.updateIdea(id, updateIdeaDto);
  }

  @Delete('ideas/:phone/:id')
  async removeIdea(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Removendo ideia ${id} para telefone: ${phone}`);
    return this.agentWppService.removeIdea(id, phone);
  }

  @Patch('ideas/:phone/:id/favorite')
  async toggleIdeaFavorite(
    @Param('id', ParseIntPipe) id: number,
    @PhoneParam('phone') phone: string
  ) {
    this.logger.log(`Alternando favorito da ideia ${id} para telefone: ${phone}`);
    return this.agentWppService.toggleIdeaFavorite(id, phone);
  }

  @Post('ideas/categories')
  async createIdeaCategory(@Body() createCategoryDto: CreateIdeaCategoryAgentWppDto) {
    this.logger.log(`Criando categoria de ideia para telefone: ${createCategoryDto.phone}`);
    return this.agentWppService.createIdeaCategory(createCategoryDto);
  }
}
