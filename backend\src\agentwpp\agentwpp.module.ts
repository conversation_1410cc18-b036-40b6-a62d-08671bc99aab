import { Modu<PERSON> } from '@nestjs/common';
import { AgentWppController } from './agentwpp.controller';
import { AgentWppService } from './agentwpp.service';
import { TasksModule } from '../tasks/tasks.module';
import { FinancesModule } from '../finances/finances.module';
import { IdeasModule } from '../ideas/ideas.module';
import { DashboardModule } from '../dashboard/dashboard.module';

@Module({
  imports: [
    TasksModule,
    FinancesModule,
    IdeasModule,
    DashboardModule,
  ],
  controllers: [AgentWppController],
  providers: [AgentWppService],
  exports: [AgentWppService],
})
export class AgentWppModule {}
