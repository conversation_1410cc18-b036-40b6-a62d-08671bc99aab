import { Injectable, Logger, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { db } from '../database.types';
import { FinancialUtils } from '../common/utils/financial.utils';
import { PaginatedFinancialResponseDto, FinancialSummary } from '../common/dto/common-response.dto';
import { TasksService } from '../tasks/tasks.service';
import { FinancesService } from '../finances/finances.service';
import { IdeasService } from '../ideas/ideas.service';
import { DashboardService } from '../dashboard/dashboard.service';
import { 
  CheckIntegrationResponseDto, 
  AgentWppDashboardResponseDto,
  CreateTaskAgentWppDto,
  UpdateTaskAgentWppDto,
  CreateTaskCategoryAgentWppDto,
  CreateFinanceAgentWppDto,
  UpdateFinanceAgentWppDto,
  CreateFinanceCategoryAgentWppDto,
  CreateIdeaAgentWppDto,
  UpdateIdeaAgentWppDto,
  CreateIdeaCategoryAgentWppDto
} from './dto/agentwpp.dto';

@Injectable()
export class AgentWppService {
  private readonly logger = new Logger(AgentWppService.name);
  private db = db;
  
  // Cache simples em memória para consultas frequentes
  private readonly cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutos

  constructor(
    private tasksService: TasksService,
    private financesService: FinancesService,
    private ideasService: IdeasService,
    private dashboardService: DashboardService,
  ) {}

  // Método para validar e normalizar telefone
  private validateAndNormalizePhone(phone: string): string {
    if (!phone) {
      throw new BadRequestException('Telefone é obrigatório');
    }

    // Remover todos os caracteres não numéricos exceto +
    const normalized = phone.replace(/[^\d+]/g, '');
    
    // Validar formato básico
    if (!/^\+?[\d]{10,15}$/.test(normalized)) {
      throw new BadRequestException('Formato de telefone inválido. Use apenas números e + (opcional)');
    }

    // Garantir que tenha pelo menos 10 dígitos
    const digitsOnly = normalized.replace('+', '');
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      throw new BadRequestException('Telefone deve ter entre 10 e 15 dígitos');
    }

    return normalized;
  }

  // Método para gerenciar cache
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data as T;
    }
    if (cached) {
      this.cache.delete(key); // Remover cache expirado
    }
    return null;
  }

  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Método para obter userId a partir do telefone
  private async getUserIdByPhone(phone: string): Promise<number> {
    try {
      const normalizedPhone = this.validateAndNormalizePhone(phone);
      const cacheKey = `user_phone_${normalizedPhone}`;
      
      // Tentar buscar do cache
      const cached = this.getFromCache<number>(cacheKey);
      if (cached) {
        return cached;
      }

      const integration = await this.db
        .selectFrom('integrations_whatsapp')
        .select(['user_id', 'status'])
        .where('phone', '=', normalizedPhone)
        .where('status', 'in', ['pending', 'active'])
        .executeTakeFirst();

      if (!integration) {
        throw new ForbiddenException(`Usuário com telefone ${phone} não possui integração WhatsApp ativa`);
      }

      // Cachear resultado
      this.setCache(cacheKey, integration.user_id);
      return integration.user_id;
    } catch (error) {
      this.logger.error(`Erro ao buscar usuário por telefone ${phone}:`, error);
      throw error;
    }
  }

  // Método para obter timezone do usuário
  private async getUserTimezone(userId: number): Promise<string> {
    try {
      const cacheKey = `user_timezone_${userId}`;
      
      // Tentar buscar do cache
      const cached = this.getFromCache<string>(cacheKey);
      if (cached) {
        return cached;
      }

      const user = await this.db
        .selectFrom('users')
        .select(['timezone'])
        .where('id', '=', userId)
        .executeTakeFirst();

      const timezone = user?.timezone || 'America/Sao_Paulo';
      
      // Cachear resultado
      this.setCache(cacheKey, timezone);
      return timezone;
    } catch (error) {
      this.logger.error(`Erro ao buscar timezone do usuário ${userId}:`, error);
      return 'America/Sao_Paulo';
    }
  }

  // ===== VERIFICAÇÃO DE INTEGRAÇÃO =====
  async checkIntegration(phone: string): Promise<CheckIntegrationResponseDto> {
    try {
      const normalizedPhone = this.validateAndNormalizePhone(phone);
      const cacheKey = `integration_check_${normalizedPhone}`;
      
      // Tentar buscar do cache
      const cached = this.getFromCache<CheckIntegrationResponseDto>(cacheKey);
      if (cached) {
        return cached;
      }

      const integration = await this.db
        .selectFrom('integrations_whatsapp')
        .select(['user_id', 'status'])
        .where('phone', '=', normalizedPhone)
        .executeTakeFirst();

      let result: CheckIntegrationResponseDto;
      
      if (integration) {
        // Buscar informações do usuário
        const user = await this.db
          .selectFrom('users')
          .select(['timezone'])
          .where('id', '=', integration.user_id)
          .executeTakeFirst();

        // Buscar configurações do assistente se a integração existir
        const assistantSettings = await this.db
          .selectFrom('user_assistant_settings')
          .select(['ai_humor', 'response_size', 'reminder_time', 'reminder_interval'])
          .where('user_id', '=', integration.user_id)
          .executeTakeFirst();

        // Buscar integração com Google Calendar
        const googleCalendarIntegration = await this.db
          .selectFrom('google_calendar_integration')
          .select(['calendar_email'])
          .where('user_id', '=', integration.user_id)
          .where('is_active', '=', true)
          .executeTakeFirst();

        result = {
          hasIntegration: true,
          status: integration.status as 'pending' | 'active' | 'inactive',
          userId: integration.user_id,
          timezone: user?.timezone || 'America/Sao_Paulo',
          assistantSettings: assistantSettings ? {
            ai_humor: assistantSettings.ai_humor || 'Moderado',
            response_size: assistantSettings.response_size || 'Curtas',
            reminder_time: assistantSettings.reminder_time ? assistantSettings.reminder_time.toString().substring(0, 5) : '09:00',
            reminder_interval: assistantSettings.reminder_interval || '30 minutos',
            google_calendar_email: googleCalendarIntegration?.calendar_email || null
          } : {
            ai_humor: 'Moderado',
            response_size: 'Curtas',
            reminder_time: '09:00',
            reminder_interval: '30 minutos',
            google_calendar_email: null
          }
        };
      } else {
        result = {
          hasIntegration: false
        };
      }

      // Cachear resultado (cache mais curto para verificações de integração)
      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      this.logger.error(`Erro ao verificar integração para telefone ${phone}:`, error);
      throw error;
    }
  }

  // ===== DASHBOARD =====
  async getDashboard(phone: string): Promise<AgentWppDashboardResponseDto> {
    try {
      const userId = await this.getUserIdByPhone(phone);
      const dashboardData = await this.dashboardService.getDashboardData(userId);

      return {
        user: dashboardData.user,
        tasks: dashboardData.tasks,
        finances: dashboardData.finances,
        ideas: dashboardData.ideas
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar dashboard para telefone ${phone}:`, error);
      throw error;
    }
  }

  // ===== TAREFAS =====
  async createTask(createTaskDto: CreateTaskAgentWppDto) {
    const userId = await this.getUserIdByPhone(createTaskDto.phone);
    const userTimezone = await this.getUserTimezone(userId);

    const { phone, ...taskData } = createTaskDto;
    return this.tasksService.create(taskData, userId, userTimezone);
  }

  async findAllTasks(phone: string, page = 1, limit = 50) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.tasksService.findAll(userId, userTimezone, page, limit);
  }

  async findOneTask(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.tasksService.findOne(id, userId, userTimezone);
  }

  async updateTask(id: number, updateTaskDto: UpdateTaskAgentWppDto) {
    const userId = await this.getUserIdByPhone(updateTaskDto.phone);
    const userTimezone = await this.getUserTimezone(userId);

    const { phone, ...taskData } = updateTaskDto;
    return this.tasksService.update(id, taskData, userId, userTimezone);
  }

  async removeTask(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    return this.tasksService.remove(id, userId);
  }

  async completeTask(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.tasksService.complete(id, userId, userTimezone);
  }

  async createTaskCategory(createCategoryDto: CreateTaskCategoryAgentWppDto) {
    const userId = await this.getUserIdByPhone(createCategoryDto.phone);

    const { phone, ...categoryData } = createCategoryDto;
    return this.tasksService.createCategory(categoryData, userId);
  }

  async findAllTaskCategories(phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    return this.tasksService.findAllCategories(userId);
  }

  // ===== FINANÇAS =====
  async createFinance(createFinanceDto: CreateFinanceAgentWppDto) {
    const userId = await this.getUserIdByPhone(createFinanceDto.phone);
    const userTimezone = await this.getUserTimezone(userId);

    const { phone, ...financeData } = createFinanceDto;
    return this.financesService.create(financeData, userId, userTimezone);
  }

  async findAllFinances(phone: string, page = 1, limit = 50) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    // Buscar dados paginados
    const paginatedResult = await this.financesService.findAll(userId, userTimezone, page, limit);
    
    // Buscar todas as transações para calcular resumo
    const allFinances = await this.db
      .selectFrom('finances')
      .select(['transaction_type', 'amount'])
      .where('user_id', '=', userId)
      .execute();
    
    // Calcular resumo financeiro
    const summary = FinancialUtils.calculateSummary(allFinances);
    
    // Retornar resposta com resumo financeiro
    return new PaginatedFinancialResponseDto(
      paginatedResult.finances,
      paginatedResult.total,
      paginatedResult.page,
      paginatedResult.limit,
      summary
    );
  }

  async findOneFinance(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.financesService.findOne(id, userId, userTimezone);
  }

  async updateFinance(id: number, updateFinanceDto: UpdateFinanceAgentWppDto) {
    const userId = await this.getUserIdByPhone(updateFinanceDto.phone);
    const userTimezone = await this.getUserTimezone(userId);

    const { phone, ...financeData } = updateFinanceDto;
    return this.financesService.update(id, financeData, userId, userTimezone);
  }

  async removeFinance(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    return this.financesService.remove(id, userId);
  }

  async getFinanceSummary(phone: string, startDate?: Date, endDate?: Date) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.financesService.getSummary(userId, userTimezone, startDate, endDate);
  }

  async createFinanceCategory(createCategoryDto: CreateFinanceCategoryAgentWppDto) {
    const userId = await this.getUserIdByPhone(createCategoryDto.phone);

    const { phone, ...categoryData } = createCategoryDto;
    return this.financesService.createCategory(categoryData, userId);
  }

  async findAllFinanceCategories(phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    return this.financesService.findAllCategories(userId);
  }

  // ===== IDEIAS =====
  async createIdea(createIdeaDto: CreateIdeaAgentWppDto) {
    const userId = await this.getUserIdByPhone(createIdeaDto.phone);

    const { phone, ...ideaData } = createIdeaDto;
    return this.ideasService.create(ideaData, userId);
  }

  async findAllIdeas(phone: string, page = 1, limit = 50) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.ideasService.findAll(userId, userTimezone, page, limit);
  }

  async findOneIdea(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.ideasService.findOne(id, userId, userTimezone);
  }

  async updateIdea(id: number, updateIdeaDto: UpdateIdeaAgentWppDto) {
    const userId = await this.getUserIdByPhone(updateIdeaDto.phone);
    const userTimezone = await this.getUserTimezone(userId);

    const { phone, ...ideaData } = updateIdeaDto;
    return this.ideasService.update(id, ideaData, userId, userTimezone);
  }

  async removeIdea(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    return this.ideasService.remove(id, userId);
  }

  async toggleIdeaFavorite(id: number, phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);

    return this.ideasService.toggleFavorite(id, userId, userTimezone);
  }

  async createIdeaCategory(createCategoryDto: CreateIdeaCategoryAgentWppDto) {
    const userId = await this.getUserIdByPhone(createCategoryDto.phone);

    const { phone, ...categoryData } = createCategoryDto;
    return this.ideasService.createCategory(categoryData, userId);
  }

  async findAllIdeaCategories(phone: string) {
    const userId = await this.getUserIdByPhone(phone);
    return this.ideasService.findAllCategories(userId);
  }

  // ===== NOVOS MÉTODOS PARA WHATSAPP =====
  
  // Obter transações recentes com total do período
  async getRecentFinances(phone: string, limit = 10, days = 1) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);
    
    // Calcular datas para o período
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Buscar transações recentes
    const recentTransactions = await this.db
      .selectFrom('finances')
      .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
      .select([
        'finances.id',
        'finances.transaction_type',
        'finances.amount',
        'finances.description',
        'finances.transaction_date',
        'finances_categories.name as category_name'
      ])
      .where('finances.user_id', '=', userId)
      .orderBy('finances.transaction_date', 'desc')
      .limit(limit)
      .execute();
    
    // Contar total de transações no período
    const totalCount = await this.db
      .selectFrom('finances')
      .select(this.db.fn.count('id').as('count'))
      .where('user_id', '=', userId)
      .where('transaction_date', '>=', startDate)
      .where('transaction_date', '<=', endDate)
      .executeTakeFirst();
    
    return {
      recent_transactions: recentTransactions.map(t => ({
        id: t.id,
        type: t.transaction_type,
        amount: t.amount,
        description: t.description,
        category_name: t.category_name,
        date: t.transaction_date
      })),
      total_in_period: Number(totalCount?.count || 0),
      period_days: days
    };
  }
  
  // Obter tarefas recentes com total do período
  async getRecentTasks(phone: string, limit = 10, days = 1) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);
    
    // Calcular datas para o período
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Buscar tarefas recentes
    const recentTasks = await this.db
      .selectFrom('tasks')
      .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
      .select([
        'tasks.id',
        'tasks.task_type',
        'tasks.name',
        'tasks.description',
        'tasks.task_date',
        'tasks.completed_at',
        'tasks_categories.name as category_name'
      ])
      .where('tasks.user_id', '=', userId)
      .orderBy('tasks.created_at', 'desc')
      .limit(limit)
      .execute();
    
    // Contar total de tarefas no período
    const totalCount = await this.db
      .selectFrom('tasks')
      .select(this.db.fn.count('id').as('count'))
      .where('user_id', '=', userId)
      .where('created_at', '>=', startDate)
      .where('created_at', '<=', endDate)
      .executeTakeFirst();
    
    return {
      recent_tasks: recentTasks.map(t => ({
        id: t.id,
        type: t.task_type,
        name: t.name,
        description: t.description,
        task_date: t.task_date,
        completed: !!t.completed_at,
        category_name: t.category_name
      })),
      total_in_period: Number(totalCount?.count || 0),
      period_days: days
    };
  }
  
  // Obter ideias recentes com total do período
  async getRecentIdeas(phone: string, limit = 10, days = 1) {
    const userId = await this.getUserIdByPhone(phone);
    const userTimezone = await this.getUserTimezone(userId);
    
    // Calcular datas para o período
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Buscar ideias recentes
    const recentIdeas = await this.db
      .selectFrom('ideas')
      .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
      .select([
        'ideas.id',
        'ideas.name',
        'ideas.description',
        'ideas.is_favorite',
        'ideas_categories.name as category_name'
      ])
      .where('ideas.user_id', '=', userId)
      .orderBy('ideas.created_at', 'desc')
      .limit(limit)
      .execute();
    
    // Contar total de ideias no período
    const totalCount = await this.db
      .selectFrom('ideas')
      .select(this.db.fn.count('id').as('count'))
      .where('user_id', '=', userId)
      .where('created_at', '>=', startDate)
      .where('created_at', '<=', endDate)
      .executeTakeFirst();
    
    return {
      recent_ideas: recentIdeas.map(i => ({
        id: i.id,
        name: i.name,
        description: i.description,
        is_favorite: i.is_favorite,
        category_name: i.category_name
      })),
      total_in_period: Number(totalCount?.count || 0),
      period_days: days
    };
  }
  
  // Criar tarefa rápida (sem data)
  async createQuickTask(createTaskDto: { phone: string; name: string; description?: string }) {
    const userId = await this.getUserIdByPhone(createTaskDto.phone);
    const userTimezone = await this.getUserTimezone(userId);
    
    const taskData = {
      task_type: 'task' as const,
      name: createTaskDto.name,
      description: createTaskDto.description
    };
    
    return this.tasksService.create(taskData, userId, userTimezone);
  }
  
  // ===== VALIDAÇÃO DE INTEGRAÇÃO =====
  async validateNumber(validateDto: { activation_code: string; phone: string }) {
    try {
      this.logger.log(`Validando número ${validateDto.phone} com código ${validateDto.activation_code}`);

      // Validar e normalizar telefone
      const normalizedPhone = this.validateAndNormalizePhone(validateDto.phone);

      // Validar código de ativação
      if (!validateDto.activation_code || validateDto.activation_code.trim().length === 0) {
        throw new BadRequestException('Código de ativação é obrigatório');
      }

      // Buscar integração pelo código de ativação
      const integration = await this.db
        .selectFrom('integrations_whatsapp')
        .where('activation_code', '=', validateDto.activation_code.trim())
        .where('status', '=', 'pending')
        .selectAll()
        .executeTakeFirst();

      if (!integration) {
        throw new NotFoundException('Código de ativação inválido ou expirado');
      }

      // Verificar se o telefone já está sendo usado por outra integração
      const existingPhoneIntegration = await this.db
        .selectFrom('integrations_whatsapp')
        .where('phone', '=', normalizedPhone)
        .where('status', 'in', ['pending', 'active'])
        .where('id', '!=', integration.id)
        .executeTakeFirst();

      if (existingPhoneIntegration) {
        throw new BadRequestException('Este número de telefone já está sendo usado por outra integração');
      }

      // Atualizar a integração com o número validado
      await this.db
        .updateTable('integrations_whatsapp')
        .set({
          phone: normalizedPhone,
          status: 'active',
          is_validated: true,
          activation_code: null // Limpar o código após a validação
        })
        .where('id', '=', integration.id)
        .execute();

      // Limpar cache relacionado ao telefone
      this.cache.delete(`user_phone_${normalizedPhone}`);
      this.cache.delete(`integration_check_${normalizedPhone}`);

      this.logger.log(`Número ${normalizedPhone} validado com sucesso`);
      
      return {
        success: true,
        message: 'Número validado com sucesso',
        phone: normalizedPhone,
        user_id: integration.user_id
      };
    } catch (error) {
      this.logger.error(`Erro ao validar número:`, error);
      throw error;
    }
  }
}
