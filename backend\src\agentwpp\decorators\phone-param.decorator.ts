import { createParamDecorator, ExecutionContext, BadRequestException } from '@nestjs/common';

/**
 * Decorator personalizado para parâmetros de telefone.
 * Garante que o valor seja sempre tratado como string,
 * evitando problemas com números grandes que perdem precisão.
 */
export const PhoneParam = createParamDecorator(
  (parameterName: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const param = request.params[parameterName];
    
    if (param === undefined) {
      throw new BadRequestException(`Parameter ${parameterName} is required`);
    }
    
    // Força conversão para string para evitar problemas com números grandes
    return String(param);
  },
);
