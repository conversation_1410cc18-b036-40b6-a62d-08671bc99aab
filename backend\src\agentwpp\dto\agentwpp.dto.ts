import { IsNotEmpty, IsString, IsOptional, IsEnum, IsDateString, IsNumber, IsBoolean, IsDecimal, IsPhoneNumber, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { PaginationDto } from '../../common/dto/common-response.dto';

// Base DTO com telefone para identificação do usuário
export class BaseAgentWppDto {
  @IsNotEmpty({ 
    message: 'Campo "phone" é obrigatório. Forneça um número de telefone válido para identificar o usuário.' 
  })
  @IsString({ 
    message: 'Campo "phone" deve ser uma string. Exemplo: "+5511999999999" ou "11999999999"' 
  })
  @Matches(/^\+?[\d\s\-\(\)]{10,20}$/, { 
    message: 'Formato de telefone inválido. Use apenas números, espaços, hífens, parênteses e + (opcional). Exemplos válidos: "+5511999999999", "11999999999", "(11) 99999-9999"' 
  })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // Normalizar telefone: remover espaços extras e caracteres especiais, manter apenas números e +
      const normalized = value.replace(/\s+/g, '').replace(/[^\d+]/g, '');
      // Log para debugging quando necessário
      // console.log(`[PHONE_VALIDATION] Original: "${value}" -> Normalized: "${normalized}"`);
      return normalized;
    }
    return value;
  })
  phone: string;
}

// DTO para paginação específica do AgentWPP
export class AgentWppPaginationDto extends PaginationDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsNumber()
  categoryId?: number;
}

// DTOs para Tarefas
export class CreateTaskAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsEnum(['appointment', 'task'])
  task_type: 'appointment' | 'task';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDateString()
  task_date?: string;
}

export class UpdateTaskAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsEnum(['appointment', 'task'])
  task_type?: 'appointment' | 'task';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDateString()
  task_date?: string;
}

export class CreateTaskCategoryAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

// DTOs para Finanças
export class CreateFinanceAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsEnum(['income', 'expense'])
  transaction_type: 'income' | 'expense';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsBoolean()
  is_saving?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  @IsDecimal({ decimal_digits: '0,2' })
  amount: string;

  @IsNotEmpty()
  @IsDateString()
  transaction_date: string;
}

export class UpdateFinanceAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsEnum(['income', 'expense'])
  transaction_type?: 'income' | 'expense';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsBoolean()
  is_saving?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  @IsDecimal({ decimal_digits: '0,2' })
  amount?: string;

  @IsOptional()
  @IsDateString()
  transaction_date?: string;
}

export class CreateFinanceCategoryAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEnum(['income', 'expense'])
  transaction_type: 'income' | 'expense';

  @IsOptional()
  @IsString()
  color?: string;
}

// DTOs para Ideias
export class CreateIdeaAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsBoolean()
  is_favorite?: boolean;
}

export class UpdateIdeaAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsBoolean()
  is_favorite?: boolean;
}

export class CreateIdeaCategoryAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

// DTOs de resposta
export class CheckIntegrationResponseDto {
  hasIntegration: boolean;
  status?: 'pending' | 'active' | 'inactive';
  userId?: number;
  timezone?: string;
  assistantSettings?: {
    ai_humor: string;
    response_size: string;
    reminder_time: string;
    reminder_interval: string;
    google_calendar_email: string | null;
  };
}

export class AgentWppDashboardResponseDto {
  user: {
    name: string;
    timezone: string;
  };
  tasks: {
    completed: number;
    total: number;
  };
  finances: {
    spent: number;
    budget: number;
    income: number;
    savings: number;
  };
  ideas: {
    today: number;
    total: number;
    favorites: number;
  };
}
