import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);
  private readonly requestCounts = new Map<string, { count: number; resetTime: number }>();
  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 minuto
  private readonly RATE_LIMIT_MAX_REQUESTS = 100; // 100 requests por minuto

  constructor(private configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];
    const validApiKey = this.configService.get('N8N_API_KEY');
    const clientIp = this.getClientIp(request);

    // Log da tentativa de acesso
    this.logger.debug(`[API_KEY] Tentativa de acesso de ${clientIp} para ${request.method} ${request.url}`);

    // Verificar se a API Key foi fornecida
    if (!apiKey) {
      this.logger.warn(`[API_KEY] Acesso negado - API Key não fornecida de ${clientIp}`);
      throw new UnauthorizedException('API Key é obrigatória');
    }

    // Verificar se a API Key é válida
    if (apiKey !== validApiKey) {
      this.logger.warn(`[API_KEY] Acesso negado - API Key inválida de ${clientIp}`);
      throw new UnauthorizedException('API Key inválida');
    }

    // Verificar rate limiting
    if (!this.checkRateLimit(clientIp)) {
      this.logger.warn(`[API_KEY] Rate limit excedido para ${clientIp}`);
      throw new UnauthorizedException('Rate limit excedido. Tente novamente em alguns minutos.');
    }

    this.logger.debug(`[API_KEY] Acesso autorizado para ${clientIp}`);
    return true;
  }

  private getClientIp(request: any): string {
    return request.ip || 
           request.connection?.remoteAddress || 
           request.socket?.remoteAddress || 
           request.connection?.socket?.remoteAddress || 
           'unknown';
  }

  private checkRateLimit(clientIp: string): boolean {
    const now = Date.now();
    const clientData = this.requestCounts.get(clientIp);

    // Se não há dados do cliente ou a janela de tempo expirou, resetar
    if (!clientData || now > clientData.resetTime) {
      this.requestCounts.set(clientIp, {
        count: 1,
        resetTime: now + this.RATE_LIMIT_WINDOW
      });
      return true;
    }

    // Se ainda está dentro da janela de tempo, incrementar contador
    if (clientData.count >= this.RATE_LIMIT_MAX_REQUESTS) {
      return false; // Rate limit excedido
    }

    clientData.count++;
    return true;
  }
}
