import { ValidationPipe, ArgumentMetadata, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class NoTransformValidationPipe extends ValidationPipe {
  constructor() {
    super({
      transform: false, // Desabilita transformação automática
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  async transform(value: any, metadata: ArgumentMetadata) {
    // Log para debug
    console.log(`[NoTransformValidationPipe] Type: ${metadata.type}, Data: ${metadata.data}, Value: ${value} (${typeof value})`);
    
    // Para parâmetros de rota (route params), especialmente telefone, não aplicamos transformação
    if (metadata.type === 'param') {
      // Garantir que telefones sempre sejam strings
      if (metadata.data === 'phone') {
        const phoneString = String(value);
        console.log(`[NoTransformValidationPipe] Phone param: ${value} -> ${phoneString}`);
        return phoneString;
      }
      return value;
    }
    
    // Para body, query, etc., aplica validação normal mas sem transformação automática
    try {
      return await super.transform(value, metadata);
    } catch (error) {
      console.error(`[NoTransformValidationPipe] Validation error:`, error);
      throw error;
    }
  }
}
