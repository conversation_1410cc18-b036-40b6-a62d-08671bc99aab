import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { DatabaseModule } from './database.module';
import { AuthModule } from './auth/auth.module';
import { AppService } from './app.service';
import { DashboardModule } from './dashboard/dashboard.module';
import { TasksModule } from './tasks/tasks.module';
import { FinancesModule } from './finances/finances.module';
import { IdeasModule } from './ideas/ideas.module';
import { ConfigModule as AppConfigModule } from './config/config.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { AgentWppModule } from './agentwpp/agentwpp.module';
import { HealthModule } from './health/health.module';
import { ProfileModule } from './profile/profile.module';
import { PlansModule } from './plans/plans.module';
import { AdminModule } from './admin/admin.module';
import { GoogleCalendarModule } from './google-calendar/google-calendar.module';
import { DatabaseErrorInterceptor } from './database/database-error.interceptor';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    DatabaseModule,
    AuthModule,
    DashboardModule,
    TasksModule,
    FinancesModule,
    IdeasModule,
    AppConfigModule,
    IntegrationsModule,
    AgentWppModule,
    HealthModule,
    ProfileModule,
    PlansModule,
    AdminModule,
    GoogleCalendarModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: DatabaseErrorInterceptor,
    },
  ],
})
export class AppModule {}
