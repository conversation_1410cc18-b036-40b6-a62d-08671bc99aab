import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthGuard } from '@nestjs/passport';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto.email, loginDto.password, loginDto.device_uuid);
  }

  @Post('refresh')
  async refresh(@Body() body: { refresh_token: string; device_uuid: string }) {
    return this.authService.refresh(body.refresh_token, body.device_uuid);
  }

  @Post('logout')
  async logout(@Body() body: { refresh_token: string; device_uuid: string }) {
    await this.authService.logout(body.refresh_token, body.device_uuid);
    return { message: 'Logged out successfully' };
  }

  @UseGuards(AuthGuard('jwt'))
  @Post('protected')
  async protected(@Request() req) {
    return { message: 'This is a protected route', user: req.user };
  }
}