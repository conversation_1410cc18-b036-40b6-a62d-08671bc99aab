import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsOption<PERSON> } from "class-validator";

export class RegisterDto {
    @IsNotEmpty()
    @IsString()
    name: string;

    @IsString()
    password: string;

    @IsEmail()
    email: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsString()
    timezone?: string;

    @IsOptional()
    @IsString()
    device_uuid?: string;
}
