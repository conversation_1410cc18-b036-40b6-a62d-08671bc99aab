import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable, of } from 'rxjs';

@Injectable()
export class CorsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Se for uma requisição OPTIONS, responder imediatamente
    if (request.method === 'OPTIONS') {
      response.header('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || 'http://localhost:5173');
      response.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
      response.header('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-Device-UUID,X-Dev-Mode,X-Dev-User-Id');
      response.header('Access-Control-Allow-Credentials', 'true');
      response.header('Access-Control-Max-Age', '3600');
      response.status(204).send();
      return of(null);
    }

    return next.handle();
  }
}
