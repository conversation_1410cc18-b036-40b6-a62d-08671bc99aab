import { IsN<PERSON>ber, IsOptional, Min } from 'class-validator';

// DTO genérico para paginação
export class PaginationDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number = 50;
}

// Resposta paginada genérica
export class PaginatedResponseDto<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;

  constructor(data: T[], total: number, page: number, limit: number) {
    this.data = data;
    this.total = total;
    this.page = page;
    this.limit = limit;
    this.totalPages = Math.ceil(total / limit);
    this.hasNext = page < this.totalPages;
    this.hasPrevious = page > 1;
  }
}

// Resposta padrão para sucesso
export class SuccessResponseDto<T = any> {
  success: boolean = true;
  message?: string;
  data?: T;
  timestamp: string = new Date().toISOString();

  constructor(data?: T, message?: string) {
    this.data = data;
    this.message = message;
  }
}

// Resposta padrão para erro
export class ErrorResponseDto {
  success: boolean = false;
  message: string;
  error?: string;
  timestamp: string = new Date().toISOString();
  statusCode?: number;

  constructor(message: string, error?: string, statusCode?: number) {
    this.message = message;
    this.error = error;
    this.statusCode = statusCode;
  }
}

// Resposta com valores financeiros
export class FinancialSummary {
  totalAmount: number;
  totalIncome: number;
  totalExpenses: number;
  balance: number;

  constructor(totalAmount: number = 0, totalIncome: number = 0, totalExpenses: number = 0) {
    this.totalAmount = totalAmount;
    this.totalIncome = totalIncome;
    this.totalExpenses = totalExpenses;
    this.balance = totalIncome - totalExpenses;
  }
}

// Resposta paginada com valores financeiros
export class PaginatedFinancialResponseDto<T> extends PaginatedResponseDto<T> {
  summary: FinancialSummary;

  constructor(
    data: T[], 
    total: number, 
    page: number, 
    limit: number,
    summary: FinancialSummary
  ) {
    super(data, total, page, limit);
    this.summary = summary;
  }
}
