import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ErrorResponseDto } from '../dto/common-response.dto';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger('GlobalExceptionFilter');

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = '';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const errorResponse = exception.getResponse();
      
      if (typeof errorResponse === 'string') {
        message = errorResponse;
      } else if (typeof errorResponse === 'object' && errorResponse !== null) {
        const errorObj = errorResponse as any;
        message = errorObj.message || errorObj.error || message;
        error = errorObj.error || '';
        
        // Se for um erro de validação, formatar as mensagens
        if (errorObj.message && Array.isArray(errorObj.message)) {
          message = errorObj.message.join(', ');
        }
      }
    } else if (exception instanceof Error) {
      message = exception.message;
      error = exception.name;
      this.logger.error(`Unhandled exception: ${exception.message}`, exception.stack);
    } else {
      this.logger.error('Unknown exception type', exception);
    }

    const errorResponse = new ErrorResponseDto(message, error, status);

    // Log do erro
    this.logger.error({
      message: 'Exception thrown',
      path: request.url,
      method: request.method,
      statusCode: status,
      error: message,
      stack: exception instanceof Error ? exception.stack : 'Unknown stack',
      timestamp: new Date().toISOString(),
    });

    response.status(status).json(errorResponse);
  }
}
