import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { SuccessResponseDto, ErrorResponseDto } from '../dto/common-response.dto';

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger('API');

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    const startTime = Date.now();
    const { method, url, body, query, params } = request;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip || 'unknown';

    // Log apenas se for erro (removido log de todas as requisições para reduzir poluição)

    return next.handle().pipe(
      map((data) => {
        const duration = Date.now() - startTime;
        
        // Log apenas erros (removido log de sucesso para reduzir poluição)

        // Se já é um SuccessResponseDto, retorna como está
        if (data instanceof SuccessResponseDto) {
          return data;
        }

        // Para rotas de API que retornam DTOs estruturados (como tasks, finances, etc),
        // não encapsular para manter compatibilidade com o frontend
        const isApiDataRoute = url.includes('/tasks') ||
                              url.includes('/finances') ||
                              url.includes('/ideas') ||
                              url.includes('/dashboard') ||
                              url.includes('/categories') ||
                              url.includes('/profile') ||
                              url.includes('/agentwpp');
        
        if (isApiDataRoute && data && typeof data === 'object') {
          return data;
        }

        // Para outras rotas, retorna os dados diretamente para manter compatibilidade
        // com o frontend que espera os dados sem wrapper
        return data;
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        
        // Log de erro
        this.logger.error({
          message: 'Request failed',
          method,
          url,
          error: error.message,
          stack: error.stack,
          duration: `${duration}ms`,
          timestamp: new Date().toISOString(),
        });

        // Padronizar erro
        if (error instanceof HttpException) {
          const status = error.getStatus();
          const errorResponse = error.getResponse();
          
          return throwError(() => new HttpException(
            new ErrorResponseDto(
              error.message,
              typeof errorResponse === 'string' ? errorResponse : JSON.stringify(errorResponse),
              status
            ),
            status
          ));
        }

        // Erro não tratado
        return throwError(() => new HttpException(
          new ErrorResponseDto(
            'Internal server error',
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR
          ),
          HttpStatus.INTERNAL_SERVER_ERROR
        ));
      })
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;
    
    const sensitiveFields = ['password', 'token', 'api_key', 'secret'];
    const sanitized = { ...body };
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    }
    
    return sanitized;
  }
}
