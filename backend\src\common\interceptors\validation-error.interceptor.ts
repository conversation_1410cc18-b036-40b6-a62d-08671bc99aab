import { 
  Injectable, 
  NestInterceptor, 
  ExecutionContext, 
  CallHandler,
  BadRequestException,
  Logger
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable()
export class ValidationErrorInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ValidationErrorInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const userAgent = request.headers['user-agent'] || '';
    const isN8NRequest = userAgent.toLowerCase().includes('n8n') || 
                        request.headers['x-n8n'] || 
                        request.url?.includes('/mcp');

    return next.handle().pipe(
      catchError((error) => {
        // Se for um erro de validação do class-validator
        if (error instanceof BadRequestException && error.getResponse && typeof error.getResponse === 'object') {
          const response = error.getResponse() as any;
          
          if (response.message && Array.isArray(response.message)) {
            // Melhorar mensagens de erro especificamente para requisições do n8n/MCP
            const improvedMessages = response.message.map((msg: string) => {
              if (msg.includes('phone') && msg.includes('should not be empty')) {
                return 'ERRO DE VALIDAÇÃO: Campo "phone" é obrigatório. Para usar o AgentWPP, você deve fornecer um número de telefone válido. Exemplo: {"phone": "+5511999999999"}';
              }
              if (msg.includes('phone') && msg.includes('must be a string')) {
                return 'ERRO DE VALIDAÇÃO: Campo "phone" deve ser uma string. Forneça o telefone como texto. Exemplo: {"phone": "11999999999"}';
              }
              if (msg.includes('task_type') && msg.includes('should not be empty')) {
                return 'ERRO DE VALIDAÇÃO: Campo "task_type" é obrigatório. Valores aceitos: "task" ou "appointment". Exemplo: {"task_type": "task"}';
              }
              if (msg.includes('name') && msg.includes('should not be empty')) {
                return 'ERRO DE VALIDAÇÃO: Campo "name" é obrigatório. Forneça um nome/título. Exemplo: {"name": "Minha tarefa"}';
              }
              if (msg.includes('transaction_type') && msg.includes('should not be empty')) {
                return 'ERRO DE VALIDAÇÃO: Campo "transaction_type" é obrigatório. Valores aceitos: "income" ou "expense". Exemplo: {"transaction_type": "expense"}';
              }
              if (msg.includes('amount') && msg.includes('should not be empty')) {
                return 'ERRO DE VALIDAÇÃO: Campo "amount" é obrigatório. Forneça o valor como string. Exemplo: {"amount": "50.00"}';
              }
              if (msg.includes('activation_code') && msg.includes('should not be empty')) {
                return 'ERRO DE VALIDAÇÃO: Campo "activation_code" é obrigatório. Forneça o código de ativação recebido. Exemplo: {"activation_code": "ABC123"}';
              }
              return msg;
            });

            // Log detalhado para debugging quando for requisição do n8n
            if (isN8NRequest) {
              this.logger.warn(`[N8N_VALIDATION_ERROR] Erro de validação em requisição do n8n/MCP:`);
              this.logger.warn(`[N8N_VALIDATION_ERROR] URL: ${request.method} ${request.url}`);
              this.logger.warn(`[N8N_VALIDATION_ERROR] Body: ${JSON.stringify(request.body)}`);
              this.logger.warn(`[N8N_VALIDATION_ERROR] Erros: ${JSON.stringify(improvedMessages)}`);
              this.logger.warn(`[N8N_VALIDATION_ERROR] Dica: Certifique-se de que todos os campos obrigatórios estão sendo enviados no formato correto.`);
            }

            // Retornar erro melhorado
            const improvedError = new BadRequestException({
              error: 'Validation failed',
              message: improvedMessages,
              statusCode: 400,
              timestamp: new Date().toISOString(),
              path: request.url,
              help: 'Verifique se todos os campos obrigatórios estão sendo enviados corretamente. Para o AgentWPP, o campo "phone" é sempre necessário.'
            });

            return throwError(() => improvedError);
          }
        }

        // Se não for erro de validação, apenas repassa
        return throwError(() => error);
      })
    );
  }
}
