export interface IBaseRepository<T, CreateDto, UpdateDto> {
  create(data: CreateDto, userId: number): Promise<T>;
  findAll(userId: number, options?: FindAllOptions): Promise<PaginatedResult<T>>;
  findOne(id: number, userId: number): Promise<T>;
  update(id: number, data: UpdateDto, userId: number): Promise<T>;
  remove(id: number, userId: number): Promise<void>;
}

export interface FindAllOptions {
  page?: number;
  limit?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}