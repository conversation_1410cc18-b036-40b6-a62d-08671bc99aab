import { ValidationPipe, ArgumentMetadata, Injectable } from '@nestjs/common';

@Injectable()
export class GlobalValidationPipe extends ValidationPipe {
  constructor() {
    super({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    });
  }

  async transform(value: any, metadata: ArgumentMetadata) {
    // Para parâmetros de rota, não aplicamos transformação para evitar problemas
    // com números grandes que perdem precisão (como telefones)
    if (metadata.type === 'param') {
      return value; // Retorna sem transformação
    }

    // Para body, query, etc., aplica transformação normal
    return await super.transform(value, metadata);
  }
}
