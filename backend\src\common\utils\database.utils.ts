import { Kysely } from 'kysely';
import { Database } from '../../database.types';

export class DatabaseUtils {
  static async executeWithTransaction<T>(
    db: Kysely<Database>,
    callback: (trx: any) => Promise<T>
  ): Promise<T> {
    return await db.transaction().execute(callback);
  }

  static buildPaginationQuery<T>(
    query: any,
    page: number = 1,
    limit: number = 50
  ) {
    const offset = (page - 1) * limit;
    return query.limit(limit).offset(offset);
  }
}