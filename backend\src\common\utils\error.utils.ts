import { Logger, HttpException, HttpStatus } from '@nestjs/common';

export class ErrorUtils {
  static handleServiceError(
    logger: Logger,
    error: any,
    operation: string,
    userId?: number
  ): never {
    const userInfo = userId ? ` para usuário ${userId}` : '';
    logger.error(`Erro ao ${operation}${userInfo}:`, error);

    if (error instanceof HttpException) {
      throw error;
    }

    // Mapear erros específicos do banco
    if (error.code === 'ER_DUP_ENTRY') {
      throw new HttpException(
        'Registro duplicado',
        HttpStatus.CONFLICT
      );
    }

    if (error.code === 'ER_NO_REFERENCED_ROW_2') {
      throw new HttpException(
        'Referência inválida',
        HttpStatus.BAD_REQUEST
      );
    }

    // Erro genérico
    throw new HttpException(
      `Erro ao ${operation}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  static validateOwnership(entity: any, userId: number, entityName: string): void {
    if (!entity) {
      throw new HttpException(
        `${entityName} não encontrado`,
        HttpStatus.NOT_FOUND
      );
    }

    if (entity.user_id !== userId) {
      throw new HttpException(
        'Acesso negado',
        HttpStatus.FORBIDDEN
      );
    }
  }
}