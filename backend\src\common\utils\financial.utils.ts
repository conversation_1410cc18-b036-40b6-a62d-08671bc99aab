import { FinancialSummary } from '../dto/common-response.dto';

export interface FinanceRecord {
  transaction_type: 'income' | 'expense';
  amount: string | number;
  is_saving?: boolean;
}

export class FinancialUtils {
  /**
   * Calcula resumo financeiro de uma lista de transações
   */
  static calculateSummary(finances: FinanceRecord[]): FinancialSummary {
    let totalIncome = 0;
    let totalExpenses = 0;
    let totalAmount = 0;

    for (const finance of finances) {
      const amount = typeof finance.amount === 'string' 
        ? parseFloat(finance.amount) 
        : finance.amount;

      if (isNaN(amount)) continue;

      totalAmount += Math.abs(amount);

      if (finance.transaction_type === 'income') {
        totalIncome += amount;
      } else if (finance.transaction_type === 'expense') {
        totalExpenses += Math.abs(amount);
      }
    }

    return new FinancialSummary(totalAmount, totalIncome, totalExpenses);
  }

  /**
   * Formatar valor monetário para exibição
   */
  static formatCurrency(value: number, currency: string = 'BRL'): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency,
    }).format(value);
  }

  /**
   * Converter string para número decimal
   */
  static parseDecimal(value: string): number {
    if (!value) return 0;
    // Remove qualquer caractere que não seja dígito, ponto ou vírgula
    const cleaned = value.replace(/[^\d.,]/g, '');
    // Substitui vírgula por ponto para conversão
    const normalized = cleaned.replace(',', '.');
    return parseFloat(normalized) || 0;
  }

  /**
   * Validar se um valor monetário é válido
   */
  static isValidAmount(amount: string | number): boolean {
    const numValue = typeof amount === 'string' 
      ? this.parseDecimal(amount) 
      : amount;
    
    return !isNaN(numValue) && numValue >= 0;
  }

  /**
   * Calcular percentual de mudança entre dois valores
   */
  static calculatePercentageChange(oldValue: number, newValue: number): number {
    if (oldValue === 0) return newValue > 0 ? 100 : 0;
    return ((newValue - oldValue) / oldValue) * 100;
  }
}
