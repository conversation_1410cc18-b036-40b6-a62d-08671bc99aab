// Simplificar timezone utils para otimizar performance temporariamente
export class TimezoneUtils {
  static fromUserTimezone(date: Date | string, timezone: string): Date {
    // Simplificar para retornar diretamente a data sem processamento pesado
    if (date instanceof Date) return date;
    return new Date(date);
  }

  static toUserTimezone(date: Date | string, timezone: string): Date {
    // Simplificar para retornar diretamente a data sem processamento pesado
    if (date instanceof Date) return date;
    return new Date(date);
  }

  static formatInTimezone(
    date: Date | string, 
    timezone: string, 
    formatStr: string = 'yyyy-MM-dd HH:mm:ss'
  ): string {
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toISOString().replace('T', ' ').substring(0, 19);
  }

  static prepareDateForDatabase(date: Date | string | null | undefined, timezone: string): Date | null {
    if (!date) return null;
    if (date instanceof Date) return date;
    return new Date(date);
  }
}

// Manter compatibilidade com código existente
export const fromUserTimezone = TimezoneUtils.fromUserTimezone;
export const toUserTimezone = TimezoneUtils.toUserTimezone;
export const formatForUser = TimezoneUtils.formatInTimezone;