import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Request,
  Query,
  HttpException,
  HttpStatus,
  Logger,
  Put
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ConfigService } from './config.service';
import {
  CreateAnnualSavingsGoalDto,
  UpdateAnnualSavingsGoalDto,
  AnnualSavingsGoalResponseDto
} from './dto/annual-savings-goal.dto';
import { UpdateUserConfigDto, UserConfigResponseDto } from './dto/user-config.dto';

@Controller('config')
@UseGuards(AuthGuard('jwt'))
export class ConfigController {
  private readonly logger = new Logger(ConfigController.name);

  constructor(private readonly configService: ConfigService) {}

  // Endpoints para meta anual de economia
  @Get('annual-savings-goal')
  async getAnnualSavingsGoal(
    @Request() req,
    @Query('year') year?: number
  ): Promise<AnnualSavingsGoalResponseDto | { message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      const goal = await this.configService.getAnnualSavingsGoal(userId, year);

      if (!goal) {
        return { message: 'Meta anual de economia não encontrada' };
      }

      return goal;
    } catch (error) {
      this.logger.error('Erro ao buscar meta anual de economia:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('annual-savings-goal')
  async createOrUpdateAnnualSavingsGoal(
    @Body() createGoalDto: CreateAnnualSavingsGoalDto,
    @Request() req
  ): Promise<AnnualSavingsGoalResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.configService.createOrUpdateAnnualSavingsGoal(createGoalDto, userId);
    } catch (error) {
      this.logger.error('Erro ao criar/atualizar meta anual de economia:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Endpoints para configuração do usuário
  @Get('user')
  async getUserConfig(@Request() req): Promise<UserConfigResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.configService.getUserConfig(userId);
    } catch (error) {
      this.logger.error('Erro ao buscar configuração do usuário:', error);

      if (error.message.includes('não encontrado')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('user')
  async updateUserConfig(
    @Body() updateUserDto: UpdateUserConfigDto,
    @Request() req
  ): Promise<UserConfigResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.configService.updateUserConfig(userId, updateUserDto);
    } catch (error) {
      this.logger.error('Erro ao atualizar configuração do usuário:', error);

      if (error.message.includes('não encontrado')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
