import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { db } from '../database.types';
import {
  CreateAnnualSavingsGoalDto,
  UpdateAnnualSavingsGoalDto,
  AnnualSavingsGoalResponseDto
} from './dto/annual-savings-goal.dto';
import { UpdateUserConfigDto, UserConfigResponseDto } from './dto/user-config.dto';

@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private db = db;

  // Métodos para meta anual de economia
  async getAnnualSavingsGoal(userId: number, year?: number): Promise<AnnualSavingsGoalResponseDto | null> {
    try {
      const currentYear = year || new Date().getFullYear();

      const goal = await this.db
        .selectFrom('config_annual_savings_goal')
        .selectAll()
        .where('user_id', '=', userId)
        .where('year', '=', currentYear)
        .executeTakeFirst();

      if (!goal) {
        return null;
      }

      this.logger.debug(`Meta anual de economia encontrada para usuário ${userId}, ano ${currentYear}`);
      return goal;
    } catch (error) {
      this.logger.error(`Erro ao buscar meta anual de economia para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar meta anual de economia: ${error.message}`);
    }
  }

  async createOrUpdateAnnualSavingsGoal(
    createGoalDto: CreateAnnualSavingsGoalDto,
    userId: number
  ): Promise<AnnualSavingsGoalResponseDto> {
    try {
      // Verificar se já existe uma meta para o ano
      const existingGoal = await this.getAnnualSavingsGoal(userId, createGoalDto.year);

      if (existingGoal) {
        // Atualizar meta existente
        await this.db
          .updateTable('config_annual_savings_goal')
          .set({
            amount: createGoalDto.amount,
            updated_at: new Date()
          })
          .where('id', '=', existingGoal.id)
          .where('user_id', '=', userId)
          .execute();

        this.logger.log(`Meta anual de economia atualizada para usuário ${userId}, ano ${createGoalDto.year}`);
        const updatedGoal = await this.getAnnualSavingsGoal(userId, createGoalDto.year);
        return updatedGoal!;
      } else {
        // Criar nova meta
        const goalData = {
          ...createGoalDto,
          user_id: userId,
          created_at: new Date(),
          updated_at: new Date()
        };

        const result = await this.db
          .insertInto('config_annual_savings_goal')
          .values(goalData)
          .executeTakeFirst();

        if (!result.insertId) {
          throw new Error('Falha ao criar meta anual de economia');
        }

        this.logger.log(`Meta anual de economia criada com ID ${result.insertId} para usuário ${userId}`);
        const newGoal = await this.getAnnualSavingsGoal(userId, createGoalDto.year);
        return newGoal!;
      }
    } catch (error) {
      this.logger.error(`Erro ao criar/atualizar meta anual de economia para usuário ${userId}:`, error);
      throw new Error(`Erro ao criar/atualizar meta anual de economia: ${error.message}`);
    }
  }

  // Métodos para configuração do usuário
  async getUserConfig(userId: number): Promise<UserConfigResponseDto> {
    try {
      const user = await this.db
        .selectFrom('users')
        .select(['id', 'name', 'email', 'phone', 'timezone', 'created_at', 'updated_at'])
        .where('id', '=', userId)
        .where('deleted_at', 'is', null)
        .executeTakeFirst();

      if (!user) {
        throw new NotFoundException(`Usuário com ID ${userId} não encontrado`);
      }

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone || undefined,
        timezone: user.timezone,
        created_at: user.created_at,
        updated_at: user.updated_at
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar configuração do usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar configuração do usuário: ${error.message}`);
    }
  }

  async updateUserConfig(userId: number, updateUserDto: UpdateUserConfigDto): Promise<UserConfigResponseDto> {
    try {
      // Verificar se o usuário existe
      await this.getUserConfig(userId);

      const updateData = {
        ...updateUserDto,
        updated_at: new Date()
      };

      // Remover campos undefined
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await this.db
        .updateTable('users')
        .set(updateData)
        .where('id', '=', userId)
        .where('deleted_at', 'is', null)
        .execute();

      this.logger.log(`Configuração do usuário ${userId} atualizada`);
      return this.getUserConfig(userId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao atualizar configuração do usuário ${userId}:`, error);
      throw new Error(`Erro ao atualizar configuração do usuário: ${error.message}`);
    }
  }
}
