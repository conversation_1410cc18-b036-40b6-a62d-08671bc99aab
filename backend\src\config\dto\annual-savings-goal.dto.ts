import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';

export class CreateAnnualSavingsGoalDto {
  @IsNotEmpty()
  @IsNumber()
  year: number;

  @IsNotEmpty()
  @IsString()
  amount: string; // DECIMAL como string
}

export class UpdateAnnualSavingsGoalDto {
  @IsNotEmpty()
  @IsString()
  amount: string; // DECIMAL como string
}

export class AnnualSavingsGoalResponseDto {
  id: number;
  year: number;
  amount: string;
  user_id?: number | null | undefined;
  created_at: Date;
  updated_at: Date;
}
