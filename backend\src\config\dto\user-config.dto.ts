import { IsOptional, IsString, IsEmail } from 'class-validator';

export class UpdateUserConfigDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  timezone?: string;
}

export class UserConfigResponseDto {
  id: number;
  name: string;
  email: string;
  phone?: string;
  timezone: string;
  created_at: Date;
  updated_at: Date;
}
