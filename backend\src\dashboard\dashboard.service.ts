import { Injectable, Logger } from '@nestjs/common';
import { db } from '../database.types';
import {
  DashboardResponseDto,
  TasksSummaryDto,
  FinancesSummaryDto,
  IdeasSummaryDto,
  MonthlyProgressDto,
  UserSummaryDto
} from './dto/dashboard-response.dto';
import { toUserTimezone, fromUserTimezone } from '../utils/timezone';
import {
  UserNotFoundException,
  DashboardDataException
} from './exceptions/dashboard.exceptions';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);
  private db = db;

  async getDashboardData(userId: number): Promise<DashboardResponseDto> {
    try {
      this.logger.log(`Buscando dados do dashboard para usuário ${userId}`);

      // Buscar informações do usuário
      const user = await this.getUserInfo(userId);

      // Buscar dados agregados
      const [tasks, finances, ideas, monthlyProgress] = await Promise.all([
        this.getTasksSummary(userId, user.timezone),
        this.getFinancesSummary(userId, user.timezone),
        this.getIdeasSummary(userId, user.timezone),
        this.getMonthlyProgress(userId, user.timezone)
      ]);

      const currentDate = new Date();

      const dashboardData = {
        user,
        tasks,
        finances,
        ideas,
        monthlyProgress,
        currentMonth: currentDate.toLocaleString('pt-BR', { month: 'long' }),
        currentYear: currentDate.getFullYear()
      };

      this.logger.log(`Dados do dashboard carregados com sucesso para usuário ${userId}`);
      return dashboardData;
    } catch (error) {
      this.logger.error(`Erro ao buscar dados do dashboard para usuário ${userId}:`, error);

      if (error instanceof UserNotFoundException) {
        throw error;
      }

      throw new DashboardDataException(error.message);
    }
  }

  private async getUserInfo(userId: number): Promise<UserSummaryDto> {
    try {
      const user = await this.db
        .selectFrom('users')
        .select(['name', 'timezone'])
        .where('id', '=', userId)
        .where('deleted_at', 'is', null)
        .executeTakeFirst();

      if (!user) {
        throw new UserNotFoundException(userId);
      }

      return {
        name: user.name,
        timezone: user.timezone
      };
    } catch (error) {
      if (error instanceof UserNotFoundException) {
        throw error;
      }

      this.logger.error(`Erro ao buscar informações do usuário ${userId}:`, error);
      throw new DashboardDataException(`Erro ao buscar informações do usuário: ${error.message}`);
    }
  }

  private async getTasksSummary(userId: number, userTimezone: string): Promise<TasksSummaryDto> {
    try {
      // Buscar apenas compromissos (appointments) do usuário
      const tasks = await this.db
        .selectFrom('tasks')
        .select(['completed_at'])
        .where('user_id', '=', userId)
        .where('task_type', '=', 'appointment')
        .execute();

      const completed = tasks.filter(task => task.completed_at !== null).length;
      const total = tasks.length;

      this.logger.debug(`Resumo de compromissos para usuário ${userId}: ${completed}/${total} completos`);

      return {
        completed,
        total
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar resumo de compromissos para usuário ${userId}:`, error);
      throw new DashboardDataException(`Erro ao buscar resumo de compromissos: ${error.message}`);
    }
  }

  private async getFinancesSummary(userId: number, userTimezone: string): Promise<FinancesSummaryDto> {
    try {
      const currentDate = new Date();
      const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);

      // Converter para UTC considerando o timezone do usuário
      const startOfMonthUTC = fromUserTimezone(startOfMonth, userTimezone);
      const endOfMonthUTC = fromUserTimezone(endOfMonth, userTimezone);

      // Buscar transações do mês atual
      const transactions = await this.db
        .selectFrom('finances')
        .select(['transaction_type', 'amount', 'is_saving'])
        .where('user_id', '=', userId)
        .where('transaction_date', '>=', startOfMonthUTC)
        .where('transaction_date', '<=', endOfMonthUTC)
        .execute();

      let income = 0;
      let expenses = 0;
      let savings = 0;

      transactions.forEach(transaction => {
        const amount = parseFloat(transaction.amount);

        if (transaction.transaction_type === 'income') {
          income += amount;
        } else if (transaction.transaction_type === 'expense') {
          expenses += amount;
        }

        // Cofrinho: depósitos (expense) são positivos, retiradas (income) são negativas
        if (transaction.is_saving) {
          if (transaction.transaction_type === 'expense') {
            savings += amount; // Depósito
          } else {
            savings -= amount; // Retirada
          }
        }
      });

      // Buscar meta anual de economia para calcular orçamento
      const savingsGoal = await this.db
        .selectFrom('config_annual_savings_goal')
        .select(['amount'])
        .where('user_id', '=', userId)
        .where('year', '=', currentDate.getFullYear())
        .executeTakeFirst();

      const monthlyBudget = savingsGoal ? parseFloat(savingsGoal.amount) / 12 : 0; // Sem meta = 0
      const hasAnnualGoal = !!savingsGoal;

      this.logger.debug(`Resumo financeiro para usuário ${userId}: receita=${income}, gastos=${expenses}, economia=${savings}, meta anual=${hasAnnualGoal}`);

      return {
        spent: expenses,
        budget: monthlyBudget,
        income,
        expenses,
        savings,
        hasAnnualGoal
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar resumo financeiro para usuário ${userId}:`, error);
      throw new DashboardDataException(`Erro ao buscar resumo financeiro: ${error.message}`);
    }
  }

  private async getIdeasSummary(userId: number, userTimezone: string): Promise<IdeasSummaryDto> {
    const currentDate = new Date();
    const startOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
    const endOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), 23, 59, 59);

    // Converter para UTC considerando o timezone do usuário
    const startOfDayUTC = fromUserTimezone(startOfDay, userTimezone);
    const endOfDayUTC = fromUserTimezone(endOfDay, userTimezone);

    // Buscar ideias criadas hoje
    const todayIdeas = await this.db
      .selectFrom('ideas')
      .select(['id'])
      .where('user_id', '=', userId)
      .where('created_at', '>=', startOfDayUTC)
      .where('created_at', '<=', endOfDayUTC)
      .execute();

    // Buscar total de ideias
    const totalIdeas = await this.db
      .selectFrom('ideas')
      .select(['id'])
      .where('user_id', '=', userId)
      .execute();

    // Buscar ideias favoritas
    const favoriteIdeas = await this.db
      .selectFrom('ideas')
      .select(['id'])
      .where('user_id', '=', userId)
      .where('is_favorite', '=', true)
      .execute();

    return {
      today: todayIdeas.length,
      total: totalIdeas.length,
      favorites: favoriteIdeas.length
    };
  }

  private async getMonthlyProgress(userId: number, userTimezone: string): Promise<MonthlyProgressDto> {
    const currentDate = new Date();
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);

    // Converter para UTC considerando o timezone do usuário
    const startOfMonthUTC = fromUserTimezone(startOfMonth, userTimezone);
    const endOfMonthUTC = fromUserTimezone(endOfMonth, userTimezone);

    // Buscar compromissos (appointments) do mês
    const appointments = await this.db
      .selectFrom('tasks')
      .select(['completed_at'])
      .where('user_id', '=', userId)
      .where('task_type', '=', 'appointment')
      .where('created_at', '>=', startOfMonthUTC)
      .where('created_at', '<=', endOfMonthUTC)
      .execute();

    const appointmentsCompleted = appointments.filter(task => task.completed_at !== null).length;
    const appointmentsTotal = appointments.length;

    // Buscar economia do mês (depósitos - retiradas)
    const savingsTransactions = await this.db
      .selectFrom('finances')
      .select(['amount', 'transaction_type'])
      .where('user_id', '=', userId)
      .where('is_saving', '=', true)
      .where('transaction_date', '>=', startOfMonthUTC)
      .where('transaction_date', '<=', endOfMonthUTC)
      .execute();

    const monthlySavings = savingsTransactions.reduce((total, transaction) => {
      const amount = parseFloat(transaction.amount);
      // Depósitos (expense) são positivos, retiradas (income) são negativas
      return transaction.transaction_type === 'expense' ? total + amount : total - amount;
    }, 0);

    // Buscar ideias criadas no mês
    const monthlyIdeas = await this.db
      .selectFrom('ideas')
      .select(['id'])
      .where('user_id', '=', userId)
      .where('created_at', '>=', startOfMonthUTC)
      .where('created_at', '<=', endOfMonthUTC)
      .execute();

    // Buscar tarefas completadas no mês
    const completedTasks = await this.db
      .selectFrom('tasks')
      .select(['id'])
      .where('user_id', '=', userId)
      .where('completed_at', '>=', startOfMonthUTC)
      .where('completed_at', '<=', endOfMonthUTC)
      .execute();

    // Calcular progresso da meta financeira
    const savingsGoal = await this.db
      .selectFrom('config_annual_savings_goal')
      .select(['amount'])
      .where('user_id', '=', userId)
      .where('year', '=', currentDate.getFullYear())
      .executeTakeFirst();

    const monthlyGoal = savingsGoal ? parseFloat(savingsGoal.amount) / 12 : 0;
    const financialGoalProgress = monthlyGoal > 0 ? (monthlySavings / monthlyGoal) * 100 : 0;

    return {
      appointments: {
        completed: appointmentsCompleted,
        total: appointmentsTotal
      },
      savings: monthlySavings,
      ideas: monthlyIdeas.length,
      tasksCompleted: completedTasks.length,
      financialGoalProgress: Math.min(financialGoalProgress, 100) // Limitar a 100%
    };
  }
}
