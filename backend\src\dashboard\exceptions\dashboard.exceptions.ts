import { HttpException, HttpStatus } from '@nestjs/common';

export class UserNotFoundException extends HttpException {
  constructor(userId: number) {
    super(`Usuário com ID ${userId} não encontrado`, HttpStatus.NOT_FOUND);
  }
}

export class DashboardDataException extends HttpException {
  constructor(message: string, status: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR) {
    super(`Erro ao buscar dados do dashboard: ${message}`, status);
  }
}

export class InvalidUserTokenException extends HttpException {
  constructor() {
    super('Token de usuário inválido ou não fornecido', HttpStatus.UNAUTHORIZED);
  }
}
