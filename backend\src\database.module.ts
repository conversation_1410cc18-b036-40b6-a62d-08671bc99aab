import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { databaseProviders } from './database/database.provider';
import { DatabaseHealthService } from './database/database-health.service';
import { DatabaseErrorInterceptor } from './database/database-error.interceptor';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
  ],
  providers: [
    ...databaseProviders,
    DatabaseHealthService,
    DatabaseErrorInterceptor,
  ],
  exports: [
    ...databaseProviders,
    DatabaseHealthService,
    DatabaseErrorInterceptor,
  ],
})
export class DatabaseModule {}
