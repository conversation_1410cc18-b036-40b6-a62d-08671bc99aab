import { Generated, Kysely, MysqlDialect } from "kysely";
import { createPool } from 'mysql2';
import * as dotenv from 'dotenv';

dotenv.config();

export interface UserTable {
  id: Generated<number>;
  name: string;
  email: string;
  password?: string | null;
  phone?: string | null;
  timezone: string;
  is_admin?: boolean | null;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

export interface RefreshTokenTable {
  id: Generated<number>;
  refresh_token: string;
  device_uuid: string;
  expires_at: Date;
  revoked?: boolean | null;
  user_id?: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface ConfigAnnualSavingsGoalTable {
  id: Generated<number>;
  year: number;
  amount: string; // DECIMAL como string
  user_id?: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface AnnualGoalTable {
  id: Generated<number>;
  user_id: number;
  goal_amount: string; // DECIMAL como string
  year: number;
  created_at: Date;
  updated_at: Date;
}


export interface TaskCategoryTable {
  id: Generated<number>;
  name: string;
  user_id?: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface TaskTable {
  id: Generated<number>;
  task_type: 'appointment' | 'task';
  category_id?: number | null;
  name: string;
  description?: string | null;
  task_date?: Date | null;
  user_id: number;
  completed_at?: Date | null;
  created_at: Date;
  updated_at: Date;
}

export interface FinanceCategoryTable {
  id: Generated<number>;
  name: string;
  transaction_type: 'income' | 'expense';
  color?: string | null;
  user_id?: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface FinanceTable {
  id: Generated<number>;
  transaction_type: 'income' | 'expense';
  category_id?: number | null;
  is_saving?: boolean | null;
  description?: string | null;
  amount: string; // DECIMAL como string
  transaction_date: Date;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface IdeaCategoryTable {
  id: Generated<number>;
  name: string;
  user_id?: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface IdeaTable {
  id: Generated<number>;
  category_id?: number | null;
  name: string;
  description?: string | null;
  content?: string | null;
  is_favorite?: boolean | null;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface IntegrationWhatsappTable {
  id: Generated<number>;
  status: 'pending' | 'active' | 'inactive';
  phone: string | null;
  is_validated?: boolean | null;
  activation_code?: string | null;
  user_id: number;
}

export interface UserAssistantSettingsTable {
  id: Generated<number>;
  user_id: number;
  ai_humor: 'formal' | 'friendly' | 'casual' | 'professional';
  response_size: 'short' | 'medium' | 'long' | 'detailed';
  reminder_time?: string | null;
  reminder_interval?: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface PaymentSettingsTable {
  id: Generated<number>;
  stripe_public_key?: string | null;
  stripe_secret_key?: string | null;
  stripe_webhook_secret?: string | null;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface GoogleCalendarIntegrationTable {
  id: Generated<number>;
  user_id: number;
  calendar_email: string;
  is_active: boolean;
  sync_tasks: boolean;
  sync_appointments: boolean;
  default_reminder_minutes: number;
  created_at: Date;
  updated_at: Date;
}

export interface PlanTable {
  id: Generated<number>;
  name: string;
  slug: string;
  description?: string | null;
  price: string; // DECIMAL como string
  currency: string;
  billing_period: 'monthly' | 'yearly';
  stripe_price_id?: string | null;
  features?: string | null; // JSON como string
  is_active: boolean;
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

export interface UserSubscriptionTable {
  id: Generated<number>;
  user_id: number;
  plan_id: number;
  stripe_subscription_id?: string | null;
  stripe_customer_id?: string | null;
  status: 'active' | 'inactive' | 'canceled' | 'past_due' | 'unpaid';
  current_period_start?: Date | null;
  current_period_end?: Date | null;
  canceled_at?: Date | null;
  ends_at?: Date | null;
  created_at: Date;
  updated_at: Date;
}

export interface Database {
  users: UserTable;
  refresh_tokens: RefreshTokenTable;
  config_annual_savings_goal: ConfigAnnualSavingsGoalTable;
  annual_goals: AnnualGoalTable;
  tasks_categories: TaskCategoryTable;
  tasks: TaskTable;
  finances_categories: FinanceCategoryTable;
  finances: FinanceTable;
  ideas_categories: IdeaCategoryTable;
  ideas: IdeaTable;
  integrations_whatsapp: IntegrationWhatsappTable;
  user_assistant_settings: UserAssistantSettingsTable;
  payment_settings: PaymentSettingsTable;
  google_calendar_integration: GoogleCalendarIntegrationTable;
  plans: PlanTable;
  user_subscriptions: UserSubscriptionTable;
}

export const db = new Kysely<Database>({
  dialect: new MysqlDialect({
    pool: createPool({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      timezone: '+00:00',
      debug: process.env.MYSQL_VERBOSE_LOGS === 'true', // Only enable debug if explicitly requested
      trace: false, // Always disable packet tracing
      charset: 'utf8mb4',
      supportBigNumbers: true,
      bigNumberStrings: true,
    }),
  }),
  log: process.env.MYSQL_VERBOSE_LOGS === 'true' ? ['query', 'error'] : ['error'], // Conditional logging
});