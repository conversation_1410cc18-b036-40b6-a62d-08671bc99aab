import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { DatabaseHealthService } from './database-health.service';

@Injectable()
export class DatabaseErrorInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DatabaseErrorInterceptor.name);

  constructor(private readonly databaseHealthService: DatabaseHealthService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        if (this.isDatabaseError(error)) {
          this.logger.error('Database error intercepted:', error.message);
          
          // Log additional context
          const request = context.switchToHttp().getRequest();
          this.logger.error(`Error occurred in ${request.method} ${request.url}`);
          
          // Check if it's a connection error that might benefit from retry
          if (this.isConnectionError(error)) {
            this.logger.warn('Connection error detected, health service will handle reconnection');
            
            // Return a more user-friendly error
            return throwError(() => new HttpException(
              {
                statusCode: HttpStatus.SERVICE_UNAVAILABLE,
                message: 'Serviço temporariamente indisponível. Tente novamente em alguns momentos.',
                error: 'Service Unavailable',
                timestamp: new Date().toISOString(),
                path: context.switchToHttp().getRequest().url,
              },
              HttpStatus.SERVICE_UNAVAILABLE,
            ));
          }
          
          // For other database errors
          if (this.isQueryError(error)) {
            this.logger.error('Query error:', error.message);
            return throwError(() => new HttpException(
              {
                statusCode: HttpStatus.BAD_REQUEST,
                message: 'Erro na consulta ao banco de dados',
                error: 'Bad Request',
                timestamp: new Date().toISOString(),
                path: context.switchToHttp().getRequest().url,
              },
              HttpStatus.BAD_REQUEST,
            ));
          }
          
          // Generic database error
          return throwError(() => new HttpException(
            {
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
              message: 'Erro interno do servidor',
              error: 'Internal Server Error',
              timestamp: new Date().toISOString(),
              path: context.switchToHttp().getRequest().url,
            },
            HttpStatus.INTERNAL_SERVER_ERROR,
          ));
        }
        
        // If it's not a database error, re-throw as is
        return throwError(() => error);
      }),
    );
  }

  private isDatabaseError(error: any): boolean {
    if (!error) return false;

    // Check for common database error indicators
    const databaseErrorIndicators = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'PROTOCOL_CONNECTION_LOST',
      'ER_',
      'WARN_DATA_TRUNCATED',
      'ER_NO_SUCH_TABLE',
      'ER_BAD_FIELD_ERROR',
      'ER_DUP_ENTRY',
      'Connection',
      'Pool',
      'mysql',
      'Kysely',
    ];

    return databaseErrorIndicators.some(indicator =>
      error.code?.includes(indicator) ||
      error.message?.includes(indicator) ||
      error.name?.includes(indicator) ||
      error.errno?.toString().includes(indicator)
    );
  }

  private isConnectionError(error: any): boolean {
    if (!error) return false;

    const connectionErrorCodes = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'PROTOCOL_CONNECTION_LOST',
      'ER_GET_CONNECTION_TIMEOUT',
      'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR',
      'PROTOCOL_ENQUEUE_AFTER_QUIT',
      'Connection lost',
      'Connection timeout',
    ];

    return connectionErrorCodes.some(code =>
      error.code === code ||
      error.message?.includes(code) ||
      error.errno === code
    );
  }

  private isQueryError(error: any): boolean {
    if (!error) return false;

    const queryErrorCodes = [
      'ER_NO_SUCH_TABLE',
      'ER_BAD_FIELD_ERROR',
      'ER_DUP_ENTRY',
      'ER_SYNTAX_ERROR',
      'ER_UNKNOWN_COLUMN',
      'ER_NON_UNIQ_ERROR',
      'ER_TABLE_EXISTS_ERROR',
      'ER_PARSE_ERROR',
    ];

    return queryErrorCodes.some(code =>
      error.code === code ||
      error.message?.includes(code) ||
      error.errno === code
    );
  }
}
