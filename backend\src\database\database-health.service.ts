import { Injectable, Logger, OnModuleInit, Inject } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Connection } from 'mysql2/promise';
import { MYSQL2_CONNECTION } from './database.provider';

@Injectable()
export class DatabaseHealthService implements OnModuleInit {
  private readonly logger = new Logger(DatabaseHealthService.name);
  private isHealthy = false;
  private lastHealthCheck: Date | null = null;
  private consecutiveFailures = 0;
  private readonly maxConsecutiveFailures = 3;

  constructor(
    @Inject(MYSQL2_CONNECTION)
    private readonly connection: Connection,
  ) {}

  async onModuleInit() {
    // Perform initial health check
    await this.performHealthCheck();
  }

  @Cron('0 */2 * * * *') // A cada 2 minutos
  async scheduledHealthCheck() {
    await this.performHealthCheck();
  }

  private async performHealthCheck(): Promise<void> {
    try {
      // Simple query to test connection
      const startTime = Date.now();
      await this.connection.execute('SELECT 1 as test');
        
      const duration = Date.now() - startTime;
      
      this.isHealthy = true;
      this.lastHealthCheck = new Date();
      this.consecutiveFailures = 0;
    } catch (error) {
      this.consecutiveFailures++;
      this.isHealthy = false;
      
      this.logger.error(`Database health check failed (attempt ${this.consecutiveFailures}/${this.maxConsecutiveFailures}):`, error.message);
      
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        this.logger.error('Database appears to be unhealthy after multiple consecutive failures');
      }
    }
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error = new Error('Unknown error');
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (this.isConnectionError(error)) {
          this.logger.warn(`Database operation failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
          
          if (attempt < maxRetries) {
            this.logger.debug(`Retrying in ${retryDelay}ms...`);
            await this.sleep(retryDelay);
            
            // Exponential backoff
            retryDelay *= 2;
            
            // Perform health check before retry
            await this.performHealthCheck();
            
            continue;
          }
        }
        
        // If it's not a connection error or we've exceeded max retries, throw immediately
        throw error;
      }
    }
    
    throw lastError;
  }

  private isConnectionError(error: any): boolean {
    if (!error) return false;
    
    const connectionErrorCodes = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'PROTOCOL_CONNECTION_LOST',
      'ER_GET_CONNECTION_TIMEOUT',
      'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR',
      'PROTOCOL_ENQUEUE_AFTER_QUIT',
    ];
    
    return connectionErrorCodes.some(code => 
      error.code === code || 
      error.message?.includes(code) ||
      error.errno === code
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getHealthStatus() {
    return {
      isHealthy: this.isHealthy,
      lastHealthCheck: this.lastHealthCheck,
      consecutiveFailures: this.consecutiveFailures,
    };
  }

  async testConnection(): Promise<{ success: boolean; duration: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      await this.connection.execute('SELECT 1 as test');
        
      return {
        success: true,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        duration: Date.now() - startTime,
        error: error.message,
      };
    }
  }
}
