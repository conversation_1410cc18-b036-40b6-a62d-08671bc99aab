-- C<PERSON>r tabela user_assistant_settings se não existir
CREATE TABLE IF NOT EXISTS user_assistant_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ai_humor ENUM('formal', 'friendly', 'casual', 'professional') DEFAULT 'friendly',
    response_size ENUM('short', 'medium', 'long', 'detailed') DEFAULT 'medium',
    reminder_time TIME DEFAULT '09:00:00',
    reminder_interval VARCHAR(10) DEFAULT '30',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
);

-- Adicionar novos campos se a tabela já existir (verificação segura)
-- Note: MySQL não suporta ADD COLUMN IF NOT EXISTS diretamente
-- <PERSON><PERSON><PERSON> linhas podem gerar erro se as colunas já existirem, mas isso é normal
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'user_assistant_settings' 
  AND column_name = 'reminder_time';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE user_assistant_settings ADD COLUMN reminder_time TIME DEFAULT ''09:00:00''',
  'SELECT ''Column reminder_time already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'user_assistant_settings' 
  AND column_name = 'reminder_interval';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE user_assistant_settings ADD COLUMN reminder_interval VARCHAR(10) DEFAULT ''30''',
  'SELECT ''Column reminder_interval already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
