import { Logger } from '@nestjs/common';

/**
 * Custom MySQL2 logger configuration to suppress verbose connection logs
 * while keeping essential error and connection status information
 */
export class MySQLLoggerConfig {
  private static readonly logger = new Logger('MySQL2');
  private static originalConsoleLog: typeof console.log;
  private static originalConsoleError: typeof console.error;
  private static originalConsoleWarn: typeof console.warn;
  private static isConfigured = false;

  /**
   * Configure MySQL2 logging to suppress verbose output
   */
  static configure(): void {
    if (this.isConfigured) {
      return;
    }

    // Store original console methods
    this.originalConsoleLog = console.log;
    this.originalConsoleError = console.error;
    this.originalConsoleWarn = console.warn;

    // Also override process.stdout.write to catch low-level output
    const originalStdoutWrite = process.stdout.write;
    process.stdout.write = function(chunk: any, encoding?: any, callback?: any): boolean {
      const message = chunk.toString();
      if (MySQLLoggerConfig.shouldSuppressLog(message)) {
        return true; // Suppress the output
      }
      return originalStdoutWrite.call(process.stdout, chunk, encoding, callback);
    };

    // Override console methods to filter MySQL2 verbose logs
    console.log = (...args: any[]) => {
      const message = args.join(' ');
      
      // Filter out verbose MySQL2 logs
      if (this.shouldSuppressLog(message)) {
        return;
      }
      
      this.originalConsoleLog.apply(console, args);
    };

    console.error = (...args: any[]) => {
      const message = args.join(' ');
      
      // Allow important error messages through
      if (this.isImportantError(message)) {
        this.originalConsoleError.apply(console, args);
        return;
      }
      
      // Filter out verbose error logs
      if (this.shouldSuppressLog(message)) {
        return;
      }
      
      this.originalConsoleError.apply(console, args);
    };

    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      
      // Filter MySQL2 configuration warnings but keep important ones
      if (this.isConfigurationWarning(message)) {
        // Log configuration warnings only once with our logger
        if (message.includes('acquireTimeout') || message.includes('timeout') || message.includes('reconnect')) {
          this.logger.debug('MySQL2 configuration warning suppressed (non-critical)');
        }
        return;
      }
      
      this.originalConsoleWarn.apply(console, args);
    };

    this.isConfigured = true;
    this.logger.debug('MySQL2 verbose logging suppression configured');
  }

  /**
   * Restore original console methods
   */
  static restore(): void {
    if (!this.isConfigured) {
      return;
    }

    console.log = this.originalConsoleLog;
    console.error = this.originalConsoleError;
    console.warn = this.originalConsoleWarn;
    
    this.isConfigured = false;
    this.logger.debug('MySQL2 logging configuration restored');
  }

  /**
   * Check if a log message should be suppressed
   */
  private static shouldSuppressLog(message: string): boolean {
    const suppressPatterns = [
      // Raw packet data
      /raw:\s+[0-9a-f]+/i,
      // Trace information
      /at\s+.*mysql2.*connection\.js/i,
      /at\s+.*mysql2.*packet_parser\.js/i,
      // ClientHandshake messages
      /ClientHandshake#unknown\s+name/i,
      // Server hello packet details
      /Server\s+hello\s+packet:\s+capability\s+flags/i,
      // Sending handshake packet
      /Sending\s+handshake\s+packet:\s+flags/i,
      // Hex dump patterns
      /^[0-9a-f\s]+$/i,
      // Add command messages
      /Add\s+command:\s+ClientHandshake/i,
      // Packet direction indicators
      /^\d+\s+\d+\s+[<>=]+\s+/i,
    ];

    return suppressPatterns.some(pattern => pattern.test(message));
  }

  /**
   * Check if an error message is important and should not be suppressed
   */
  private static isImportantError(message: string): boolean {
    const importantPatterns = [
      /connection\s+refused/i,
      /access\s+denied/i,
      /unknown\s+database/i,
      /table.*doesn't\s+exist/i,
      /syntax\s+error/i,
      /duplicate\s+entry/i,
      /foreign\s+key\s+constraint/i,
    ];

    return importantPatterns.some(pattern => pattern.test(message));
  }

  /**
   * Check if a warning is a MySQL2 configuration warning
   */
  private static isConfigurationWarning(message: string): boolean {
    const configWarningPatterns = [
      /Ignoring\s+invalid\s+configuration\s+option/i,
      /acquireTimeout.*warning/i,
      /timeout.*warning/i,
      /reconnect.*warning/i,
    ];

    return configWarningPatterns.some(pattern => pattern.test(message));
  }
}
