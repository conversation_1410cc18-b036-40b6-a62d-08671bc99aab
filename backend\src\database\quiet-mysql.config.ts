/**
 * Quiet MySQL2 Configuration
 * 
 * This module provides a simple way to suppress MySQL2's verbose connection logs
 * by overriding the console methods before MySQL2 initializes.
 */

let isConfigured = false;
let originalConsoleLog: typeof console.log;
let originalStdoutWrite: typeof process.stdout.write;

/**
 * Configure quiet MySQL2 logging
 */
export function configureQuietMySQL(): void {
  if (isConfigured) {
    return;
  }

  // Store original methods
  originalConsoleLog = console.log;
  originalStdoutWrite = process.stdout.write;

  // Override console.log to filter MySQL2 verbose output
  console.log = (...args: any[]) => {
    const message = args.join(' ');
    
    // Filter out MySQL2 verbose patterns
    if (shouldSuppressMessage(message)) {
      return;
    }
    
    originalConsoleLog.apply(console, args);
  };

  // Override process.stdout.write to catch low-level output (with error handling)
  try {
    process.stdout.write = function(chunk: any, encoding?: any, callback?: any): boolean {
      const message = chunk.toString();

      if (shouldSuppressMessage(message)) {
        // Return true to indicate successful write (but don't actually write)
        if (typeof callback === 'function') {
          setImmediate(callback);
        }
        return true;
      }

      return originalStdoutWrite.call(process.stdout, chunk, encoding, callback);
    };
  } catch (error) {
    // If stdout override fails, continue without it
    console.warn('Could not override stdout.write for MySQL logging suppression');
  }

  // Disable debug modules that MySQL2 might use
  process.env.DEBUG = '';
  process.env.NODE_DEBUG = '';

  isConfigured = true;
}

/**
 * Restore original logging behavior
 */
export function restoreOriginalLogging(): void {
  if (!isConfigured) {
    return;
  }

  console.log = originalConsoleLog;
  process.stdout.write = originalStdoutWrite;
  isConfigured = false;
}

/**
 * Check if a message should be suppressed
 */
function shouldSuppressMessage(message: string): boolean {
  const suppressPatterns = [
    // Raw packet data
    /raw:\s+[0-9a-f]+/i,
    
    // Trace information from MySQL2
    /at\s+.*mysql2.*\.js:\d+:\d+/i,
    
    // ClientHandshake messages
    /ClientHandshake#unknown\s+name/i,
    /Add\s+command:\s+ClientHandshake/i,
    
    // Server hello packet details
    /Server\s+hello\s+packet:\s+capability\s+flags/i,
    
    // Sending handshake packet
    /Sending\s+handshake\s+packet:\s+flags/i,
    
    // Hex dump patterns (long strings of hex)
    /^[0-9a-f\s]{50,}$/i,
    
    // Packet direction indicators
    /^\d+\s+\d+\s+[<>=]+\s+/i,
    
    // MySQL2 configuration warnings (keep only essential ones)
    /Ignoring\s+invalid\s+configuration\s+option.*acquireTimeout/i,
    /Ignoring\s+invalid\s+configuration\s+option.*timeout/i,
    /Ignoring\s+invalid\s+configuration\s+option.*reconnect/i,
  ];

  return suppressPatterns.some(pattern => pattern.test(message));
}

/**
 * Check if quiet mode is enabled
 */
export function isQuietModeEnabled(): boolean {
  return isConfigured;
}
