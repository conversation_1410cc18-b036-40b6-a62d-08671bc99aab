import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>nt, Min } from 'class-validator';

export class CreateAnnualGoalDto {
  @IsNotEmpty({ message: 'O valor da meta anual é obrigatório' })
  @IsNumber({}, { message: 'O valor da meta anual deve ser um número' })
  @Min(0.01, { message: 'O valor da meta anual deve ser maior que zero' })
  goalAmount: number;

  @IsOptional()
  @IsInt({ message: 'O ano deve ser um número inteiro' })
  @Min(2000, { message: 'O ano deve ser maior que 2000' })
  year?: number;
}
