export class FinanceResponseDto {
  id: number;
  transaction_type: 'income' | 'expense';
  category_id?: number;
  category_name?: string;
  is_saving?: boolean;
  description?: string;
  amount: string; // DECIMAL como string
  transaction_date: Date;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}

export class FinanceListResponseDto {
  finances: FinanceResponseDto[];
  total: number;
  page: number;
  limit: number;
}

export class FinanceSummaryDto {
  totalIncome: number;
  totalExpenses: number;
  totalSavings: number;
  balance: number;
  period: {
    start: Date;
    end: Date;
  };
}
