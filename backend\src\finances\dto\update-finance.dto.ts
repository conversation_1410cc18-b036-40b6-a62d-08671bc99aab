import { IsOptional, IsString, IsEnum, IsDateString, IsNumber, IsBoolean } from 'class-validator';

export class UpdateFinanceDto {
  @IsOptional()
  @IsEnum(['income', 'expense'])
  transaction_type?: 'income' | 'expense';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsBoolean()
  is_saving?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  amount?: string; // DECIMAL como string

  @IsOptional()
  @IsDateString()
  transaction_date?: string;
}
