import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { FinancesController } from './finances.controller';
import { FinancesService } from './finances.service';
import { RepositoriesModule } from '../repositories/repositories.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: { expiresIn: configService.get('JWT_EXPIRES_IN') },
      }),
      inject: [ConfigService],
    }),
    RepositoriesModule,
  ],
  controllers: [FinancesController],
  providers: [FinancesService],
  exports: [FinancesService]
})
export class FinancesModule {}
