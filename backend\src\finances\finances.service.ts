import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
import { CreateFinanceCategoryDto } from './dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from './dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from './dto/finance-category-response.dto';
import { FinanceRepository } from '../repositories/finance.repository';
import { FinanceCategoryRepository } from '../repositories/finance-category.repository';

@Injectable()
export class FinancesService {
  private readonly logger = new Logger(FinancesService.name);

  constructor(
    private readonly financeRepository: FinanceRepository,
    private readonly financeCategoryRepository: FinanceCategoryRepository,
  ) {}

  // Métodos para transações financeiras
  async create(createFinanceDto: CreateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto> {
    return this.financeRepository.create(createFinanceDto, userId, userTimezone);
  }

  async findAll(userId: number, userTimezone: string, page = 1, limit = 50): Promise<FinanceListResponseDto> {
    const result = await this.financeRepository.findAllWithCategory(userId, userTimezone, page, limit);
    return {
      finances: result.data,
      total: result.total,
      page: result.page,
      limit: result.limit
    };
  }

  async findOne(id: number, userId: number, userTimezone: string): Promise<FinanceResponseDto> {
    return this.financeRepository.findOne(id, userId, userTimezone);
  }

  async update(id: number, updateFinanceDto: UpdateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto> {
    return this.financeRepository.update(id, updateFinanceDto, userId, userTimezone);
  }

  async remove(id: number, userId: number): Promise<void> {
    return this.financeRepository.remove(id, userId);
  }

  async getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto> {
    return this.financeRepository.getSummary(userId, userTimezone, startDate, endDate);
  }

  async getExpensesByCategory(userId: number, startDate?: Date, endDate?: Date): Promise<{ category: string; amount: number; count: number }[]> {
    return this.financeRepository.getExpensesByCategory(userId, startDate, endDate);
  }

  async getAnnualSummary(userId: number, year: number, userTimezone: string) {
    return this.financeRepository.getAnnualSummary(userId, year, userTimezone);
  }

  async getAnnualCategoryDistribution(userId: number, year: number, userTimezone: string) {
    return this.financeRepository.getAnnualCategoryDistribution(userId, year, userTimezone);
  }

  // Métodos para categorias financeiras
  async createCategory(createCategoryDto: CreateFinanceCategoryDto, userId: number): Promise<FinanceCategoryResponseDto> {
    return this.financeCategoryRepository.create(createCategoryDto, userId);
  }

  async findAllCategories(userId: number): Promise<FinanceCategoryResponseDto[]> {
    const result = await this.financeCategoryRepository.findAllOrderedByType(userId, 'America/Sao_Paulo');
    return result;
  }

  async findOneCategory(id: number, userId: number): Promise<FinanceCategoryResponseDto> {
    return this.financeCategoryRepository.findOne(id, userId);
  }

  async updateCategory(id: number, updateCategoryDto: UpdateFinanceCategoryDto, userId: number): Promise<FinanceCategoryResponseDto> {
    return this.financeCategoryRepository.update(id, updateCategoryDto, userId);
  }

  async removeCategory(id: number, userId: number): Promise<void> {
    // Verificar se a categoria está sendo usada
    const isInUse = await this.financeCategoryRepository.checkCategoryInUse(id, userId);
    
    if (isInUse) {
      throw new HttpException(
        'Não é possível remover categoria que está sendo usada por transações',
        HttpStatus.BAD_REQUEST
      );
    }

    return this.financeCategoryRepository.remove(id, userId);
  }
}