import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class UpdateGoogleCalendarIntegrationDto {
  @IsEmail({}, { message: 'Email deve ter um formato válido' })
  calendar_email: string;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsBoolean()
  sync_tasks?: boolean;

  @IsOptional()
  @IsBoolean()
  sync_appointments?: boolean;

  @IsOptional()
  @IsNumber({}, { message: 'Lembre<PERSON> deve ser um número' })
  @Min(0, { message: '<PERSON>mbre<PERSON> deve ser no mínimo 0 minutos' })
  @Max(10080, { message: '<PERSON>mbrete deve ser no máximo 7 dias (10080 minutos)' })
  default_reminder_minutes?: number;
}

export class GoogleCalendarIntegrationResponseDto {
  id: number;
  calendar_email: string;
  is_active: boolean;
  sync_tasks: boolean;
  sync_appointments: boolean;
  default_reminder_minutes: number;
  created_at: Date;
  updated_at: Date;
}
