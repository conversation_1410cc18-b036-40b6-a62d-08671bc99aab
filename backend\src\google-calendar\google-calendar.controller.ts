import {
  Controller,
  Get,
  Put,
  Delete,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GoogleCalendarService } from './google-calendar.service';
import {
  UpdateGoogleCalendarIntegrationDto,
  GoogleCalendarIntegrationResponseDto,
} from './dto/google-calendar.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';

@Controller('google-calendar')
@UseGuards(AuthGuard('jwt'))
export class GoogleCalendarController {
  constructor(private readonly googleCalendarService: GoogleCalendarService) {}

  // Obter integração do Google Calendar
  @Get()
  @HttpCode(HttpStatus.OK)
  async getIntegration(@Request() req): Promise<GoogleCalendarIntegrationResponseDto | null> {
    return this.googleCalendarService.getIntegration(req.user.userId);
  }

  // Configurar/atualizar integração do Google Calendar
  @Put()
  @HttpCode(HttpStatus.OK)
  async updateIntegration(
    @Request() req,
    @Body() updateDto: UpdateGoogleCalendarIntegrationDto,
  ): Promise<GoogleCalendarIntegrationResponseDto> {
    return this.googleCalendarService.updateIntegration(req.user.userId, updateDto);
  }

  // Remover integração do Google Calendar
  @Delete()
  @HttpCode(HttpStatus.OK)
  async deleteIntegration(@Request() req): Promise<SuccessResponseDto> {
    await this.googleCalendarService.deleteIntegration(req.user.userId);
    return {
      success: true,
      message: 'Integração com Google Calendar removida com sucesso',
      timestamp: new Date().toISOString(),
    };
  }
}
