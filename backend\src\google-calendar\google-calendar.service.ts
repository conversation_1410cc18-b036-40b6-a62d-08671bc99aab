import {
  Injectable,
  NotFoundException,
  Inject,
} from '@nestjs/common';
import { Connection } from 'mysql2/promise';
import {
  UpdateGoogleCalendarIntegrationDto,
  GoogleCalendarIntegrationResponseDto,
} from './dto/google-calendar.dto';
import { MYSQL2_CONNECTION } from '../database/database.provider';

@Injectable()
export class GoogleCalendarService {
  constructor(@Inject(MYSQL2_CONNECTION) private connection: Connection) {}

  async getIntegration(userId: number): Promise<GoogleCalendarIntegrationResponseDto | null> {
    const [rows] = await this.connection.execute(
      `SELECT id, calendar_email, is_active, sync_tasks, sync_appointments,
              default_reminder_minutes, created_at, updated_at
       FROM google_calendar_integration 
       WHERE user_id = ?`,
      [userId]
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      return null;
    }

    const integration = rows[0] as any;
    return {
      id: integration.id,
      calendar_email: integration.calendar_email,
      is_active: integration.is_active === 1,
      sync_tasks: integration.sync_tasks === 1,
      sync_appointments: integration.sync_appointments === 1,
      default_reminder_minutes: integration.default_reminder_minutes,
      created_at: integration.created_at,
      updated_at: integration.updated_at,
    };
  }

  async updateIntegration(
    userId: number,
    updateDto: UpdateGoogleCalendarIntegrationDto
  ): Promise<GoogleCalendarIntegrationResponseDto> {
    // Verificar se já existe integração
    const existing = await this.getIntegration(userId);
    
    if (!existing) {
      // Criar nova integração
      const [result] = await this.connection.execute(
        `INSERT INTO google_calendar_integration 
         (user_id, calendar_email, is_active, sync_tasks, sync_appointments, default_reminder_minutes)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          userId,
          updateDto.calendar_email,
          updateDto.is_active !== false ? 1 : 0,
          updateDto.sync_tasks !== false ? 1 : 0,
          updateDto.sync_appointments !== false ? 1 : 0,
          updateDto.default_reminder_minutes || 15,
        ]
      );

      const insertId = (result as any).insertId;
      return this.getIntegrationById(insertId);
    } else {
      // Atualizar integração existente
      const updateFields: string[] = [];
      const values: any[] = [];

      updateFields.push('calendar_email = ?');
      values.push(updateDto.calendar_email);

      if (typeof updateDto.is_active === 'boolean') {
        updateFields.push('is_active = ?');
        values.push(updateDto.is_active ? 1 : 0);
      }

      if (typeof updateDto.sync_tasks === 'boolean') {
        updateFields.push('sync_tasks = ?');
        values.push(updateDto.sync_tasks ? 1 : 0);
      }

      if (typeof updateDto.sync_appointments === 'boolean') {
        updateFields.push('sync_appointments = ?');
        values.push(updateDto.sync_appointments ? 1 : 0);
      }

      if (updateDto.default_reminder_minutes !== undefined) {
        updateFields.push('default_reminder_minutes = ?');
        values.push(updateDto.default_reminder_minutes);
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(userId);

      await this.connection.execute(
        `UPDATE google_calendar_integration 
         SET ${updateFields.join(', ')} 
         WHERE user_id = ?`,
        values
      );

      const updatedIntegration = await this.getIntegration(userId);
      return updatedIntegration!;
    }
  }

  private async getIntegrationById(id: number): Promise<GoogleCalendarIntegrationResponseDto> {
    const [rows] = await this.connection.execute(
      `SELECT id, calendar_email, is_active, sync_tasks, sync_appointments,
              default_reminder_minutes, created_at, updated_at
       FROM google_calendar_integration 
       WHERE id = ?`,
      [id]
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      throw new NotFoundException('Integração não encontrada');
    }

    const integration = rows[0] as any;
    return {
      id: integration.id,
      calendar_email: integration.calendar_email,
      is_active: integration.is_active === 1,
      sync_tasks: integration.sync_tasks === 1,
      sync_appointments: integration.sync_appointments === 1,
      default_reminder_minutes: integration.default_reminder_minutes,
      created_at: integration.created_at,
      updated_at: integration.updated_at,
    };
  }

  async deleteIntegration(userId: number): Promise<void> {
    const [result] = await this.connection.execute(
      'DELETE FROM google_calendar_integration WHERE user_id = ?',
      [userId]
    );

    if ((result as any).affectedRows === 0) {
      throw new NotFoundException('Integração não encontrada');
    }
  }
}
