import { Controller, Get, HttpStatus, HttpCode } from '@nestjs/common';
import { DatabaseHealthService } from '../database/database-health.service';

@Controller('health')
export class HealthController {
  constructor(private readonly databaseHealthService: DatabaseHealthService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  async getHealthStatus() {
    const dbHealth = this.databaseHealthService.getHealthStatus();
    const connectionTest = await this.databaseHealthService.testConnection();
    
    const overallHealth = dbHealth.isHealthy && connectionTest.success;
    
    return {
      status: overallHealth ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: dbHealth.isHealthy ? 'healthy' : 'unhealthy',
          lastCheck: dbHealth.lastHealthCheck,
          consecutiveFailures: dbHealth.consecutiveFailures,
          connectionTest: {
            success: connectionTest.success,
            duration: `${connectionTest.duration}ms`,
            error: connectionTest.error,
          },
        },
      },
    };
  }

  @Get('database')
  @HttpCode(HttpStatus.OK)
  async getDatabaseHealth() {
    const dbHealth = this.databaseHealthService.getHealthStatus();
    const connectionTest = await this.databaseHealthService.testConnection();
    
    return {
      status: dbHealth.isHealthy && connectionTest.success ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      lastHealthCheck: dbHealth.lastHealthCheck,
      consecutiveFailures: dbHealth.consecutiveFailures,
      connectionTest: {
        success: connectionTest.success,
        duration: `${connectionTest.duration}ms`,
        error: connectionTest.error,
      },
    };
  }
}
