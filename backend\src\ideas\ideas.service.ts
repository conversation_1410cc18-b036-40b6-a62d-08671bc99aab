import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { CreateIdeaDto } from './dto/create-idea.dto';
import { UpdateIdeaDto } from './dto/update-idea.dto';
import { IdeaResponseDto, IdeaListResponseDto } from './dto/idea-response.dto';
import { CreateIdeaCategoryDto } from './dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from './dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from './dto/idea-category-response.dto';
import { IdeaRepository } from '../repositories/idea.repository';
import { IdeaCategoryRepository } from '../repositories/idea-category.repository';

@Injectable()
export class IdeasService {
  private readonly logger = new Logger(IdeasService.name);

  constructor(
    private readonly ideaRepository: IdeaRepository,
    private readonly ideaCategoryRepository: IdeaCategoryRepository,
  ) {}

  // Métodos para ideias
  async create(createIdeaDto: CreateIdeaDto, userId: number): Promise<IdeaResponseDto> {
    return this.ideaRepository.create(createIdeaDto, userId);
  }

  async findAll(userId: number, userTimezone: string, page = 1, limit = 50): Promise<IdeaListResponseDto> {
    const result = await this.ideaRepository.findAllWithCategory(userId, userTimezone, page, limit);
    return {
      ideas: result.data,
      total: result.total,
      page: result.page,
      limit: result.limit
    };
  }

  async findOne(id: number, userId: number, userTimezone = 'America/Sao_Paulo'): Promise<IdeaResponseDto> {
    return this.ideaRepository.findOne(id, userId, userTimezone);
  }

  async update(id: number, updateIdeaDto: UpdateIdeaDto, userId: number, userTimezone = 'America/Sao_Paulo'): Promise<IdeaResponseDto> {
    return this.ideaRepository.update(id, updateIdeaDto, userId, userTimezone);
  }

  async remove(id: number, userId: number): Promise<void> {
    return this.ideaRepository.remove(id, userId);
  }

  async toggleFavorite(id: number, userId: number, userTimezone = 'America/Sao_Paulo'): Promise<IdeaResponseDto> {
    return this.ideaRepository.toggleFavorite(id, userId);
  }

  // Métodos para categorias de ideias
  async createCategory(createCategoryDto: CreateIdeaCategoryDto, userId: number): Promise<IdeaCategoryResponseDto> {
    return this.ideaCategoryRepository.create(createCategoryDto, userId);
  }

  async findAllCategories(userId: number): Promise<IdeaCategoryResponseDto[]> {
    return this.ideaCategoryRepository.findAllOrderedByName(userId);
  }

  async findOneCategory(id: number, userId: number): Promise<IdeaCategoryResponseDto> {
    return this.ideaCategoryRepository.findOne(id, userId);
  }

  async updateCategory(id: number, updateCategoryDto: UpdateIdeaCategoryDto, userId: number): Promise<IdeaCategoryResponseDto> {
    return this.ideaCategoryRepository.update(id, updateCategoryDto, userId);
  }

  async removeCategory(id: number, userId: number): Promise<void> {
    // Verificar se a categoria está sendo usada
    const isInUse = await this.ideaCategoryRepository.checkCategoryInUse(id, userId);
    
    if (isInUse) {
      throw new HttpException(
        'Não é possível remover categoria que está sendo usada por ideias',
        HttpStatus.BAD_REQUEST
      );
    }

    return this.ideaCategoryRepository.remove(id, userId);
  }
}