import { IsNotEmpty, IsString, IsBoolean, IsEnum, IsOptional } from 'class-validator';

export class CreateWhatsAppIntegrationDto {
  // Phone não é mais obrigatório na criação, será gerado um código de ativação
}

export class ValidateWhatsAppIntegrationDto {
  @IsNotEmpty({ 
    message: 'Campo "activation_code" é obrigatório. Código de ativação é necessário para validar a integração.' 
  })
  @IsString({ 
    message: 'Campo "activation_code" deve ser uma string.' 
  })
  activation_code: string;

  @IsNotEmpty({ 
    message: 'Campo "phone" é obrigatório. Forneça um número de telefone válido para identificar o usuário.' 
  })
  @IsString({ 
    message: 'Campo "phone" deve ser uma string. Exemplo: "+5511999999999" ou "11999999999"' 
  })
  phone: string;
}

export class UpdateWhatsAppIntegrationDto {
  @IsOptional()
  @IsEnum(['pending', 'active', 'inactive'])
  status?: 'pending' | 'active' | 'inactive';

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsBoolean()
  is_validated?: boolean;
}

export class WhatsAppIntegrationResponseDto {
  id: number;
  status: 'pending' | 'active' | 'inactive';
  phone?: string | null;
  is_validated: boolean;
  activation_code?: string | null;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}
