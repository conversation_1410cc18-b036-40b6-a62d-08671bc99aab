import { <PERSON>, Get, Post, Body, Patch, Param, Delete, ParseIntPipe, UseGuards, Request } from '@nestjs/common';
import { DevAwareAuthGuard } from '../auth/dev-auth.guard';
import { IntegrationsService } from './integrations.service';
import { CreateWhatsAppIntegrationDto, UpdateWhatsAppIntegrationDto, ValidateWhatsAppIntegrationDto } from './dto/whatsapp-integration.dto';

@Controller('integrations')
@UseGuards(DevAwareAuthGuard)
export class IntegrationsController {
  constructor(private readonly integrationsService: IntegrationsService) {}

  @Post('whatsapp')
  create(@Request() req: any, @Body() createWhatsAppIntegrationDto: CreateWhatsAppIntegrationDto) {
    return this.integrationsService.createWhatsAppIntegration(createWhatsAppIntegrationDto, req.user.userId || req.user.id);
  }

  @Get('whatsapp')
  findUserIntegration(@Request() req: any) {
    return this.integrationsService.findWhatsAppIntegrationByUserId(req.user.userId || req.user.id);
  }

  @Get('whatsapp/:id')
  findOne(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    return this.integrationsService.findOneWhatsAppIntegration(id);
  }

  @Patch('whatsapp/:id')
  update(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateWhatsAppIntegrationDto: UpdateWhatsAppIntegrationDto,
  ) {
    return this.integrationsService.updateWhatsAppIntegration(id, updateWhatsAppIntegrationDto, req.user.userId || req.user.id);
  }

  @Post('whatsapp/:id/validate')
  validate(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    return this.integrationsService.validateWhatsAppIntegration(id);
  }

  @Delete('whatsapp/:id')
  remove(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    return this.integrationsService.removeWhatsAppIntegration(id, req.user.userId || req.user.id);
  }

  @Post('whatsapp/validate-code')
  validateWithCode(@Request() req: any, @Body() validateDto: ValidateWhatsAppIntegrationDto) {
    return this.integrationsService.validateWithActivationCode(validateDto);
  }

  @Get('whatsapp/:id/whatsapp-url')
  getWhatsAppUrl(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    // Buscar o código de ativação da integração
    return this.integrationsService.findOneWhatsAppIntegration(id)
      .then(integration => {
        if (!integration.activation_code) {
          throw new Error('Integração já foi validada ou não possui código de ativação');
        }
        return {
          whatsapp_url: this.integrationsService.getWhatsAppUrl(integration.activation_code),
          activation_code: integration.activation_code
        };
      });
  }
}
