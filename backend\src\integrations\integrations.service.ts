import { Injectable, NotFoundException, Logger, ConflictException } from '@nestjs/common';
import { db } from '../database.types';
import { CreateWhatsAppIntegrationDto, UpdateWhatsAppIntegrationDto, WhatsAppIntegrationResponseDto, ValidateWhatsAppIntegrationDto } from './dto/whatsapp-integration.dto';
import * as crypto from 'crypto';

@Injectable()
export class IntegrationsService {
  private readonly logger = new Logger(IntegrationsService.name);
  private db = db;

  async createWhatsAppIntegration(
    createWhatsAppIntegrationDto: CreateWhatsAppIntegrationDto,
    userId: number
  ): Promise<WhatsAppIntegrationResponseDto> {
    try {
      this.logger.log(`Criando integração WhatsApp para usuário ${userId}`);

      // Verificar se já existe uma integração ativa para este usuário
      const existingIntegration = await this.db
        .selectFrom('integrations_whatsapp')
        .selectAll()
        .where('user_id', '=', userId)
        .where('status', 'in', ['pending', 'active'])
        .executeTakeFirst();

      if (existingIntegration) {
        throw new ConflictException('Usuário já possui uma integração WhatsApp ativa ou pendente');
      }

      // Gerar código de ativação
      const activationCode = this.generateActivationCode(userId);

      // Criar nova integração com código de ativação
      const insertResult = await this.db
        .insertInto('integrations_whatsapp')
        .values({
          phone: null, // Phone será null até a validação
          status: 'pending',
          is_validated: false,
          activation_code: activationCode,
          user_id: userId,
        })
        .executeTakeFirst();

      const integrationId = Number(insertResult.insertId);

      // Buscar a integração criada
      const newIntegration = await this.findOneWhatsAppIntegration(integrationId);

      this.logger.log(`Integração WhatsApp criada com sucesso - ID: ${integrationId}, Código: ${activationCode}`);
      return newIntegration;
    } catch (error) {
      this.logger.error(`Erro ao criar integração WhatsApp:`, error);
      throw error;
    }
  }

  async findOneWhatsAppIntegration(id: number): Promise<WhatsAppIntegrationResponseDto> {
    try {
      const integration = await this.db
        .selectFrom('integrations_whatsapp')
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();

      if (!integration) {
        throw new NotFoundException(`Integração WhatsApp com ID ${id} não encontrada`);
      }

      return {
        id: integration.id,
        status: integration.status as 'pending' | 'active' | 'inactive',
        phone: integration.phone,
        is_validated: Boolean(integration.is_validated),
        activation_code: integration.activation_code,
        user_id: integration.user_id,
        created_at: new Date(),
        updated_at: new Date(),
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar integração WhatsApp ${id}:`, error);
      throw error;
    }
  }

  async findWhatsAppIntegrationByUserId(userId: number): Promise<WhatsAppIntegrationResponseDto | null> {
    try {
      const integration = await this.db
        .selectFrom('integrations_whatsapp')
        .selectAll()
        .where('user_id', '=', userId)
        .where('status', '!=', 'inactive')
        .executeTakeFirst();

      if (!integration) {
        return null;
      }

      return {
        id: integration.id,
        status: integration.status as 'pending' | 'active' | 'inactive',
        phone: integration.phone,
        is_validated: Boolean(integration.is_validated),
        activation_code: integration.activation_code,
        user_id: integration.user_id,
        created_at: new Date(),
        updated_at: new Date(),
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar integração WhatsApp do usuário ${userId}:`, error);
      throw error;
    }
  }

  async updateWhatsAppIntegration(
    id: number,
    updateWhatsAppIntegrationDto: UpdateWhatsAppIntegrationDto,
    userId?: number
  ): Promise<WhatsAppIntegrationResponseDto> {
    try {
      this.logger.log(`Atualizando integração WhatsApp ${id}`);

      // Verificar se a integração existe
      const existingIntegration = await this.findOneWhatsAppIntegration(id);

      // Se userId foi fornecido, verificar se a integração pertence ao usuário
      if (userId && existingIntegration.user_id !== userId) {
        throw new NotFoundException('Integração não encontrada para este usuário');
      }

      // Validar telefone se foi fornecido
      if (updateWhatsAppIntegrationDto.phone) {
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,20}$/;
        if (!phoneRegex.test(updateWhatsAppIntegrationDto.phone)) {
          throw new Error('Formato de telefone inválido');
        }
      }

      // Atualizar integração
      await this.db
        .updateTable('integrations_whatsapp')
        .set(updateWhatsAppIntegrationDto)
        .where('id', '=', id)
        .execute();

      // Retornar integração atualizada
      const updatedIntegration = await this.findOneWhatsAppIntegration(id);

      this.logger.log(`Integração WhatsApp ${id} atualizada com sucesso`);
      return updatedIntegration;
    } catch (error) {
      this.logger.error(`Erro ao atualizar integração WhatsApp ${id}:`, error);
      throw error;
    }
  }

  async removeWhatsAppIntegration(id: number, userId?: number): Promise<void> {
    try {
      this.logger.log(`Removendo integração WhatsApp ${id}`);

      // Verificar se a integração existe
      const existingIntegration = await this.findOneWhatsAppIntegration(id);

      // Se userId foi fornecido, verificar se a integração pertence ao usuário
      if (userId && existingIntegration.user_id !== userId) {
        throw new NotFoundException('Integração não encontrada para este usuário');
      }

      // Remover integração (soft delete definindo status como inactive)
      await this.db
        .updateTable('integrations_whatsapp')
        .set({ status: 'inactive' })
        .where('id', '=', id)
        .execute();

      this.logger.log(`Integração WhatsApp ${id} removida com sucesso`);
    } catch (error) {
      this.logger.error(`Erro ao remover integração WhatsApp ${id}:`, error);
      throw error;
    }
  }

  async validateWhatsAppIntegration(id: number): Promise<WhatsAppIntegrationResponseDto> {
    try {
      this.logger.log(`Validando integração WhatsApp ${id}`);

      // Aqui você implementaria a lógica de validação real
      // Por exemplo, enviar um código via WhatsApp e verificar

      const updatedIntegration = await this.updateWhatsAppIntegration(id, {
        status: 'active',
        is_validated: true,
      });

      this.logger.log(`Integração WhatsApp ${id} validada com sucesso`);
      return updatedIntegration;
    } catch (error) {
      this.logger.error(`Erro ao validar integração WhatsApp ${id}:`, error);
      throw error;
    }
  }

  async validateWithActivationCode(validateDto: ValidateWhatsAppIntegrationDto): Promise<WhatsAppIntegrationResponseDto> {
    try {
      this.logger.log(`Validando integração com código: ${validateDto.activation_code}`);

      // Buscar integração pelo código de ativação
      const integration = await this.db
        .selectFrom('integrations_whatsapp')
        .selectAll()
        .where('activation_code', '=', validateDto.activation_code)
        .where('status', '=', 'pending')
        .executeTakeFirst();

      if (!integration) {
        throw new NotFoundException('Código de ativação inválido ou expirado');
      }

      // Validar formato do telefone
      const phoneRegex = /^\+?[\d\s\-\(\)]{10,20}$/;
      if (!phoneRegex.test(validateDto.phone)) {
        throw new Error('Formato de telefone inválido');
      }

      // Atualizar a integração com o número validado
      await this.db
        .updateTable('integrations_whatsapp')
        .set({
          phone: validateDto.phone,
          status: 'active',
          is_validated: true,
          activation_code: null // Limpar o código após a validação
        })
        .where('id', '=', integration.id)
        .execute();

      const updatedIntegration = await this.findOneWhatsAppIntegration(integration.id);

      this.logger.log(`Integração WhatsApp validada com sucesso para o número: ${validateDto.phone}`);
      return updatedIntegration;
    } catch (error) {
      this.logger.error(`Erro ao validar integração WhatsApp com código:`, error);
      throw error;
    }
  }

  getWhatsAppUrl(activationCode: string): string {
    const whatsappNumber = process.env.WHATSAPP_DEFAULT_NUMBER || '5511999999999';
    const message = encodeURIComponent(activationCode);
    return `https://wa.me/${whatsappNumber}?text=${message}`;
  }

  private generateActivationCode(userId: number | null = null): string {
    // Gerar uma chave aleatória de 8 caracteres
    const randomKey = crypto.randomBytes(4).toString('hex').toUpperCase();
    return `start-dupli-${userId}${randomKey}`;
  }
}
