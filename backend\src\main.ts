import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { CorsInterceptor } from './common/cors.interceptor';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { GlobalValidationPipe } from './common/pipes/global-validation.pipe';

async function bootstrap() {
  // Disable MySQL2 debug output unless explicitly enabled
  if (process.env.MYSQL_VERBOSE_LOGS !== 'true') {
    process.env.DEBUG = '';
    process.env.NODE_DEBUG = '';
  }
  const app = await NestFactory.create(AppModule);
  
  // Middleware para lidar com OPTIONS antes de qualquer guard
  app.use((req, res, next) => {
    if (req.method === 'OPTIONS') {
      res.header('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || 'http://localhost:5173');
      res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-Device-UUID,X-Dev-Mode,X-Dev-User-Id');
      res.header('Access-Control-Allow-Credentials', 'true');
      res.header('Access-Control-Max-Age', '3600');
      return res.status(204).send();
    }
    next();
  });

  // Configuração CORS
  app.enableCors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Device-UUID',
      'X-Dev-Mode',
      'X-Dev-User-Id'
    ],
    preflightContinue: false,
    optionsSuccessStatus: 204,
    credentials: true
  });

  app.useGlobalPipes(new GlobalValidationPipe());

  // Usar filtros e interceptores globais
  app.useGlobalFilters(new GlobalExceptionFilter());
  app.useGlobalInterceptors(
    new CorsInterceptor(),
    new ResponseInterceptor()
  );

  const port = process.env.PORT || 3000;
  await app.listen(port);
  console.log(`🚀 Backend rodando na porta ${port}`);
  console.log(`📡 CORS habilitado para: http://localhost:5173`);
}
bootstrap();
