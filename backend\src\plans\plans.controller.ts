import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PlansService } from './plans.service';
import {
  PlanResponseDto,
  UserSubscriptionResponseDto,
  CreateSubscriptionDto,
} from './dto/plans.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';

@Controller('plans')
@UseGuards(AuthGuard('jwt'))
export class PlansController {
  constructor(private readonly plansService: PlansService) {}

  // Listar todos os planos disponíveis
  @Get()
  @HttpCode(HttpStatus.OK)
  async getAllPlans(): Promise<PlanResponseDto[]> {
    return this.plansService.getAllPlans();
  }

  // Obter assinatura atual do usuário
  @Get('my-subscription')
  @HttpCode(HttpStatus.OK)
  async getMySubscription(@Request() req): Promise<UserSubscriptionResponseDto | null> {
    return this.plansService.getUserSubscription(req.user.userId);
  }

  // Criar nova assinatura
  @Post('subscribe')
  @HttpCode(HttpStatus.CREATED)
  async subscribe(
    @Request() req,
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<UserSubscriptionResponseDto> {
    return this.plansService.createSubscription(req.user.userId, createSubscriptionDto);
  }

  // Cancelar assinatura
  @Delete('cancel')
  @HttpCode(HttpStatus.OK)
  async cancelSubscription(@Request() req): Promise<SuccessResponseDto> {
    await this.plansService.cancelSubscription(req.user.userId);
    return {
      success: true,
      message: 'Assinatura cancelada com sucesso',
      timestamp: new Date().toISOString(),
    };
  }
}
