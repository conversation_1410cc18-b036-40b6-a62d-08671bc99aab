import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { Connection } from 'mysql2/promise';
import {
  PlanResponseDto,
  UserSubscriptionResponseDto,
  CreateSubscriptionDto,
} from './dto/plans.dto';
import { MYSQL2_CONNECTION } from '../database/database.provider';

@Injectable()
export class PlansService {
  constructor(@Inject(MYSQL2_CONNECTION) private connection: Connection) {}

  async getAllPlans(): Promise<PlanResponseDto[]> {
    const [rows] = await this.connection.execute(
      `SELECT id, name, slug, description, price, currency, billing_period, 
              features, is_active, sort_order, created_at 
       FROM plans 
       WHERE is_active = 1 
       ORDER BY sort_order ASC, price ASC`
    );

    return (rows as any[]).map(row => ({
      id: row.id,
      name: row.name,
      slug: row.slug,
      description: row.description,
      price: parseFloat(row.price),
      currency: row.currency,
      billing_period: row.billing_period,
      features: row.features ? (typeof row.features === 'string' ? JSON.parse(row.features) : row.features) : [],
      is_active: row.is_active === 1,
      sort_order: row.sort_order,
      created_at: row.created_at,
    }));
  }

  async getUserSubscription(userId: number): Promise<UserSubscriptionResponseDto | null> {
    const [rows] = await this.connection.execute(
      `SELECT 
         us.id, us.status, us.current_period_start, us.current_period_end,
         us.canceled_at, us.ends_at, us.created_at,
         p.id as plan_id, p.name as plan_name, p.slug as plan_slug,
         p.description as plan_description, p.price as plan_price,
         p.currency as plan_currency, p.billing_period as plan_billing_period,
         p.features as plan_features, p.is_active as plan_is_active,
         p.sort_order as plan_sort_order, p.created_at as plan_created_at
       FROM user_subscriptions us
       INNER JOIN plans p ON us.plan_id = p.id
       WHERE us.user_id = ? AND us.status IN ('active', 'past_due')
       ORDER BY us.created_at DESC
       LIMIT 1`,
      [userId]
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      return null;
    }

    const row = rows[0] as any;
    return {
      id: row.id,
      plan: {
        id: row.plan_id,
        name: row.plan_name,
        slug: row.plan_slug,
        description: row.plan_description,
        price: parseFloat(row.plan_price),
        currency: row.plan_currency,
        billing_period: row.plan_billing_period,
        features: row.plan_features ? (typeof row.plan_features === 'string'
          ? JSON.parse(row.plan_features)
          : row.plan_features) : [],
        is_active: row.plan_is_active === 1,
        sort_order: row.plan_sort_order,
        created_at: row.plan_created_at,
      },
      status: row.status,
      current_period_start: row.current_period_start,
      current_period_end: row.current_period_end,
      canceled_at: row.canceled_at,
      ends_at: row.ends_at,
      created_at: row.created_at,
    };
  }

  async createSubscription(
    userId: number,
    createSubscriptionDto: CreateSubscriptionDto
  ): Promise<UserSubscriptionResponseDto> {
    // Verificar se o plano existe
    const [planRows] = await this.connection.execute(
      'SELECT * FROM plans WHERE id = ? AND is_active = 1',
      [createSubscriptionDto.plan_id]
    );

    if (!Array.isArray(planRows) || planRows.length === 0) {
      throw new NotFoundException('Plano não encontrado ou inativo');
    }

    // Verificar se o usuário já tem uma assinatura ativa
    const existingSubscription = await this.getUserSubscription(userId);
    if (existingSubscription) {
      throw new BadRequestException('Usuário já possui uma assinatura ativa');
    }

    // Criar nova assinatura
    const currentDate = new Date();
    const nextBillingDate = new Date();
    const plan = planRows[0] as any;
    
    if (plan.billing_period === 'monthly') {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    } else if (plan.billing_period === 'yearly') {
      nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
    }

    const [result] = await this.connection.execute(
      `INSERT INTO user_subscriptions 
       (user_id, plan_id, status, current_period_start, current_period_end) 
       VALUES (?, ?, 'active', ?, ?)`,
      [userId, createSubscriptionDto.plan_id, currentDate, nextBillingDate]
    );

    const subscriptionId = (result as any).insertId;
    
    // Buscar a assinatura criada
    const newSubscription = await this.connection.execute(
      `SELECT 
         us.id, us.status, us.current_period_start, us.current_period_end,
         us.canceled_at, us.ends_at, us.created_at,
         p.id as plan_id, p.name as plan_name, p.slug as plan_slug,
         p.description as plan_description, p.price as plan_price,
         p.currency as plan_currency, p.billing_period as plan_billing_period,
         p.features as plan_features, p.is_active as plan_is_active,
         p.sort_order as plan_sort_order, p.created_at as plan_created_at
       FROM user_subscriptions us
       INNER JOIN plans p ON us.plan_id = p.id
       WHERE us.id = ?`,
      [subscriptionId]
    );

    const row = (newSubscription[0] as any[])[0];
    return {
      id: row.id,
      plan: {
        id: row.plan_id,
        name: row.plan_name,
        slug: row.plan_slug,
        description: row.plan_description,
        price: parseFloat(row.plan_price),
        currency: row.plan_currency,
        billing_period: row.plan_billing_period,
        features: row.plan_features ? (typeof row.plan_features === 'string'
          ? JSON.parse(row.plan_features)
          : row.plan_features) : [],
        is_active: row.plan_is_active === 1,
        sort_order: row.plan_sort_order,
        created_at: row.plan_created_at,
      },
      status: row.status,
      current_period_start: row.current_period_start,
      current_period_end: row.current_period_end,
      canceled_at: row.canceled_at,
      ends_at: row.ends_at,
      created_at: row.created_at,
    };
  }

  async cancelSubscription(userId: number): Promise<void> {
    const subscription = await this.getUserSubscription(userId);
    
    if (!subscription) {
      throw new NotFoundException('Nenhuma assinatura ativa encontrada');
    }

    await this.connection.execute(
      `UPDATE user_subscriptions 
       SET status = 'canceled', canceled_at = CURRENT_TIMESTAMP 
       WHERE id = ? AND user_id = ?`,
      [subscription.id, userId]
    );
  }
}
