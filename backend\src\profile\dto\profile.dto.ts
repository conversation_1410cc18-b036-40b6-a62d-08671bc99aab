import { IsString, <PERSON>NotEmpty, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, <PERSON>E<PERSON> } from 'class-validator';

// DTO para atualizar informações pessoais
export class UpdatePersonalInfoDto {
    @IsNotEmpty()
    @IsString()
    name: string;

    @IsEmail()
    email: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsString()
    timezone?: string;
}

// DTO para alterar senha
export class ChangePasswordDto {
    @IsString()
    @IsNotEmpty()
    currentPassword: string;

    @IsString()
    @IsNotEmpty()
    @MinLength(6, { message: 'A nova senha deve ter pelo menos 6 caracteres' })
    newPassword: string;

    @IsString()
    @IsNotEmpty()
    confirmPassword: string;
}

// Enum para humor da IA
export enum AiHumor {
    FORMAL = 'formal',
    FRIENDLY = 'friendly',
    CASUAL = 'casual',
    PROFESSIONAL = 'professional'
}

// Enum para tamanho das respostas
export enum ResponseSize {
    SHORT = 'short',
    MEDIUM = 'medium',
    LONG = 'long',
    DETAILED = 'detailed'
}

// DTO para configurações do assistente
export class UpdateAssistantSettingsDto {
    @IsOptional()
    @IsEnum(AiHumor)
    ai_humor?: AiHumor;

    @IsOptional()
    @IsEnum(ResponseSize)
    response_size?: ResponseSize;
    
    @IsOptional()
    @IsString()
    reminder_time?: string; // Formato HH:MM
    
    @IsOptional()
    @IsString()
    reminder_interval?: string; // Intervalo em minutos
}

// DTO de resposta para informações do perfil
export class ProfileInfoResponseDto {
    id: number;
    name: string;
    email: string;
    phone?: string;
    timezone: string;
    is_admin: boolean;
    created_at: Date;
}

// DTO de resposta para configurações do assistente
export class AssistantSettingsResponseDto {
    ai_humor: string;
    response_size: string;
    reminder_time: string; // Formato HH:MM
    reminder_interval: string; // Intervalo em minutos
}
