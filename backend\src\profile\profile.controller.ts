import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  BadRequestException,
} from '@nestjs/common';
import { DevAwareAuthGuard } from '../auth/dev-auth.guard';
import { ProfileService } from './profile.service';
import {
  UpdatePersonalInfoDto,
  ChangePasswordDto,
  UpdateAssistantSettingsDto,
  ProfileInfoResponseDto,
  AssistantSettingsResponseDto,
} from './dto/profile.dto';
import { SuccessResponseDto } from '../common/dto/common-response.dto';

@Controller('profile')
@UseGuards(DevAwareAuthGuard)
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  // Buscar informações do perfil
  @Get()
  @HttpCode(HttpStatus.OK)
  async getProfile(@Request() req): Promise<ProfileInfoResponseDto> {
    return this.profileService.getProfile(req.user.userId);
  }

  // Atualizar informações pessoais
  @Put('personal-info')
  @HttpCode(HttpStatus.OK)
  async updatePersonalInfo(
    @Request() req,
    @Body() updatePersonalInfoDto: UpdatePersonalInfoDto,
  ): Promise<SuccessResponseDto> {
    await this.profileService.updatePersonalInfo(req.user.userId, updatePersonalInfoDto);
    return {
      success: true,
      message: 'Informações pessoais atualizadas com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

  // Alterar senha
  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Request() req,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<SuccessResponseDto> {
    if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
      throw new BadRequestException('A nova senha e a confirmação devem ser iguais');
    }

    await this.profileService.changePassword(req.user.userId, changePasswordDto);
    return {
      success: true,
      message: 'Senha alterada com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

  // Buscar configurações do assistente
  @Get('assistant-settings')
  @HttpCode(HttpStatus.OK)
  async getAssistantSettings(@Request() req): Promise<AssistantSettingsResponseDto> {
    return this.profileService.getAssistantSettings(req.user.userId);
  }

  // Atualizar configurações do assistente
  @Put('assistant-settings')
  @HttpCode(HttpStatus.OK)
  async updateAssistantSettings(
    @Request() req,
    @Body() updateAssistantSettingsDto: UpdateAssistantSettingsDto,
  ): Promise<SuccessResponseDto> {
    await this.profileService.updateAssistantSettings(req.user.userId, updateAssistantSettingsDto);
    return {
      success: true,
      message: 'Configurações do assistente atualizadas com sucesso',
      timestamp: new Date().toISOString(),
    };
  }

}
