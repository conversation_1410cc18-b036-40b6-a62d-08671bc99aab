import {
  Injectable,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Connection } from 'mysql2/promise';
import * as bcrypt from 'bcrypt';
import {
  UpdatePersonalInfoDto,
  ChangePasswordDto,
  UpdateAssistantSettingsDto,
  ProfileInfoResponseDto,
  AssistantSettingsResponseDto,
} from './dto/profile.dto';
import { MYSQL2_CONNECTION } from '../database/database.provider';

@Injectable()
export class ProfileService {
  constructor(@Inject(MYSQL2_CONNECTION) private connection: Connection) {}

  async getProfile(userId: number): Promise<ProfileInfoResponseDto> {
    const [rows] = await this.connection.execute(
      `SELECT id, name, email, phone, timezone, is_admin, created_at 
       FROM users 
       WHERE id = ? AND deleted_at IS NULL`,
      [userId]
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const user = rows[0] as any;
    return {
      id: user.id,
      name: user.name || '',
      email: user.email || '',
      phone: user.phone || null,
      timezone: user.timezone || 'America/Sao_Paulo',
      is_admin: user.is_admin === 1,
      created_at: user.created_at,
    };
  }

  async updatePersonalInfo(userId: number, updateDto: UpdatePersonalInfoDto): Promise<void> {
    // Verificar se o email já existe para outro usuário
    const [emailCheck] = await this.connection.execute(
      'SELECT id FROM users WHERE email = ? AND id != ? AND deleted_at IS NULL',
      [updateDto.email, userId]
    );

    if (Array.isArray(emailCheck) && emailCheck.length > 0) {
      throw new BadRequestException('Este email já está sendo usado por outro usuário');
    }

    // Atualizar informações do usuário
    const [result] = await this.connection.execute(
      `UPDATE users 
       SET name = ?, email = ?, phone = ?, timezone = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ? AND deleted_at IS NULL`,
      [
        updateDto.name ?? null, 
        updateDto.email ?? null, 
        updateDto.phone ?? null, 
        updateDto.timezone ?? null, 
        userId
      ]
    );

    if ((result as any).affectedRows === 0) {
      throw new NotFoundException('Usuário não encontrado');
    }
  }

  async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<void> {
    // Verificar senha atual
    const [rows] = await this.connection.execute(
      'SELECT password FROM users WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!Array.isArray(rows) || rows.length === 0) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const user = rows[0] as any;
    const isCurrentPasswordValid = await bcrypt.compare(
      changePasswordDto.currentPassword,
      user.password
    );

    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Senha atual incorreta');
    }

    // Hash da nova senha
    const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 10);

    // Atualizar senha
    await this.connection.execute(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [hashedNewPassword, userId]
    );
  }

  async getAssistantSettings(userId: number): Promise<AssistantSettingsResponseDto> {
    try {
      const [rows] = await this.connection.execute(
        `SELECT ai_humor, response_size, reminder_time, reminder_interval 
         FROM user_assistant_settings 
         WHERE user_id = ?`,
        [userId]
      );

      if (!Array.isArray(rows) || rows.length === 0) {
        // Criar configurações padrão se não existirem
        try {
          await this.connection.execute(
            `INSERT INTO user_assistant_settings (user_id, ai_humor, response_size, reminder_time, reminder_interval) 
             VALUES (?, 'friendly', 'medium', '09:00:00', '30')`,
            [userId]
          );
        } catch (insertError) {
          // Se der erro na inserção, ainda retorna valores padrão
          console.error('Error inserting default assistant settings:', insertError);
        }

        return {
          ai_humor: 'friendly',
          response_size: 'medium',
          reminder_time: '09:00',
          reminder_interval: '30',
        };
      }

      const settings = rows[0] as any;
      return {
        ai_humor: settings.ai_humor || 'friendly',
        response_size: settings.response_size || 'medium',
        reminder_time: settings.reminder_time ? settings.reminder_time.slice(0, 5) : '09:00', // Converter TIME para HH:MM
        reminder_interval: settings.reminder_interval || '30',
      };
    } catch (error) {
      console.error('Error getting assistant settings:', error);
      // Se a tabela não existir ou houver outro erro, retorna configurações padrão
      return {
        ai_humor: 'friendly',
        response_size: 'medium',
        reminder_time: '09:00',
        reminder_interval: '30',
      };
    }
  }

  async updateAssistantSettings(
    userId: number,
    updateDto: UpdateAssistantSettingsDto
  ): Promise<void> {
    try {
      // Verificar se já existem configurações para o usuário
      const [existingRows] = await this.connection.execute(
        'SELECT id FROM user_assistant_settings WHERE user_id = ?',
        [userId]
      );

      if (!Array.isArray(existingRows) || existingRows.length === 0) {
        // Criar nova configuração
        const reminderTime = updateDto.reminder_time ? `${updateDto.reminder_time}:00` : '09:00:00';
        await this.connection.execute(
          `INSERT INTO user_assistant_settings (user_id, ai_humor, response_size, reminder_time, reminder_interval) 
           VALUES (?, ?, ?, ?, ?)`,
          [
            userId, 
            updateDto.ai_humor || 'friendly', 
            updateDto.response_size || 'medium',
            reminderTime,
            updateDto.reminder_interval || '30'
          ]
        );
      } else {
        // Atualizar configuração existente
        const updateFields: string[] = [];
        const updateValues: any[] = [];

        if (updateDto.ai_humor) {
          updateFields.push('ai_humor = ?');
          updateValues.push(updateDto.ai_humor);
        }

        if (updateDto.response_size) {
          updateFields.push('response_size = ?');
          updateValues.push(updateDto.response_size);
        }

        if (updateDto.reminder_time) {
          updateFields.push('reminder_time = ?');
          updateValues.push(`${updateDto.reminder_time}:00`); // Converter HH:MM para HH:MM:SS
        }

        if (updateDto.reminder_interval) {
          updateFields.push('reminder_interval = ?');
          updateValues.push(updateDto.reminder_interval);
        }

        if (updateFields.length > 0) {
          updateFields.push('updated_at = CURRENT_TIMESTAMP');
          updateValues.push(userId);

          await this.connection.execute(
            `UPDATE user_assistant_settings SET ${updateFields.join(', ')} WHERE user_id = ?`,
            updateValues
          );
        }
      }
    } catch (error) {
      console.error('Error updating assistant settings:', error);
      throw new BadRequestException('Erro ao salvar configurações do assistente');
    }
  }

}
