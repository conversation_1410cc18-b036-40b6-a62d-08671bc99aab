import { Injectable, Inject, Logger } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { DATABASE_CONNECTION } from '../database/database.provider';
import { IBaseRepository, FindAllOptions, PaginatedResult } from '../common/interfaces/base-repository.interface';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export abstract class BaseRepository<T, CreateDto, UpdateDto> implements IBaseRepository<T, CreateDto, UpdateDto> {
  protected readonly logger: Logger;
  protected readonly db: Kysely<Database>;

  constructor(
    @Inject(DATABASE_CONNECTION) db: Kysely<Database>,
    loggerContext: string
  ) {
    this.db = db;
    this.logger = new Logger(loggerContext);
  }

  abstract get tableName(): keyof Database;
  abstract get entityName(): string;
  abstract mapToResponseDto(entity: any, userTimezone?: string): T;
  abstract prepareCreateData(dto: CreateDto, userId: number, userTimezone?: string): any;
  abstract prepareUpdateData(dto: UpdateDto, userTimezone?: string): any;

  async create(data: CreateDto, userId: number, userTimezone: string = 'UTC'): Promise<T> {
    try {
      const createData = this.prepareCreateData(data, userId, userTimezone);
      
      const result = await (this.db as any)
        .insertInto(this.tableName)
        .values(createData)
        .executeTakeFirst();

      if (!result.insertId) {
        throw new Error(`Falha ao criar ${this.entityName}`);
      }

      this.logger.log(`${this.entityName} criado com ID ${result.insertId} para usuário ${userId}`);
      return this.findOne(Number(result.insertId), userId, userTimezone);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, `criar ${this.entityName}`, userId);
    }
  }

  async findAll(userId: number, options: FindAllOptions = {}): Promise<PaginatedResult<T>> {
    try {
      const { page = 1, limit = 50, orderBy = 'created_at', orderDirection = 'desc' } = options;
      const offset = (page - 1) * limit;
      
      let query = (this.db as any)
        .selectFrom(this.tableName)
        .selectAll()
        .where('user_id', '=', userId);

      // Aplicar filtros adicionais se fornecidos
      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.where(key, '=', value);
          }
        });
      }

      // Aplicar ordenação e paginação
      query = query.orderBy(orderBy, orderDirection).limit(limit).offset(offset);
      const entities = await query.execute();

      // Contar total
      const totalResult = await (this.db as any)
        .selectFrom(this.tableName)
        .select((eb: any) => eb.fn.count('id').as('count'))
        .where('user_id', '=', userId)
        .executeTakeFirst();

      const total = Number(totalResult?.count || 0);

      // Mapear para DTOs
      const data = entities.map((entity: any) => this.mapToResponseDto(entity));

      this.logger.debug(`Listados ${entities.length} ${this.entityName}s para usuário ${userId}`);

      return { data, total, page, limit };
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, `listar ${this.entityName}s`, userId);
    }
  }

  async findOne(id: number, userId: number, userTimezone: string = 'UTC'): Promise<T> {
    try {
      const entity = await (this.db as any)
        .selectFrom(this.tableName)
        .selectAll()
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .executeTakeFirst();

      ErrorUtils.validateOwnership(entity, userId, this.entityName);

      return this.mapToResponseDto(entity, userTimezone);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, `buscar ${this.entityName}`, userId);
    }
  }

  async update(id: number, data: UpdateDto, userId: number, userTimezone: string = 'UTC'): Promise<T> {
    try {
      // Verificar se existe e pertence ao usuário
      await this.findOne(id, userId, userTimezone);

      const updateData = this.prepareUpdateData(data, userTimezone);

      await (this.db as any)
        .updateTable(this.tableName)
        .set(updateData)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`${this.entityName} ${id} atualizado para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, `atualizar ${this.entityName}`, userId);
    }
  }

  async remove(id: number, userId: number): Promise<void> {
    try {
      // Verificar se existe e pertence ao usuário
      await this.findOne(id, userId);

      await (this.db as any)
        .deleteFrom(this.tableName)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`${this.entityName} ${id} removido para usuário ${userId}`);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, `remover ${this.entityName}`, userId);
    }
  }
}