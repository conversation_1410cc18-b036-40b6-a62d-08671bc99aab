import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { db } from '../database.types';
import { IFinanceRepository } from './interfaces/finance.repository.interface';
import { CreateFinanceDto } from '../finances/dto/create-finance.dto';
import { UpdateFinanceDto } from '../finances/dto/update-finance.dto';
import { FinanceResponseDto, FinanceSummaryDto } from '../finances/dto/finance-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class FinanceRepository implements IFinanceRepository {
  private readonly logger = new Logger(FinanceRepository.name);
  private db = db;

  async create(createFinanceDto: CreateFinanceDto, userId: number, userTimezone: string = 'UTC'): Promise<FinanceResponseDto> {
    const data = this.prepareCreateData(createFinanceDto, userId, userTimezone);

    const result = await this.db
      .insertInto('finances')
      .values(data)
      .executeTakeFirstOrThrow();

    return this.findOne(Number(result.insertId), userId, userTimezone);
  }

  async findAll(userId: number, options?: any): Promise<any> {
    // Implementação básica para compatibilidade
    return this.findAllWithCategory(userId, 'UTC', 1, 50);
  }

  async findOne(id: number, userId: number, userTimezone: string = 'UTC'): Promise<FinanceResponseDto> {
    const finance = await this.db
      .selectFrom('finances')
      .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
      .select([
        'finances.id',
        'finances.transaction_type',
        'finances.category_id',
        'finances.is_saving',
        'finances.description',
        'finances.amount',
        'finances.transaction_date',
        'finances.user_id',
        'finances.created_at',
        'finances.updated_at',
        'finances_categories.name as category_name'
      ])
      .where('finances.id', '=', id)
      .where('finances.user_id', '=', userId)
      .executeTakeFirst();

    if (!finance) {
      throw new Error(`Transação financeira com ID ${id} não encontrada`);
    }

    return this.mapToResponseDto(finance, userTimezone);
  }

  async update(id: number, updateFinanceDto: UpdateFinanceDto, userId: number, userTimezone: string = 'UTC'): Promise<FinanceResponseDto> {
    const data = this.prepareUpdateData(updateFinanceDto, userTimezone);

    await this.db
      .updateTable('finances')
      .set(data)
      .where('id', '=', id)
      .where('user_id', '=', userId)
      .execute();

    return this.findOne(id, userId, userTimezone);
  }

  async remove(id: number, userId: number): Promise<void> {
    await this.db
      .deleteFrom('finances')
      .where('id', '=', id)
      .where('user_id', '=', userId)
      .execute();
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): FinanceResponseDto {
    return {
      id: entity.id,
      transaction_type: entity.transaction_type,
      category_id: entity.category_id || undefined,
      category_name: entity.category_name || undefined,
      is_saving: entity.is_saving || undefined,
      description: entity.description || undefined,
      amount: entity.amount,
      transaction_date: TimezoneUtils.toUserTimezone(entity.transaction_date, userTimezone),
      user_id: entity.user_id,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateFinanceDto, userId: number, userTimezone: string = 'UTC'): any {
    return {
      ...dto,
      user_id: userId,
      transaction_date: TimezoneUtils.prepareDateForDatabase(dto.transaction_date, userTimezone),
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateFinanceDto, userTimezone: string = 'UTC'): any {
    const updateData = {
      ...dto,
      transaction_date: dto.transaction_date ? 
        TimezoneUtils.prepareDateForDatabase(dto.transaction_date, userTimezone) : 
        undefined,
      updated_at: new Date()
    };

    // Remover campos undefined
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    return updateData;
  }

  async findAllWithCategory(userId: number, userTimezone: string, page = 1, limit = 50) {
    try {
      const offset = (page - 1) * limit;

      // Consulta com LEFT JOIN seguindo o padrão do dashboard
      const finances = await this.db
        .selectFrom('finances')
        .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
        .select([
          'finances.id',
          'finances.transaction_type',
          'finances.category_id',
          'finances.is_saving',
          'finances.description',
          'finances.amount',
          'finances.transaction_date',
          'finances.user_id',
          'finances.created_at',
          'finances.updated_at',
          'finances_categories.name as category_name'
        ])
        .where('finances.user_id', '=', userId)
        .orderBy('finances.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      const total = await this.db
        .selectFrom('finances')
        .select(this.db.fn.count('id').as('count'))
        .where('user_id', '=', userId)
        .executeTakeFirst();

      // Usar mapToResponseDto para processamento correto
      const data = finances.map(finance => this.mapToResponseDto(finance, userTimezone));

      return {
        data,
        total: Number(total?.count || 0),
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`Erro na consulta de finanças: ${error.message}`);
      throw error;
    }
  }

  async getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto> {
    try {
      // Se não fornecidas, usar o mês atual
      const now = new Date();
      const start = startDate || new Date(now.getFullYear(), now.getMonth(), 1);
      const end = endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

      // Converter para UTC considerando o timezone do usuário
      const startUTC = TimezoneUtils.fromUserTimezone(start, userTimezone);
      const endUTC = TimezoneUtils.fromUserTimezone(end, userTimezone);

      const transactions = await this.db
        .selectFrom('finances')
        .select(['transaction_type', 'amount', 'is_saving'])
        .where('user_id', '=', userId)
        .where('transaction_date', '>=', startUTC)
        .where('transaction_date', '<=', endUTC)
        .execute();

      let totalIncome = 0;
      let totalExpenses = 0;
      let totalSavings = 0;

      transactions.forEach(transaction => {
        const amount = parseFloat(transaction.amount);

        if (transaction.transaction_type === 'income') {
          totalIncome += amount;
        } else if (transaction.transaction_type === 'expense') {
          totalExpenses += amount;
        }

        if (transaction.is_saving) {
          if (transaction.transaction_type === 'expense') {
            totalSavings += amount;
          } else {
            totalSavings -= amount;
          }
        }
      });

      const balance = totalIncome - totalExpenses;

      return {
        totalIncome,
        totalExpenses,
        totalSavings,
        balance,
        period: {
          start: TimezoneUtils.toUserTimezone(startUTC, userTimezone),
          end: TimezoneUtils.toUserTimezone(endUTC, userTimezone)
        }
      };
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'buscar resumo financeiro', userId);
    }
  }

  async getExpensesByCategory(userId: number, startDate?: Date, endDate?: Date): Promise<{ category: string; amount: number; count: number }[]> {
    try {
      let query = this.db
        .selectFrom('finances')
        .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
        .select([
          'finances_categories.name as category',
          'finances.category_id as category_id',
          this.db.fn.sum('finances.amount').as('amount'),
          this.db.fn.count('finances.id').as('count')
        ])
        .where('finances.user_id', '=', userId)
        .where('finances.transaction_type', '=', 'expense')
        .groupBy('finances.category_id')
        .groupBy('finances_categories.name')
        .orderBy('amount', 'desc');

      if (startDate) {
        query = query.where('finances.transaction_date', '>=', startDate);
      }
      if (endDate) {
        query = query.where('finances.transaction_date', '<=', endDate);
      }

      const result = await query.execute();

      return result.map(row => ({
        category: row.category || `Categoria ID ${row.category_id || 'N/A'}`,
        amount: Number(row.amount) || 0,
        count: Number(row.count) || 0
      }));
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'buscar gastos por categoria', userId);
    }
  }

  async getAnnualSummary(userId: number, year: number, userTimezone: string) {
    try {
      const startDate = new Date(year, 0, 1, 0, 0, 0, 0);
      const endDate = new Date(year, 11, 31, 23, 59, 59, 999);

      const startUTC = TimezoneUtils.fromUserTimezone(startDate, userTimezone);
      const endUTC = TimezoneUtils.fromUserTimezone(endDate, userTimezone);

      const transactions = await this.db
        .selectFrom('finances')
        .select(['transaction_type', 'amount', 'is_saving', 'transaction_date'])
        .where('user_id', '=', userId)
        .where('transaction_date', '>=', startUTC)
        .where('transaction_date', '<=', endUTC)
        .orderBy('transaction_date', 'asc')
        .execute();

      // Processar dados mensais
      const monthlyData = Array.from({ length: 12 }, (_, index) => {
        const monthName = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'][index];
        return {
          month: monthName,
          monthNumber: index + 1,
          income: 0,
          expenses: 0,
          savings: 0,
          balance: 0
        };
      });

      transactions.forEach(transaction => {
        const transactionDate = TimezoneUtils.toUserTimezone(transaction.transaction_date, userTimezone);
        const month = transactionDate.getMonth();
        const amount = parseFloat(transaction.amount);

        if (transaction.transaction_type === 'income') {
          monthlyData[month].income += amount;
        } else if (transaction.transaction_type === 'expense') {
          monthlyData[month].expenses += amount;
        }

        if (transaction.is_saving) {
          if (transaction.transaction_type === 'expense') {
            monthlyData[month].savings += amount;
          } else {
            monthlyData[month].savings -= amount;
          }
        }
      });

      monthlyData.forEach(month => {
        month.balance = month.income - month.expenses;
      });

      const totalIncome = monthlyData.reduce((sum, month) => sum + month.income, 0);
      const totalExpenses = monthlyData.reduce((sum, month) => sum + month.expenses, 0);
      const totalSavings = monthlyData.reduce((sum, month) => sum + month.savings, 0);
      const totalBalance = totalIncome - totalExpenses;

      return {
        year,
        monthlyData,
        totals: {
          income: totalIncome,
          expenses: totalExpenses,
          savings: totalSavings,
          balance: totalBalance
        }
      };
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'buscar resumo anual', userId);
    }
  }

  async getAnnualCategoryDistribution(userId: number, year: number, userTimezone: string) {
    try {
      const startDate = new Date(year, 0, 1, 0, 0, 0, 0);
      const endDate = new Date(year, 11, 31, 23, 59, 59, 999);

      const startUTC = TimezoneUtils.fromUserTimezone(startDate, userTimezone);
      const endUTC = TimezoneUtils.fromUserTimezone(endDate, userTimezone);

      const categoryExpenses = await this.db
        .selectFrom('finances')
        .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
        .select([
          'finances_categories.name as category',
          'finances_categories.id as category_id',
          this.db.fn.sum('finances.amount').as('total_amount'),
          this.db.fn.count('finances.id').as('transaction_count')
        ])
        .where('finances.user_id', '=', userId)
        .where('finances.transaction_type', '=', 'expense')
        .where('finances.transaction_date', '>=', startUTC)
        .where('finances.transaction_date', '<=', endUTC)
        .groupBy('finances.category_id')
        .groupBy('finances_categories.name')
        .orderBy('total_amount', 'desc')
        .execute();

      const totalExpenses = categoryExpenses.reduce((sum, cat) => sum + parseFloat(String(cat.total_amount) || '0'), 0);

      const colors = [
        '#B4EB00', '#212121', '#6C6C6C', '#FF3B30', '#007AFF', 
        '#4CAF50', '#FF9500', '#5856D6', '#BBBBBB', '#E5E7EB'
      ];

      const formattedData = categoryExpenses.map((category, index) => {
        const amount = parseFloat(String(category.total_amount) || '0');
        const percentage = totalExpenses > 0 ? (amount / totalExpenses) * 100 : 0;

        return {
          name: category.category || `Categoria ID ${category.category_id || 'Sem categoria'}`,
          value: amount,
          color: colors[index % colors.length],
          percentage: Math.round(percentage * 10) / 10,
          count: Number(category.transaction_count) || 0
        };
      });

      return formattedData.sort((a, b) => b.value - a.value).slice(0, 10);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'buscar distribuição anual de categorias', userId);
    }
  }
}