import { Injectable, Inject } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { DATABASE_CONNECTION } from '../database/database.provider';
import { BaseRepository } from './base.repository';
import { IIdeaCategoryRepository } from './interfaces/idea-category.repository.interface';
import { CreateIdeaCategoryDto } from '../ideas/dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from '../ideas/dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from '../ideas/dto/idea-category-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class IdeaCategoryRepository extends BaseRepository<IdeaCategoryResponseDto, CreateIdeaCategoryDto, UpdateIdeaCategoryDto> implements IIdeaCategoryRepository {
  
  constructor(@Inject(DATABASE_CONNECTION) db: Kysely<Database>) {
    super(db, 'IdeaCategoryRepository');
  }

  get tableName(): keyof Database {
    return 'ideas_categories';
  }

  get entityName(): string {
    return 'categoria de ideia';
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): IdeaCategoryResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      user_id: entity.user_id,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateIdeaCategoryDto, userId: number): any {
    return {
      ...dto,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateIdeaCategoryDto): any {
    return {
      ...dto,
      updated_at: new Date()
    };
  }

  async checkCategoryInUse(id: number, userId: number): Promise<boolean> {
    try {
      const ideasUsingCategory = await this.db
        .selectFrom('ideas')
        .select(['id'])
        .where('category_id', '=', id)
        .where('user_id', '=', userId)
        .limit(1)
        .execute();

      return ideasUsingCategory.length > 0;
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'verificar uso da categoria', userId);
    }
  }

  async findAllOrderedByName(userId: number, userTimezone: string = 'UTC') {
    try {
      const categories = await this.db
        .selectFrom('ideas_categories')
        .selectAll()
        .where('user_id', '=', userId)
        .orderBy('name', 'asc')
        .execute();

      return categories.map(category => this.mapToResponseDto(category, userTimezone));
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'listar categorias ordenadas', userId);
    }
  }
}