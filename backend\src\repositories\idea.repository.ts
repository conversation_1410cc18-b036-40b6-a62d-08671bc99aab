import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { db } from '../database.types';
import { IIdeaRepository } from './interfaces/idea.repository.interface';
import { CreateIdeaDto } from '../ideas/dto/create-idea.dto';
import { UpdateIdeaDto } from '../ideas/dto/update-idea.dto';
import { IdeaResponseDto } from '../ideas/dto/idea-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class IdeaRepository implements IIdeaRepository {
  private readonly logger = new Logger(IdeaRepository.name);
  private db = db;

  async create(createIdeaDto: CreateIdeaDto, userId: number, userTimezone: string = 'UTC'): Promise<IdeaResponseDto> {
    const data = this.prepareCreateData(createIdeaDto, userId);

    const result = await this.db
      .insertInto('ideas')
      .values(data)
      .executeTakeFirstOrThrow();

    return this.findOne(Number(result.insertId), userId, userTimezone);
  }

  async findAll(userId: number, options?: any): Promise<any> {
    // Implementação básica para compatibilidade
    return this.findAllWithCategory(userId, 'UTC', 1, 50);
  }

  async findOne(id: number, userId: number, userTimezone: string = 'UTC'): Promise<IdeaResponseDto> {
    const idea = await this.db
      .selectFrom('ideas')
      .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
      .select([
        'ideas.id',
        'ideas.category_id',
        'ideas.name',
        'ideas.description',
        'ideas.content',
        'ideas.is_favorite',
        'ideas.user_id',
        'ideas.created_at',
        'ideas.updated_at',
        'ideas_categories.name as category_name'
      ])
      .where('ideas.id', '=', id)
      .where('ideas.user_id', '=', userId)
      .executeTakeFirst();

    if (!idea) {
      throw new Error(`Ideia com ID ${id} não encontrada`);
    }

    return this.mapToResponseDto(idea, userTimezone);
  }

  async update(id: number, updateIdeaDto: UpdateIdeaDto, userId: number, userTimezone: string = 'UTC'): Promise<IdeaResponseDto> {
    const data = this.prepareUpdateData(updateIdeaDto);

    await this.db
      .updateTable('ideas')
      .set(data)
      .where('id', '=', id)
      .where('user_id', '=', userId)
      .execute();

    return this.findOne(id, userId, userTimezone);
  }

  async remove(id: number, userId: number): Promise<void> {
    await this.db
      .deleteFrom('ideas')
      .where('id', '=', id)
      .where('user_id', '=', userId)
      .execute();
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): IdeaResponseDto {
    return {
      id: entity.id,
      category_id: entity.category_id || undefined,
      category_name: entity.category_name || undefined,
      name: entity.name,
      description: entity.description || undefined,
      content: entity.content || undefined,
      is_favorite: entity.is_favorite || undefined,
      user_id: entity.user_id,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateIdeaDto, userId: number): any {
    return {
      ...dto,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateIdeaDto): any {
    return {
      ...dto,
      updated_at: new Date()
    };
  }

  async findAllWithCategory(userId: number, userTimezone: string, page = 1, limit = 50) {
    try {
      const offset = (page - 1) * limit;

      // Consulta com LEFT JOIN seguindo o padrão do dashboard
      const ideas = await this.db
        .selectFrom('ideas')
        .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
        .select([
          'ideas.id',
          'ideas.category_id',
          'ideas.name',
          'ideas.description',
          'ideas.content',
          'ideas.is_favorite',
          'ideas.user_id',
          'ideas.created_at',
          'ideas.updated_at',
          'ideas_categories.name as category_name'
        ])
        .where('ideas.user_id', '=', userId)
        .orderBy('ideas.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      const total = await this.db
        .selectFrom('ideas')
        .select(this.db.fn.count('id').as('count'))
        .where('user_id', '=', userId)
        .executeTakeFirst();

      // Usar mapToResponseDto para processamento correto
      const data = ideas.map(idea => this.mapToResponseDto(idea, userTimezone));

      return {
        data,
        total: Number(total?.count || 0),
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`Erro na consulta de ideias: ${error.message}`);
      throw error;
    }
  }

  async toggleFavorite(id: number, userId: number): Promise<IdeaResponseDto> {
    try {
      // Buscar a ideia atual
      const idea = await this.findOne(id, userId);

      // Alternar o status de favorito
      const newFavoriteStatus = !idea.is_favorite;

      await this.db
        .updateTable('ideas')
        .set({
          is_favorite: newFavoriteStatus,
          updated_at: new Date()
        })
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Ideia ${id} ${newFavoriteStatus ? 'marcada como favorita' : 'desmarcada como favorita'} para usuário ${userId}`);
      return this.findOne(id, userId);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'alterar status de favorito da ideia', userId);
    }
  }
}