import { IBaseRepository } from '../../common/interfaces/base-repository.interface';
import { CreateFinanceCategoryDto } from '../../finances/dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from '../../finances/dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from '../../finances/dto/finance-category-response.dto';

export interface IFinanceCategoryRepository extends IBaseRepository<FinanceCategoryResponseDto, CreateFinanceCategoryDto, UpdateFinanceCategoryDto> {
  checkCategoryInUse(id: number, userId: number): Promise<boolean>;
}