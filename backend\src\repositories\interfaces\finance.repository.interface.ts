import { IBaseRepository, PaginatedResult } from '../../common/interfaces/base-repository.interface';
import { CreateFinanceDto } from '../../finances/dto/create-finance.dto';
import { UpdateFinanceDto } from '../../finances/dto/update-finance.dto';
import { FinanceResponseDto, FinanceSummaryDto } from '../../finances/dto/finance-response.dto';

export interface IFinanceRepository extends IBaseRepository<FinanceResponseDto, CreateFinanceDto, UpdateFinanceDto> {
  getSummary(
    userId: number, 
    userTimezone: string, 
    startDate?: Date, 
    endDate?: Date
  ): Promise<FinanceSummaryDto>;
  
  getExpensesByCategory(
    userId: number, 
    startDate?: Date, 
    endDate?: Date
  ): Promise<{ category: string; amount: number; count: number }[]>;
  
  getAnnualSummary(
    userId: number, 
    year: number, 
    userTimezone: string
  ): Promise<any>;
  
  getAnnualCategoryDistribution(
    userId: number, 
    year: number, 
    userTimezone: string
  ): Promise<any[]>;
}