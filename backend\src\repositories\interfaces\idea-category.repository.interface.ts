import { IBaseRepository } from '../../common/interfaces/base-repository.interface';
import { CreateIdeaCategoryDto } from '../../ideas/dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from '../../ideas/dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from '../../ideas/dto/idea-category-response.dto';

export interface IIdeaCategoryRepository extends IBaseRepository<IdeaCategoryResponseDto, CreateIdeaCategoryDto, UpdateIdeaCategoryDto> {
  checkCategoryInUse(id: number, userId: number): Promise<boolean>;
}