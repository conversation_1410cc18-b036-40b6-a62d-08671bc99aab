import { IBaseRepository } from '../../common/interfaces/base-repository.interface';
import { CreateTaskCategoryDto } from '../../tasks/dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from '../../tasks/dto/update-task-category.dto';
import { TaskCategoryResponseDto } from '../../tasks/dto/task-category-response.dto';

export interface ITaskCategoryRepository extends IBaseRepository<TaskCategoryResponseDto, CreateTaskCategoryDto, UpdateTaskCategoryDto> {
  checkCategoryInUse(id: number, userId: number): Promise<boolean>;
}