import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '../database.module';
import { FinanceRepository } from './finance.repository';
import { FinanceCategoryRepository } from './finance-category.repository';
import { TaskRepository } from './task.repository';
import { TaskCategoryRepository } from './task-category.repository';
import { IdeaRepository } from './idea.repository';
import { IdeaCategoryRepository } from './idea-category.repository';

@Module({
  imports: [DatabaseModule],
  providers: [
    FinanceRepository,
    FinanceCategoryRepository,
    TaskRepository,
    TaskCategoryRepository,
    IdeaRepository,
    IdeaCategoryRepository,
  ],
  exports: [
    FinanceRepository,
    FinanceCategoryRepository,
    TaskRepository,
    TaskCategoryRepository,
    IdeaRepository,
    IdeaCategoryRepository,
  ],
})
export class RepositoriesModule {}