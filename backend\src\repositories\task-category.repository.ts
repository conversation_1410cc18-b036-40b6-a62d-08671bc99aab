import { Injectable, Inject } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { DATABASE_CONNECTION } from '../database/database.provider';
import { BaseRepository } from './base.repository';
import { ITaskCategoryRepository } from './interfaces/task-category.repository.interface';
import { CreateTaskCategoryDto } from '../tasks/dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from '../tasks/dto/update-task-category.dto';
import { TaskCategoryResponseDto } from '../tasks/dto/task-category-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class TaskCategoryRepository extends BaseRepository<TaskCategoryResponseDto, CreateTaskCategoryDto, UpdateTaskCategoryDto> implements ITaskCategoryRepository {
  
  constructor(@Inject(DATABASE_CONNECTION) db: Kysely<Database>) {
    super(db, 'TaskCategoryRepository');
  }

  get tableName(): keyof Database {
    return 'tasks_categories';
  }

  get entityName(): string {
    return 'categoria de tarefa';
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): TaskCategoryResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      user_id: entity.user_id,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateTaskCategoryDto, userId: number): any {
    return {
      ...dto,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateTaskCategoryDto): any {
    return {
      ...dto,
      updated_at: new Date()
    };
  }

  async checkCategoryInUse(id: number, userId: number): Promise<boolean> {
    try {
      const tasksUsingCategory = await this.db
        .selectFrom('tasks')
        .select(['id'])
        .where('category_id', '=', id)
        .where('user_id', '=', userId)
        .limit(1)
        .execute();

      return tasksUsingCategory.length > 0;
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'verificar uso da categoria', userId);
    }
  }

  async findAllOrderedByName(userId: number, userTimezone: string = 'UTC') {
    try {
      const categories = await this.db
        .selectFrom('tasks_categories')
        .selectAll()
        .where('user_id', '=', userId)
        .orderBy('name', 'asc')
        .execute();

      return categories.map(category => this.mapToResponseDto(category, userTimezone));
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'listar categorias ordenadas', userId);
    }
  }
}