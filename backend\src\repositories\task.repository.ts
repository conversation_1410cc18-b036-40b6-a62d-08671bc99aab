import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { db } from '../database.types';
import { ITaskRepository, TaskQueryResult } from './interfaces/task.repository.interface';
import { CreateTaskDto } from '../tasks/dto/create-task.dto';
import { UpdateTaskDto } from '../tasks/dto/update-task.dto';
import { TaskResponseDto } from '../tasks/dto/task-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class TaskRepository implements ITaskRepository {
  private readonly logger = new Logger(TaskRepository.name);
  private db = db;

  async create(createTaskDto: CreateTaskDto, userId: number, userTimezone: string = 'UTC'): Promise<TaskResponseDto> {
    const data = this.prepareCreateData(createTaskDto, userId, userTimezone);

    const result = await this.db
      .insertInto('tasks')
      .values(data)
      .executeTakeFirstOrThrow();

    return this.findOne(Number(result.insertId), userId, userTimezone);
  }

  async findOne(id: number, userId: number, userTimezone: string = 'UTC'): Promise<TaskResponseDto> {
    const task = await this.db
      .selectFrom('tasks')
      .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
      .select([
        'tasks.id',
        'tasks.name',
        'tasks.description',
        'tasks.task_type',
        'tasks.task_date',
        'tasks.completed_at',
        'tasks.created_at',
        'tasks.updated_at',
        'tasks.category_id',
        'tasks.user_id',
        'tasks_categories.name as category_name'
      ])
      .where('tasks.id', '=', id)
      .where('tasks.user_id', '=', userId)
      .executeTakeFirst();

    if (!task) {
      throw new Error(`Tarefa com ID ${id} não encontrada`);
    }

    return this.mapToResponseDto(task, userTimezone);
  }

  async update(id: number, updateTaskDto: UpdateTaskDto, userId: number, userTimezone: string = 'UTC'): Promise<TaskResponseDto> {
    const data = this.prepareUpdateData(updateTaskDto, userTimezone);

    await this.db
      .updateTable('tasks')
      .set(data)
      .where('id', '=', id)
      .where('user_id', '=', userId)
      .execute();

    return this.findOne(id, userId, userTimezone);
  }

  async remove(id: number, userId: number): Promise<void> {
    await this.db
      .deleteFrom('tasks')
      .where('id', '=', id)
      .where('user_id', '=', userId)
      .execute();
  }

  async findAll(userId: number, options?: any): Promise<any> {
    // Implementação básica para compatibilidade
    return this.findAllWithCategory(userId, 'UTC', 1, 50);
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): TaskResponseDto {
    return {
      id: entity.id,
      task_type: entity.task_type,
      category_id: entity.category_id || undefined,
      category_name: entity.category_name || undefined,
      name: entity.name,
      description: entity.description || undefined,
      task_date: entity.task_date ? TimezoneUtils.toUserTimezone(entity.task_date, userTimezone) : undefined,
      user_id: entity.user_id,
      completed_at: entity.completed_at ? TimezoneUtils.toUserTimezone(entity.completed_at, userTimezone) : undefined,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateTaskDto, userId: number, userTimezone: string = 'UTC'): any {
    return {
      ...dto,
      user_id: userId,
      task_date: TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone),
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateTaskDto, userTimezone: string = 'UTC'): any {
    const updateData = {
      ...dto,
      task_date: dto.task_date ? 
        TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone) : 
        undefined,
      updated_at: new Date()
    };

    // Remover campos undefined
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    return updateData;
  }

  async findAllWithCategory(
    userId: number,
    userTimezone: string,
    page = 1,
    limit = 50,
    startDate?: string,
    endDate?: string
  ): Promise<TaskQueryResult> {
    try {
      this.logger.debug(`Iniciando consulta de tarefas para usuário ${userId}`);

      // Executar consulta direta sem timeout artificial
      const result = await this.executeTaskQuery(userId, page, limit, startDate, endDate);

      this.logger.debug('Consulta de tarefas bem-sucedida');
      return result;

    } catch (error) {
      this.logger.error(`Erro na consulta de tarefas: ${error.message}`);
      throw error;
    }
  }

  private async executeTaskQuery(userId: number, page: number, limit: number, startDate?: string, endDate?: string) {
    const offset = (page - 1) * limit;

    // Consulta com LEFT JOIN seguindo o padrão do dashboard
    let query = this.db
      .selectFrom('tasks')
      .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
      .select([
        'tasks.id',
        'tasks.name',
        'tasks.description',
        'tasks.task_type',
        'tasks.task_date',
        'tasks.completed_at',
        'tasks.created_at',
        'tasks.updated_at',
        'tasks.category_id',
        'tasks.user_id',
        'tasks_categories.name as category_name'
      ])
      .where('tasks.user_id', '=', userId);

    // Aplicar filtros de data se fornecidos
    if (startDate) {
      query = query.where('tasks.task_date', '>=', new Date(startDate));
    }
    if (endDate) {
      query = query.where('tasks.task_date', '<=', new Date(endDate));
    }

    query = query
      .orderBy('tasks.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    this.logger.debug('Executando consulta de tarefas...');
    const tasks = await query.execute();

    // Contagem com os mesmos filtros
    let countQuery = this.db
      .selectFrom('tasks')
      .select(this.db.fn.count('id').as('count'))
      .where('user_id', '=', userId);

    if (startDate) {
      countQuery = countQuery.where('task_date', '>=', new Date(startDate));
    }
    if (endDate) {
      countQuery = countQuery.where('task_date', '<=', new Date(endDate));
    }

    const total = await countQuery.executeTakeFirst();

    // Usar mapToResponseDto para processamento correto
    const data = tasks.map(task => this.mapToResponseDto(task, 'America/Sao_Paulo'));

    return {
      data,
      total: Number(total?.count || 0),
      page,
      limit
    };
  }

  async complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    try {
      // Verificar se a tarefa existe e pertence ao usuário
      const task = await this.findOne(id, userId, userTimezone);

      // Se a tarefa já está concluída, desmarcar. Se não, marcar como concluída
      const isCompleted = !!task.completed_at;
      
      await this.db
        .updateTable('tasks')
        .set({
          completed_at: isCompleted ? null : new Date(),
          updated_at: new Date()
        })
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Tarefa ${id} ${isCompleted ? 'desmarcada como incompleta' : 'marcada como completa'} para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'alterar status da tarefa', userId);
    }
  }
}