import { IsOptional, IsString, IsEnum, IsDateString, IsNumber } from 'class-validator';

export class UpdateTaskDto {
  @IsOptional()
  @IsEnum(['appointment', 'task'])
  task_type?: 'appointment' | 'task';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDateString()
  task_date?: string;
}
