import { toZonedTime, fromZonedTime, format } from 'date-fns-tz';

// Exporta cada função individualmente
export function toUserTimezone(date: Date | string, userTimezone: string): Date {
  return toZonedTime(date, userTimezone);
}

export function fromUserTimezone(date: Date | string, userTimezone: string): Date {
  return fromZonedTime(date, userTimezone);
}

export function formatForUser(
  date: Date | string,
  userTimezone: string,
  pattern = 'yyyy-MM-dd HH:mm:ss'
): string {
  return format(toZonedTime(date, userTimezone), pattern, { timeZone: userTimezone });
}