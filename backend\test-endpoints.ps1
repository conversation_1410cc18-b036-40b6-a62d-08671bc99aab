$headers = @{
    'x-dev-mode' = 'true'
    'x-dev-user-id' = '1'
    'Content-Type' = 'application/json'
}

Write-Host "=== Testando endpoints da API ===" -ForegroundColor Green

# Teste 1: Tasks
Write-Host "`nTestando /tasks..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/tasks" -Headers $headers -Method GET
    Write-Host "✅ Tasks OK - Status: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content
    if ($content.Length -gt 100) {
        $content = $content.Substring(0, 100) + "..."
    }
    Write-Host "Content: $content" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Tasks ERRO: $($_.Exception.Message)" -ForegroundColor Red
}

# Teste 2: Finances  
Write-Host "`nTestando /finances..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/finances" -Headers $headers -Method GET
    Write-Host "✅ Finances OK - Status: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content
    if ($content.Length -gt 100) {
        $content = $content.Substring(0, 100) + "..."
    }
    Write-Host "Content: $content" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Finances ERRO: $($_.Exception.Message)" -ForegroundColor Red
}

# Teste 3: Ideas
Write-Host "`nTestando /ideas..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/ideas" -Headers $headers -Method GET
    Write-Host "✅ Ideas OK - Status: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content
    if ($content.Length -gt 100) {
        $content = $content.Substring(0, 100) + "..."
    }
    Write-Host "Content: $content" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Ideas ERRO: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Teste concluído ===" -ForegroundColor Green
