$headers = @{
    'x-dev-mode' = 'true'
    'x-dev-user-id' = '1'
    'Content-Type' = 'application/json'
}

Write-Host "Testando endpoint /tasks com timeout de 10s..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/tasks" -Headers $headers -Method GET -TimeoutSec 10
    Write-Host "✅ Tasks OK - Status: $($response.StatusCode)" -ForegroundColor Green
    
    # Parse do JSON para ver a estrutura
    $json = $response.Content | ConvertFrom-Json
    Write-Host "Total de tasks: $($json.total)" -ForegroundColor Cyan
    Write-Host "Tasks na página: $($json.tasks.Length)" -ForegroundColor Cyan
    
} catch [System.Net.WebException] {
    if ($_.Exception.Message -contains "timeout") {
        Write-Host "⏱️ Tasks TIMEOUT: Consulta demorou mais de 10 segundos" -ForegroundColor Red
    } else {
        Write-Host "❌ Tasks ERRO: $($_.Exception.Message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Tasks ERRO: $($_.Exception.Message)" -ForegroundColor Red
}
