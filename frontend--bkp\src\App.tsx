import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import Dashboard from './pages/Dashboard';
import TasksPage from './pages/TasksPage';
import WeekView from './pages/WeekView';
import FinancesPage from './pages/FinancesPage';
import IncomePage from './pages/IncomePage';
import ExpensesPage from './pages/ExpensesPage';
import SavingsPage from './pages/SavingsPage';
import CofrinhoPage from './pages/CofrinhoPage';
import CategoriesPage from './pages/CategoriesPage';
import IdeasPage from './pages/IdeasPage';
import IdeaDetailPage from './pages/IdeaDetailPage';
import ProfilePage from './pages/ProfilePage';
import MonthlyProgressPage from './pages/MonthlyProgressPage';
import AnnualFinancialOverview from './pages/AnnualFinancialOverview';

function App() {
  return (
    <div className="min-h-screen bg-[#F7F7F7]">
      <Navigation />
      <div className="md:pt-[72px]">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/tasks" element={<TasksPage />} />
          <Route path="/tasks/week" element={<WeekView />} />
          <Route path="/finances" element={<FinancesPage />} />
          <Route path="/finances/income" element={<IncomePage />} />
          <Route path="/finances/expenses" element={<ExpensesPage />} />
          <Route path="/finances/savings" element={<SavingsPage />} />
          <Route path="/finances/cofrinho" element={<CofrinhoPage />} />
          <Route path="/finances/categories" element={<CategoriesPage />} />
          <Route path="/finances/annual" element={<AnnualFinancialOverview />} />
          <Route path="/ideas" element={<IdeasPage />} />
          <Route path="/ideas/:id" element={<IdeaDetailPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/progresso-mensal" element={<MonthlyProgressPage />} />
        </Routes>
      </div>
    </div>
  );
}

export default App;