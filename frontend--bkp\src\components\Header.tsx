import React, { useState } from 'react';
import { ArrowLeft, Plus, ChevronDown } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface HeaderProps {
  onAddClick?: () => void;
  onEditBudgetClick?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onAddClick, onEditBudgetClick }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showDropdown, setShowDropdown] = useState(false);

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/tasks') return 'Compromissos';
    if (path.startsWith('/finances')) return 'Finanças';
    if (path === '/ideas') return 'Ideias';
    return 'dupli';
  };

  const getContextualButton = () => {
    const path = location.pathname;

    if (path === '/profile') {
      return null;
    }

    if (path === '/finances') {
      return (
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="w-10 h-10 bg-[#212121] rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <Plus size={20} className="text-white" />
          </button>

          <AnimatePresence>
            {showDropdown && (
              <>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 z-[1000]"
                  onClick={() => setShowDropdown(false)}
                />
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-lg z-[1001] py-2"
                >
                  <button
                    onClick={() => {
                      setShowDropdown(false);
                      onAddClick?.();
                    }}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 transition-colors"
                  >
                    Nova transação
                  </button>
                  <button
                    onClick={() => {
                      setShowDropdown(false);
                      onEditBudgetClick?.();
                    }}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 transition-colors"
                  >
                    Editar orçamento
                  </button>
                </motion.div>
              </>
            )}
          </AnimatePresence>
        </div>
      );
    }

    return (
      <button
        onClick={onAddClick}
        className="w-10 h-10 bg-[#212121] rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
      >
        <Plus size={20} className="text-white" />
      </button>
    );
  };

  return (
    <header className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-10">
      <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
        <button 
          onClick={() => navigate('/')}
          className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>

        <h1 className="text-xl font-bold">{getPageTitle()}</h1>

        {getContextualButton()}
      </div>
    </header>
  );
};

export default Header;