import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { Home, CheckCircle, DollarSign, Lightbulb, Settings } from 'lucide-react';

const Navigation: React.FC = () => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;

  const navItems = [
    { path: '/', icon: Home },
    { path: '/tasks', icon: CheckCircle },
    { path: '/finances', icon: DollarSign },
    { path: '/ideas', icon: Lightbulb },
    { path: '/profile', icon: Settings }
  ];

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:block fixed top-0 left-0 right-0 h-[72px] bg-[#212121] z-50">
        <div className="max-w-7xl mx-auto px-4 h-full flex items-center justify-between">
          <div className="text-white text-xl font-bold">
            Dupli
          </div>
          <ul className="flex items-center space-x-8">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center space-x-2 font-light transition-colors ${
                    isActive(item.path)
                      ? 'text-[#B4EB00]'
                      : 'text-white hover:text-[#B4EB00]'
                  }`}
                >
                  <item.icon size={20} />
                  <span>{item.path === '/profile' ? 'Configurações' : item.path === '/' ? 'Início' : item.path.slice(1)}</span>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="md:hidden fixed bottom-4 left-4 right-4 h-[70px] bg-[#212121] rounded-full z-50 shadow-[0_0_30px_rgba(0,0,0,0.25)] backdrop-blur-lg bg-opacity-95">
        <ul className="h-full flex items-center justify-evenly px-4">
          {navItems.map((item) => (
            <li key={item.path}>
              <Link
                to={item.path}
                className="p-3 flex items-center justify-center"
              >
                <item.icon
                  size={24}
                  className={isActive(item.path) ? 'text-[#B4EB00]' : 'text-[#BBBBBB]'}
                />
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </>
  );
};

export default Navigation;