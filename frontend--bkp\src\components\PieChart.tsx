import React from 'react';
import { motion } from 'framer-motion';

interface Category {
  name: string;
  value: number;
  color: string;
}

interface PieChartProps {
  data: Category[];
  size?: number;
}

const PieChart: React.FC<PieChartProps> = ({ data, size = 200 }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = 0;

  const center = size / 2;
  const radius = (size - 40) / 2;

  return (
    <div className="relative" style={{ width: size, height: size }}>
      <svg width={size} height={size}>
        {data.map((category, index) => {
          const percentage = (category.value / total) * 100;
          const angle = (percentage / 100) * 360;
          
          // Calculate the path
          const startX = center + radius * Math.cos((currentAngle * Math.PI) / 180);
          const startY = center + radius * Math.sin((currentAngle * Math.PI) / 180);
          const endX = center + radius * Math.cos(((currentAngle + angle) * Math.PI) / 180);
          const endY = center + radius * Math.sin(((currentAngle + angle) * Math.PI) / 180);
          
          const largeArcFlag = angle > 180 ? 1 : 0;
          
          const pathData = [
            `M ${center} ${center}`,
            `L ${startX} ${startY}`,
            `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
            'Z'
          ].join(' ');

          const path = (
            <motion.path
              key={category.name}
              d={pathData}
              fill={category.color}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="cursor-pointer transition-all hover:opacity-90"
              onMouseEnter={(e) => {
                const tooltip = document.getElementById('tooltip');
                if (tooltip) {
                  tooltip.style.opacity = '1';
                  tooltip.textContent = `${category.name}: ${percentage.toFixed(1)}%`;
                  tooltip.style.left = `${e.clientX + 10}px`;
                  tooltip.style.top = `${e.clientY - 20}px`;
                }
              }}
              onMouseLeave={() => {
                const tooltip = document.getElementById('tooltip');
                if (tooltip) {
                  tooltip.style.opacity = '0';
                }
              }}
            />
          );

          currentAngle += angle;
          return path;
        })}
      </svg>
      <div
        id="tooltip"
        className="fixed bg-gray-900 text-white px-2 py-1 rounded text-sm pointer-events-none opacity-0 transition-opacity"
        style={{ zIndex: 50 }}
      />
    </div>
  );
};

export default PieChart;