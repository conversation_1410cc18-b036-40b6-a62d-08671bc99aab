import React, { useState } from 'react';
import { Clock, CheckCircle, Edit2, Trash2, ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface TaskItemProps {
  id: string;
  title: string;
  description?: string;
  time?: string;
  completed: boolean;
  category?: string;
  tags?: string[];
  onComplete: (id: string) => void;
  onEdit: (id: string, updates: { title?: string; description?: string; time?: string; category?: string; tags?: string[] }) => void;
  onDelete: (id: string) => void;
}

const categories = [
  { name: 'Reuni<PERSON>', color: '#FF3B30' },
  { name: 'Compromisso', color: '#4CAF50' },
  { name: '<PERSON>re<PERSON>', color: '#B4EB00' },
  { name: 'Responsabilidade', color: '#FF9500' },
  { name: 'Agenda', color: '#007AFF' },
  { name: 'Cha<PERSON>', color: '#5856D6' }
];

const TaskItem: React.FC<TaskItemProps> = ({
  id,
  title,
  description,
  time,
  completed,
  category = 'Tarefa',
  tags = [],
  onComplete,
  onEdit,
  onDelete,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editedTitle, setEditedTitle] = useState(title);
  const [editedDescription, setEditedDescription] = useState(description || '');
  const [editedTime, setEditedTime] = useState(time || '');
  const [editedCategory, setEditedCategory] = useState(category);
  const [editedTags, setEditedTags] = useState(tags);

  const handleSave = () => {
    onEdit(id, {
      title: editedTitle,
      description: editedDescription,
      time: editedTime,
      category: editedCategory,
      tags: editedTags,
    });
    setIsExpanded(false);
  };

  const handleCancel = () => {
    setEditedTitle(title);
    setEditedDescription(description || '');
    setEditedTime(time || '');
    setEditedCategory(category);
    setEditedTags(tags);
    setIsExpanded(false);
  };

  const getCategoryColor = (categoryName: string) => {
    const cat = categories.find(c => c.name === categoryName);
    return cat?.color || '#B4EB00';
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`bg-white rounded-xl shadow-sm overflow-hidden transition-all ${
        completed ? 'opacity-60' : ''
      }`}
    >
      <div className="p-4">
        <div className="flex items-start gap-3">
          <button
            onClick={() => onComplete(id)}
            className={`w-6 h-6 rounded-full border-2 flex-shrink-0 flex items-center justify-center transition-colors ${
              completed
                ? 'bg-[#B4EB00] border-[#B4EB00]'
                : 'border-gray-300'
            }`}
            style={{
              borderColor: !completed ? getCategoryColor(category) : undefined,
              opacity: !completed ? 0.3 : 1
            }}
          >
            {completed && <CheckCircle size={16} className="text-white" />}
          </button>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <h3 
                className={`font-medium line-clamp-2 ${
                  completed ? 'line-through text-gray-400' : 'text-gray-900'
                }`}
              >
                {title}
              </h3>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors flex-shrink-0"
              >
                {isExpanded ? (
                  <ChevronUp size={16} className="text-gray-400" />
                ) : (
                  <ChevronDown size={16} className="text-gray-400" />
                )}
              </button>
            </div>

            {time && (
              <div className="flex items-center gap-1 mt-2">
                <Clock size={14} className="text-gray-400" />
                <span className="text-sm text-gray-500">{time}</span>
              </div>
            )}

            {tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: `${getCategoryColor(category)}20`,
                      color: getCategoryColor(category)
                    }}
                  >
                    {tag[0].toUpperCase()}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t border-gray-100"
          >
            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm text-gray-600 mb-1">
                  Título
                </label>
                <input
                  type="text"
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm text-gray-600 mb-1">
                  Categoria
                </label>
                <select
                  value={editedCategory}
                  onChange={(e) => setEditedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                >
                  {categories.map(cat => (
                    <option key={cat.name} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {time !== undefined && (
                <div>
                  <label className="block text-sm text-gray-600 mb-1">
                    Horário
                  </label>
                  <input
                    type="time"
                    value={editedTime}
                    onChange={(e) => setEditedTime(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm text-gray-600 mb-1">
                  Descrição
                </label>
                <textarea
                  value={editedDescription}
                  onChange={(e) => setEditedDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent resize-none"
                />
              </div>

              <div className="flex justify-between items-center gap-4 pt-2">
                <button
                  onClick={() => onDelete(id)}
                  className="px-4 py-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <Trash2 size={18} />
                </button>
                <div className="flex gap-2">
                  <button
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSave}
                    className="px-4 py-2 bg-[#B4EB00] rounded-lg hover:bg-opacity-90 transition-colors"
                  >
                    Salvar
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default TaskItem;