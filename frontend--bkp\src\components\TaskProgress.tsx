import React from 'react';

interface TaskProgressProps {
  completed: number;
  total: number;
}

const TaskProgress: React.FC<TaskProgressProps> = ({ completed, total }) => {
  const percentage = Math.round((completed / total) * 100);
  
  return (
    <div className="bg-white rounded-2xl p-4 shadow-sm">
      <p className="text-sm text-gray-600 mb-2">
        V<PERSON><PERSON> concluiu {completed} de {total} hoje – {percentage}%
      </p>
      <div className="h-1 bg-gray-100 rounded-full overflow-hidden">
        <div 
          className="h-full bg-[#B4EB00] transition-all duration-500"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export default TaskProgress;