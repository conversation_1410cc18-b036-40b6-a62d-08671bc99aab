import { THEME } from './constants';

export const theme = {
  colors: {
    ...THEME.colors,
    status: {
      success: THEME.colors.success,
      error: THEME.colors.error,
      warning: THEME.colors.warning,
    },
    text: THEME.colors.text,
    background: THEME.colors.background,
  },
  typography: {
    fontFamily: {
      base: 'Inter, sans-serif',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      none: '1',
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2',
    },
  },
  spacing: {
    ...THEME.spacing,
    layout: {
      page: '1.5rem',
      section: '2rem',
      gap: '1rem',
    },
  },
  breakpoints: {
    ...THEME.breakpoints,
    mobile: '320px',
    tablet: '481px',
    desktop: '769px',
  },
  shadows: {
    ...THEME.shadows,
    card: '0 1px 3px rgba(0, 0, 0, 0.1)',
    modal: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  },
  transitions: {
    ...THEME.transitions,
    hover: '150ms ease-in-out',
    modal: '200ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
  zIndex: {
    modal: 100,
    modalOverlay: 90,
    header: 50,
    navigation: 40,
  },
  radii: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '1rem',
    xl: '1.5rem',
    full: '9999px',
  },
} as const;

export type Theme = typeof theme;