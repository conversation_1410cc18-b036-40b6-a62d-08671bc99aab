import React, { useState } from 'react';
import { ArrowLeft, Plus, TrendingUp, Target, X, Check, Search, Filter, ChevronDown, ChevronUp, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import MonthYearSelector from '../components/MonthYearSelector';

interface SavingEntry {
  id: string;
  amount: number;
  purpose: string;
  date: string;
}

interface SavingTransaction {
  id: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  purpose: string;
  date: string;
  balanceAfter: number;
}

interface MonthlyGoal {
  target: number;
  current: number;
}

const mockSavingsData = [
  { month: 'Jan', amount: 1300 },
  { month: 'Fev', amount: 1700 },
  { month: 'Mar', amount: 1400 },
  { month: 'Abr', amount: 2490 },
  { month: 'Mai', amount: 2100 },
  { month: 'Jun', amount: 2800 },
  { month: 'Jul', amount: 2200 },
  { month: 'Ago', amount: 2600 },
  { month: 'Set', amount: 2900 },
  { month: 'Out', amount: 3100 },
  { month: 'Nov', amount: 2800 },
  { month: 'Dez', amount: 3200 }
];

const mockTransactions: SavingTransaction[] = [
  {
    id: '1',
    type: 'deposit',
    amount: 500.00,
    purpose: 'Reserva mensal',
    date: '2024-03-15',
    balanceAfter: 3200.00
  },
  {
    id: '2',
    type: 'deposit',
    amount: 200.00,
    purpose: 'Economia extra',
    date: '2024-03-10',
    balanceAfter: 2700.00
  },
  {
    id: '3',
    type: 'withdrawal',
    amount: 150.00,
    purpose: 'Emergência médica',
    date: '2024-03-08',
    balanceAfter: 2500.00
  },
  {
    id: '4',
    type: 'deposit',
    amount: 300.00,
    purpose: 'Freelance extra',
    date: '2024-03-05',
    balanceAfter: 2650.00
  },
  {
    id: '5',
    type: 'deposit',
    amount: 450.00,
    purpose: 'Reserva de emergência',
    date: '2024-03-01',
    balanceAfter: 2350.00
  }
];

const motivationalMessages = [
  "Bora guardar mais hoje?",
  "Pequenas economias constroem grandes liberdades",
  "Cada real guardado é um passo rumo aos seus sonhos",
  "Sua disciplina de hoje é a sua segurança de amanhã",
  "Economizar é investir no seu futuro",
  "Grandes conquistas começam com pequenas economias"
];

const CofrinhoPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentMessage] = useState(() => 
    motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)]
  );
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newSaving, setNewSaving] = useState({ amount: '', purpose: '' });
  const [monthlyGoal] = useState<MonthlyGoal>({ target: 3000, current: 2490 });
  const [selectedDate, setSelectedDate] = useState(new Date());
  
  // Transaction history state
  const [transactions, setTransactions] = useState(mockTransactions);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    type: '',
    startDate: '',
    endDate: '',
  });
  
  const currentMonthSavings = 2490;
  const totalSavings = mockSavingsData.reduce((sum, month) => sum + month.amount, 0);
  const averageSavings = totalSavings / mockSavingsData.length;
  const goalProgress = (monthlyGoal.current / monthlyGoal.target) * 100;

  // Filter transactions
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.purpose.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !filters.type || transaction.type === filters.type;
    const matchesDateRange = (!filters.startDate || transaction.date >= filters.startDate) &&
                           (!filters.endDate || transaction.date <= filters.endDate);
    
    return matchesSearch && matchesType && matchesDateRange;
  });

  const handleAddSaving = () => {
    if (!newSaving.amount || parseFloat(newSaving.amount) <= 0) return;
    
    const newTransaction: SavingTransaction = {
      id: Date.now().toString(),
      type: 'deposit',
      amount: parseFloat(newSaving.amount),
      purpose: newSaving.purpose || 'Economia geral',
      date: new Date().toISOString().split('T')[0],
      balanceAfter: currentMonthSavings + parseFloat(newSaving.amount)
    };
    
    setTransactions([newTransaction, ...transactions]);
    setNewSaving({ amount: '', purpose: '' });
    setIsAddModalOpen(false);
  };

  const toggleExpand = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const clearFilters = () => {
    setFilters({
      type: '',
      startDate: '',
      endDate: '',
    });
  };

  const handleEdit = (id: string, updates: Partial<SavingTransaction>) => {
    setTransactions(transactions.map(transaction =>
      transaction.id === id ? { ...transaction, ...updates } : transaction
    ));
    setExpandedId(null);
  };

  const handleDelete = (id: string) => {
    if (confirm('Tem certeza que deseja excluir esta transação?')) {
      setTransactions(transactions.filter(transaction => transaction.id !== id));
    }
  };

  const handleNewCofrinho = () => {
    // Implementar redirecionamento para tela de novo cofrinho
    console.log('Redirecionando para nova tela de cofrinho');
    // navigate('/finances/cofrinho/new');
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      {/* Header Padronizado Global */}
      <div className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-50">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          {/* Botão Voltar */}
          <button 
            onClick={() => navigate('/finances')}
            className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>

          {/* Título Centralizado */}
          <h1 className="text-xl font-bold text-gray-900">Cofrinho</h1>

          {/* Botão + com redirecionamento para novo cofrinho */}
          <button
            onClick={handleNewCofrinho}
            className="w-10 h-10 bg-[#212121] rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <span className="text-white text-xl font-light">+</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 pt-20 space-y-6">
        {/* Motivational Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {currentMessage}
          </h2>
          <p className="text-gray-600 text-lg">
            Continue construindo seu futuro financeiro
          </p>
        </motion.div>

        {/* Card Principal com Seletor de Data Integrado */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              Economizado este mês
            </h2>
            
            {/* Seletor de Data Discreto */}
            <div className="flex justify-center mb-4">
              <MonthYearSelector
                selectedDate={selectedDate}
                onDateChange={setSelectedDate}
              />
            </div>
            
            {/* Valor Total */}
            <p className="text-4xl font-bold text-[#4CAF50] mb-4">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(currentMonthSavings)}
            </p>
            
            {/* Métricas */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Total acumulado</p>
                <p className="font-semibold text-gray-900">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(totalSavings)}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Média mensal</p>
                <p className="font-semibold text-gray-900">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(averageSavings)}
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Monthly Goal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
              <Target size={20} className="text-[#B4EB00]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Meta Mensal</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Progresso</span>
              <span className="font-semibold text-gray-900">
                {Math.round(goalProgress)}%
              </span>
            </div>
            
            <div className="w-full bg-gray-100 rounded-full h-3 overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${goalProgress}%` }}
                transition={{ duration: 1, delay: 0.5 }}
                className="h-full bg-gradient-to-r from-[#B4EB00] to-[#9FD700] rounded-full"
              />
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(monthlyGoal.current)}
              </span>
              <span className="text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(monthlyGoal.target)}
              </span>
            </div>
          </div>
        </motion.div>

        {/* 12-Month History Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
              <TrendingUp size={20} className="text-[#B4EB00]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Histórico de 12 Meses</h3>
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockSavingsData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                <XAxis 
                  dataKey="month" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6C6C6C' }}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6C6C6C' }}
                  tickFormatter={(value) => 
                    new Intl.NumberFormat('pt-BR', {
                      notation: 'compact',
                      compactDisplay: 'short'
                    }).format(value)
                  }
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: 'none',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0,0,0,0.1)',
                    padding: '8px'
                  }}
                  formatter={(value: number) => [
                    new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(value),
                    'Economizado'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="amount" 
                  stroke="#B4EB00" 
                  strokeWidth={3}
                  dot={{ fill: '#B4EB00', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#B4EB00', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Transaction History */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
              <Calendar size={20} className="text-[#B4EB00]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Histórico de Transações</h3>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar transação..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
              />
            </div>
            <button 
              onClick={() => setIsFilterOpen(true)}
              className="h-12 px-6 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
            >
              <Filter size={20} className="text-gray-600" />
              <span>Filtros</span>
              {(filters.type || filters.startDate || filters.endDate) && (
                <span className="w-2 h-2 rounded-full bg-[#B4EB00]" />
              )}
            </button>
          </div>

          {/* Transaction List */}
          <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
            {filteredTransactions.map(transaction => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="overflow-hidden border border-gray-100 rounded-xl hover:border-[#B4EB00] transition-colors"
              >
                <div className="p-4 flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        transaction.type === 'deposit' 
                          ? 'bg-[#4CAF50]/10 text-[#4CAF50]' 
                          : 'bg-[#FF3B30]/10 text-[#FF3B30]'
                      }`}>
                        {transaction.type === 'deposit' ? 'Depósito' : 'Retirada'}
                      </span>
                      <h3 className="font-medium text-[#1C1C1C]">{transaction.purpose}</h3>
                    </div>
                    <div className="flex items-center gap-4 mt-1">
                      <span className="text-sm text-[#6C6C6C]">
                        {new Date(transaction.date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}
                      </span>
                      <span className="text-sm text-[#6C6C6C]">
                        Saldo: {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(transaction.balanceAfter)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className={`font-medium ${
                      transaction.type === 'deposit' ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                    }`}>
                      {transaction.type === 'deposit' ? '+' : '-'}
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(transaction.amount)}
                    </span>
                    <button
                      onClick={() => toggleExpand(transaction.id)}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      {expandedId === transaction.id ? (
                        <ChevronUp size={20} className="text-gray-600" />
                      ) : (
                        <ChevronDown size={20} className="text-gray-600" />
                      )}
                    </button>
                  </div>
                </div>

                <AnimatePresence>
                  {expandedId === transaction.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="border-t border-gray-100"
                    >
                      <div className="p-4 space-y-4">
                        <div>
                          <label className="block text-sm text-gray-600 mb-1">Valor</label>
                          <input
                            type="number"
                            value={transaction.amount}
                            onChange={(e) => handleEdit(transaction.id, { amount: parseFloat(e.target.value) })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                            step="0.01"
                          />
                        </div>

                        <div>
                          <label className="block text-sm text-gray-600 mb-1">Finalidade</label>
                          <input
                            type="text"
                            value={transaction.purpose}
                            onChange={(e) => handleEdit(transaction.id, { purpose: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                          />
                        </div>

                        <div>
                          <label className="block text-sm text-gray-600 mb-1">Data</label>
                          <input
                            type="date"
                            value={transaction.date}
                            onChange={(e) => handleEdit(transaction.id, { date: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                          />
                        </div>

                        <div className="flex justify-end gap-2 pt-2">
                          <button
                            onClick={() => handleDelete(transaction.id)}
                            className="px-4 py-2 text-[#FF3B30] hover:bg-red-50 rounded-lg transition-colors"
                          >
                            Excluir
                          </button>
                          <button
                            onClick={() => setExpandedId(null)}
                            className="px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                          >
                            Salvar
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Quick Action Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center"
        >
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-[#B4EB00] to-[#9FD700] text-gray-900 rounded-2xl font-semibold hover:shadow-lg transition-all"
          >
            <Plus size={24} />
            <span>Economizar Mais Hoje</span>
          </button>
        </motion.div>
      </div>

      {/* Filter Modal */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
              onClick={() => setIsFilterOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
            >
              <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-gray-900">Filtros</h2>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X size={20} className="text-gray-600" />
                  </button>
                </div>

                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tipo de operação
                    </label>
                    <select
                      value={filters.type}
                      onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="">Todos os tipos</option>
                      <option value="deposit">Depósitos</option>
                      <option value="withdrawal">Retiradas</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data inicial
                    </label>
                    <input
                      type="date"
                      value={filters.startDate}
                      onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data final
                    </label>
                    <input
                      type="date"
                      value={filters.endDate}
                      onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                  <button
                    onClick={clearFilters}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Limpar
                  </button>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="flex-1 px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                  >
                    Aplicar
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Add Saving Modal */}
      <AnimatePresence>
        {isAddModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
              onClick={() => setIsAddModalOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
            >
              <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Nova Economia
                  </h2>
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X size={20} className="text-gray-600" />
                  </button>
                </div>

                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Valor (R$)
                    </label>
                    <input
                      type="number"
                      value={newSaving.amount}
                      onChange={(e) => setNewSaving({ ...newSaving, amount: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent text-lg"
                      placeholder="0,00"
                      step="0.01"
                      min="0"
                      autoFocus
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Finalidade (opcional)
                    </label>
                    <input
                      type="text"
                      value={newSaving.purpose}
                      onChange={(e) => setNewSaving({ ...newSaving, purpose: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                      placeholder="Ex: Reserva de emergência"
                    />
                  </div>
                </div>

                <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleAddSaving}
                    className="flex-1 px-4 py-3 bg-gradient-to-r from-[#B4EB00] to-[#9FD700] text-gray-900 rounded-xl font-medium hover:shadow-md transition-all flex items-center justify-center gap-2"
                  >
                    <Check size={20} />
                    Salvar
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CofrinhoPage;