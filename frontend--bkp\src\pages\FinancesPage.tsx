import React, { useState } from 'react';
import { TrendingUp, TrendingDown, Wallet, ChevronRight, Plus, Edit2, PiggyBank } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import <PERSON><PERSON><PERSON> from '../components/PieChart';
import Header from '../components/Header';
import AnnualOverviewCard from '../components/AnnualOverviewCard';
import AddTransactionModal from '../components/AddTransactionModal';
import EditBudgetModal from '../components/EditBudgetModal';

// Dados mockados mais robustos e realistas
const mockData = {
  budget: 5000,
  income: 4800,
  expenses: 2310,
  savings: 2490,
  available: 2490,
  categories: [
    { name: 'Alimentação', value: 800, color: '#B4EB00' },
    { name: 'Transporte', value: 500, color: '#212121' },
    { name: 'La<PERSON>', value: 400, color: '#6C6C6C' },
    { name: '<PERSON>rad<PERSON>', value: 400, color: '#BBBBBB' },
    { name: 'Outros', value: 210, color: '#E5E7EB' }
  ],
  // Dados anuais expandidos com 12 meses completos
  annualData: [
    { month: 'Jan', income: 4500, expenses: 3200, savings: 1300 },
    { month: 'Fev', income: 4800, expenses: 3100, savings: 1700 },
    { month: 'Mar', income: 5200, expenses: 3800, savings: 1400 },
    { month: 'Abr', income: 4800, expenses: 2310, savings: 2490 },
    { month: 'Mai', income: 5100, expenses: 3400, savings: 1700 },
    { month: 'Jun', income: 4900, expenses: 3200, savings: 1700 },
    { month: 'Jul', income: 5300, expenses: 3600, savings: 1700 },
    { month: 'Ago', income: 5000, expenses: 3300, savings: 1700 },
    { month: 'Set', income: 5200, expenses: 3500, savings: 1700 },
    { month: 'Out', income: 5400, expenses: 3700, savings: 1700 },
    { month: 'Nov', income: 5100, expenses: 3400, savings: 1700 },
    { month: 'Dez', income: 5500, expenses: 3800, savings: 1700 }
  ]
};

const FinancesPage: React.FC = () => {
  const navigate = useNavigate();
  const [data, setData] = useState(mockData);
  const [isAddTransactionModalOpen, setIsAddTransactionModalOpen] = useState(false);
  const [isEditBudgetModalOpen, setIsEditBudgetModalOpen] = useState(false);

  const handleBudgetUpdate = (newBudget: number) => {
    setData(prev => ({ ...prev, budget: newBudget }));
  };

  const handleAddTransaction = (transaction: {
    type: 'income' | 'expense' | 'savings';
    amount: number;
    category: string;
    description: string;
  }) => {
    // Mock implementation - in real app, this would save to database
    console.log('Adding transaction:', transaction);
  };

  // Reorganized quadrant data with new positioning
  const quadrantData = [
    {
      title: 'Cofrinho',
      value: data.savings,
      trend: { value: 15, label: 'este mês' },
      icon: PiggyBank,
      route: '/finances/cofrinho'
    },
    {
      title: 'Receita',
      value: data.income,
      trend: { value: 8, label: 'vs mês anterior' },
      icon: TrendingUp,
      route: '/finances/income'
    },
    {
      title: 'Despesas',
      value: data.expenses,
      trend: { value: -3, label: 'vs mês anterior' },
      icon: TrendingDown,
      route: '/finances/expenses'
    },
    {
      title: 'Disponível',
      value: data.available,
      trend: { value: 12, label: 'este mês' },
      icon: Wallet,
      route: '/finances/overview'
    }
  ];

  return (
    <div className="relative min-h-screen bg-[#F7F7F7]">
      <Header 
        onAddClick={() => setIsAddTransactionModalOpen(true)}
        onEditBudgetClick={() => setIsEditBudgetModalOpen(true)}
      />

      <div className="max-w-4xl mx-auto pt-20 px-4 pb-24 md:pb-6 space-y-6">
        {/* Financial quadrant */}
        <div className="grid grid-cols-2 gap-4">
          {quadrantData.map((item, index) => {
            const Icon = item.icon;
            const isCofrinho = item.title === 'Cofrinho';
            return (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => item.route !== '/finances/overview' && navigate(item.route)}
                className={`
                  p-4 rounded-xl shadow-sm transition-all
                  ${isCofrinho 
                    ? 'bg-gradient-to-br from-[#B4EB00] to-[#9FD700] cursor-pointer hover:shadow-md' 
                    : 'bg-white'
                  }
                  ${item.route !== '/finances/overview' && !isCofrinho ? 'cursor-pointer hover:shadow-md' : ''}
                `}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg ${isCofrinho ? 'bg-white/20' : 'bg-gray-50'}`}>
                    <Icon size={18} className={isCofrinho ? 'text-gray-900' : 'text-gray-600'} />
                  </div>
                  <span className={`text-xs ${isCofrinho ? 'text-gray-900 font-medium' : 'text-gray-600'}`}>
                    {item.title}
                  </span>
                </div>
                <p className={`text-xl font-bold ${isCofrinho ? 'text-gray-900' : 'text-gray-900'}`}>
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(item.value)}
                </p>
                {item.trend && (
                  <p className={`text-xs mt-1 ${
                    isCofrinho 
                      ? 'text-gray-700'
                      : item.trend.value >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                  }`}>
                    {item.trend.value > 0 ? '+' : ''}{item.trend.value}% {item.trend.label}
                  </p>
                )}
              </motion.div>
            );
          })}
        </div>

        {/* Annual Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <AnnualOverviewCard data={data.annualData} />
        </motion.div>

        {/* Categories section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Distribuição de gastos por categoria
            </h2>
            <button
              onClick={() => navigate('/finances/categories')}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Edit2 size={20} className="text-gray-600" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Pie chart */}
            <div className="flex justify-center items-center">
              <PieChart data={data.categories} size={240} />
            </div>

            {/* Categories list */}
            <div className="space-y-3">
              {data.categories.map((category) => (
                <motion.div
                  key={category.name}
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(category.value)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {((category.value / data.expenses) * 100).toFixed(1)}%
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Add Transaction Modal */}
      <AddTransactionModal
        isOpen={isAddTransactionModalOpen}
        onClose={() => setIsAddTransactionModalOpen(false)}
        onAdd={handleAddTransaction}
      />

      {/* Edit Budget Modal */}
      <EditBudgetModal
        isOpen={isEditBudgetModalOpen}
        onClose={() => setIsEditBudgetModalOpen(false)}
        currentBudget={data.budget}
        onSave={handleBudgetUpdate}
      />
    </div>
  );
};

export default FinancesPage;