import React, { useState } from 'react';
import { Search, Plus, Heart, MoreVertical, X, Check } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '../components/Header';
import IdeaNotepad from '../components/IdeaNotepad';

interface Idea {
  id: string;
  title: string;
  description: string;
  content?: string;
  date: string;
  isFavorite: boolean;
  category: string;
}

interface NewIdea {
  title: string;
  description: string;
  category: string;
}

const mockIdeas: Idea[] = [
  {
    id: '1',
    title: 'App para Freelancers',
    description: 'Uma plataforma que ajude freelancers a gerenciar projetos, prazos e pagamentos de forma simples e intuitiva.',
    content: 'Desenvolver uma plataforma que ajude freelancers a gerenciar projetos, prazos e pagamentos de forma simples e intuitiva.\n\nFuncionalidades principais:\n- Dashboard com visão geral dos projetos\n- Sistema de tracking de tempo\n- Geração automática de relatórios\n- Integração com sistemas de pagamento\n- Chat integrado com clientes\n\nTecnologias:\n- React/Next.js para o frontend\n- Node.js/Express para o backend\n- PostgreSQL para banco de dados\n- Stripe para pagamentos\n\nModelo de negócio:\n- Freemium com planos pagos\n- Comissão sobre transações\n- Planos empresariais para agências',
    date: '2025-01-28',
    isFavorite: true,
    category: 'App'
  },
  {
    id: '2',
    title: 'Sistema de Economia Doméstica',
    description: 'Aplicativo para ajudar famílias a controlarem gastos e economizarem dinheiro.',
    content: 'Um aplicativo focado em ajudar famílias brasileiras a controlarem seus gastos e desenvolverem o hábito de economizar.\n\nProblema identificado:\n- Muitas famílias não têm controle dos gastos\n- Dificuldade em criar o hábito de poupar\n- Falta de educação financeira\n\nSolução proposta:\n- Interface simples e intuitiva\n- Gamificação para incentivar economia\n- Dicas personalizadas baseadas no perfil\n- Metas de economia com recompensas\n- Relatórios visuais e fáceis de entender\n\nDiferenciais:\n- Foco no público brasileiro\n- Integração com bancos nacionais\n- Conteúdo educativo em português\n- Suporte para economia familiar',
    date: '2025-01-27',
    isFavorite: false,
    category: 'App'
  },
  {
    id: '3',
    title: 'Curso Online de Design',
    description: 'Plataforma de ensino focada em design UI/UX para iniciantes.',
    content: 'Criar uma plataforma de ensino online especializada em design UI/UX, voltada especificamente para iniciantes que querem entrar na área.\n\nEstrutura do curso:\n1. Fundamentos do design\n2. Princípios de UI\n3. Experiência do usuário (UX)\n4. Ferramentas (Figma, Adobe XD)\n5. Portfolio e carreira\n\nMetodologia:\n- Aprendizado baseado em projetos\n- Feedback personalizado\n- Comunidade ativa de alunos\n- Mentoria com profissionais\n- Certificação reconhecida\n\nModelo de negócio:\n- Curso pago com acesso vitalício\n- Mentoria premium\n- Workshops especializados\n- Parcerias com empresas para colocação',
    date: '2025-01-26',
    isFavorite: true,
    category: 'Educação'
  }
];

const categories = ['App', 'Projeto pessoal', 'Estudo', 'Negócio', 'Educação', 'Outro'];

const IdeasPage: React.FC = () => {
  const [ideas, setIdeas] = useState(mockIdeas);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFavorites, setShowFavorites] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [editMode, setEditMode] = useState<string | null>(null);
  const [newIdea, setNewIdea] = useState<NewIdea>({
    title: '',
    description: '',
    category: categories[0]
  });
  const [editingIdea, setEditingIdea] = useState<Idea | null>(null);
  const [selectedIdea, setSelectedIdea] = useState<Idea | null>(null);

  const filteredIdeas = ideas.filter(idea => {
    const matchesSearch = (
      idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      idea.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    const matchesFavorites = showFavorites ? idea.isFavorite : true;
    return matchesSearch && matchesFavorites;
  });

  const groupedIdeas = filteredIdeas.reduce((groups, idea) => {
    const date = new Date(idea.date);
    const dateKey = date.toISOString().split('T')[0];
    
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(idea);
    return groups;
  }, {} as Record<string, Idea[]>);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Hoje';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Ontem';
    } else {
      return new Intl.DateTimeFormat('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      }).format(date);
    }
  };

  const todayIdeas = ideas.filter(idea => 
    new Date(idea.date).toDateString() === new Date().toDateString()
  ).length;

  const toggleFavorite = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setIdeas(ideas.map(idea =>
      idea.id === id ? { ...idea, isFavorite: !idea.isFavorite } : idea
    ));
  };

  const handleAddIdea = () => {
    if (!newIdea.title.trim()) return;

    const idea: Idea = {
      id: Date.now().toString(),
      title: newIdea.title,
      description: newIdea.description,
      content: newIdea.description, // Initialize content with description
      category: newIdea.category,
      date: new Date().toISOString().split('T')[0],
      isFavorite: false
    };

    setIdeas([idea, ...ideas]);
    setNewIdea({ title: '', description: '', category: categories[0] });
    setIsModalOpen(false);
  };

  const handleUpdateIdea = (id: string) => {
    if (!editingIdea) return;

    setIdeas(ideas.map(idea =>
      idea.id === id ? { ...editingIdea } : idea
    ));
    setEditMode(null);
    setEditingIdea(null);
  };

  const handleDeleteIdea = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Tem certeza que deseja excluir esta ideia?')) {
      setIdeas(ideas.filter(idea => idea.id !== id));
    }
  };

  const handleIdeaClick = (idea: Idea) => {
    setSelectedIdea(idea);
  };

  const handleNotepadClose = () => {
    setSelectedIdea(null);
  };

  const handleNotepadSave = (id: string, updates: { title: string; content: string }) => {
    setIdeas(ideas.map(idea =>
      idea.id === id ? {
        ...idea,
        title: updates.title,
        description: updates.content.slice(0, 100) + (updates.content.length > 100 ? '...' : ''),
        content: updates.content
      } : idea
    ));
  };

  const handleMoreOptions = (idea: Idea, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditMode(idea.id);
    setEditingIdea(idea);
    setExpandedId(expandedId === idea.id ? null : idea.id);
  };

  return (
    <>
      <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
        <Header onAddClick={() => setIsModalOpen(true)} />

        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="pt-20">
            {/* Search and filters */}
            <div className="grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 mb-6">
              <div className="relative">
                <Search 
                  size={20} 
                  className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" 
                />
                <input
                  type="text"
                  placeholder="Buscar ideias..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full h-12 pl-12 pr-4 bg-white rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-transparent"
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowFavorites(!showFavorites)}
                  className={`px-6 py-2 rounded-full transition-colors ${
                    showFavorites
                      ? 'bg-black text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  Favoritos
                </button>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="px-6 py-2 bg-black text-white rounded-full hover:bg-opacity-90 transition-colors flex items-center gap-2"
                >
                  <Plus size={20} />
                  <span className="hidden md:inline">Adicionar</span>
                </button>
              </div>
            </div>

            {/* Summary card */}
            <div className="bg-white rounded-xl p-6 shadow-sm mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Suas ideias</h1>
                  <p className="text-gray-600">{ideas.length} ideias no total</p>
                </div>
                {todayIdeas > 0 && (
                  <span className="text-sm text-gray-600">{todayIdeas} hoje</span>
                )}
              </div>
            </div>

            {/* Ideas grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(groupedIdeas).map(([date, dateIdeas]) => (
                <div key={date} className="space-y-4">
                  <h2 className="text-sm text-gray-600 mb-3 pl-1">
                    {formatDate(date)}
                  </h2>
                  <div className="space-y-4">
                    {dateIdeas.map(idea => (
                      <motion.div
                        key={idea.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        whileHover={{ y: -2 }}
                        className="bg-white rounded-xl shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-all"
                        onClick={() => handleIdeaClick(idea)}
                      >
                        <div className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 line-clamp-2 mb-2">
                                {idea.title}
                              </h3>
                              <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                                {idea.description}
                              </p>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500">
                                  {new Date(idea.date).toLocaleDateString('pt-BR')}
                                </span>
                                <span className="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                                  {idea.category}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-start gap-1 ml-2">
                              <button
                                onClick={(e) => toggleFavorite(idea.id, e)}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                              >
                                <Heart
                                  size={18}
                                  className={idea.isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}
                                />
                              </button>
                              <button 
                                onClick={(e) => handleMoreOptions(idea, e)}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                              >
                                <MoreVertical size={18} className="text-gray-400" />
                              </button>
                            </div>
                          </div>
                        </div>

                        <AnimatePresence>
                          {expandedId === idea.id && (
                            <motion.div
                              initial={{ height: 0 }}
                              animate={{ height: 'auto' }}
                              exit={{ height: 0 }}
                              className="border-t border-gray-100"
                            >
                              <div className="p-4 space-y-4">
                                <div>
                                  <label className="block text-sm text-gray-600 mb-1">
                                    Título
                                  </label>
                                  <input
                                    type="text"
                                    value={editingIdea?.title ?? idea.title}
                                    onChange={(e) => setEditingIdea(prev => prev ? { ...prev, title: e.target.value } : null)}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm text-gray-600 mb-1">
                                    Descrição
                                  </label>
                                  <textarea
                                    value={editingIdea?.description ?? idea.description}
                                    onChange={(e) => setEditingIdea(prev => prev ? { ...prev, description: e.target.value } : null)}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm text-gray-600 mb-1">
                                    Categoria
                                  </label>
                                  <select
                                    value={editingIdea?.category ?? idea.category}
                                    onChange={(e) => setEditingIdea(prev => prev ? { ...prev, category: e.target.value } : null)}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                  >
                                    {categories.map(category => (
                                      <option key={category} value={category}>
                                        {category}
                                      </option>
                                    ))}
                                  </select>
                                </div>

                                <div className="flex justify-between pt-2">
                                  <button
                                    onClick={(e) => handleDeleteIdea(idea.id, e)}
                                    className="px-4 py-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                  >
                                    Excluir
                                  </button>
                                  <div className="flex gap-2">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setExpandedId(null);
                                        setEditingIdea(null);
                                      }}
                                      className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                                    >
                                      Cancelar
                                    </button>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleUpdateIdea(idea.id);
                                      }}
                                      className="px-4 py-2 bg-black text-white rounded-lg hover:bg-opacity-90 transition-colors"
                                    >
                                      Salvar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Empty state */}
            {filteredIdeas.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus size={24} className="text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm ? 'Nenhuma ideia encontrada' : 'Suas primeiras ideias'}
                </h3>
                <p className="text-gray-500 text-sm mb-4">
                  {searchTerm 
                    ? 'Tente ajustar sua busca ou criar uma nova ideia'
                    : 'Comece capturando suas ideias e inspirações aqui'
                  }
                </p>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-black text-white rounded-xl hover:bg-opacity-90 transition-colors"
                >
                  <Plus size={20} />
                  Adicionar primeira ideia
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Add Idea Modal */}
        <AnimatePresence>
          {isModalOpen && (
            <>
              {/* Backdrop Overlay */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
                onClick={() => setIsModalOpen(false)}
              />
              
              {/* Modal Container */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
                className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
              >
                <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                  {/* Header */}
                  <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                    <h2 className="text-xl font-semibold text-gray-900">
                      Adicionar nova ideia
                    </h2>
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <X size={20} className="text-gray-600" />
                    </button>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-y-auto p-6 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Título
                      </label>
                      <input
                        type="text"
                        value={newIdea.title}
                        onChange={(e) => setNewIdea({ ...newIdea, title: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                        placeholder="Digite o título da sua ideia"
                        autoFocus
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Descrição (opcional)
                      </label>
                      <textarea
                        value={newIdea.description}
                        onChange={(e) => setNewIdea({ ...newIdea, description: e.target.value })}
                        rows={4}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                        placeholder="Descreva sua ideia..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Categoria
                      </label>
                      <select
                        value={newIdea.category}
                        onChange={(e) => setNewIdea({ ...newIdea, category: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                      >
                        {categories.map(category => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={handleAddIdea}
                      disabled={!newIdea.title.trim()}
                      className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Check size={20} />
                      Adicionar
                    </button>
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Notepad Interface */}
      <AnimatePresence>
        {selectedIdea && (
          <IdeaNotepad
            id={selectedIdea.id}
            title={selectedIdea.title}
            content={selectedIdea.content || selectedIdea.description}
            onClose={handleNotepadClose}
            onSave={handleNotepadSave}
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default IdeasPage;