import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ChevronDown, PiggyBank, Lightbulb, CheckCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import MonthYearSelector from '../components/MonthYearSelector';
import ProgressBar from '../components/ProgressBar';

interface MonthData {
  month: number;
  year: number;
  appointments: {
    completed: number;
    total: number;
  };
  savings: number;
  ideas: number;
}

const mockData: MonthData[] = [
  {
    month: 4,
    year: 2025,
    appointments: { completed: 18, total: 24 },
    savings: 640,
    ideas: 6,
  },
  {
    month: 3,
    year: 2025,
    appointments: { completed: 22, total: 24 },
    savings: 870,
    ideas: 4,
  },
  {
    month: 2,
    year: 2025,
    appointments: { completed: 16, total: 20 },
    savings: 550,
    ideas: 3,
  },
  {
    month: 1,
    year: 2025,
    appointments: { completed: 19, total: 22 },
    savings: 720,
    ideas: 5,
  },
  {
    month: 0,
    year: 2025,
    appointments: { completed: 20, total: 25 },
    savings: 680,
    ideas: 4,
  },
  {
    month: 11,
    year: 2024,
    appointments: { completed: 15, total: 18 },
    savings: 590,
    ideas: 3,
  },
];

type SortOption = 'performance' | 'savings' | 'ideas' | 'chronological';

const MonthlyProgressPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [sortBy, setSortBy] = useState<SortOption>('chronological');
  const [isSortOpen, setIsSortOpen] = useState(false);

  const sortOptions: { value: SortOption; label: string }[] = [
    { value: 'performance', label: 'Maior desempenho' },
    { value: 'savings', label: 'Mais economias' },
    { value: 'ideas', label: 'Mais ideias' },
    { value: 'chronological', label: 'Cronológico' },
  ];

  const sortData = (data: MonthData[]): MonthData[] => {
    const sorted = [...data];
    switch (sortBy) {
      case 'performance':
        return sorted.sort((a, b) => 
          (b.appointments.completed / b.appointments.total) - 
          (a.appointments.completed / a.appointments.total)
        );
      case 'savings':
        return sorted.sort((a, b) => b.savings - a.savings);
      case 'ideas':
        return sorted.sort((a, b) => b.ideas - a.ideas);
      default:
        return sorted.sort((a, b) => {
          const dateA = new Date(a.year, a.month);
          const dateB = new Date(b.year, b.month);
          return dateB.getTime() - dateA.getTime();
        });
    }
  };

  const getBestMonth = () => {
    return mockData.reduce((best, current) => {
      const bestPerformance = best.appointments.completed / best.appointments.total;
      const currentPerformance = current.appointments.completed / current.appointments.total;
      return currentPerformance > bestPerformance ? current : best;
    });
  };

  const bestMonth = getBestMonth();
  const bestMonthPerformance = Math.round((bestMonth.appointments.completed / bestMonth.appointments.total) * 100);

  const formatMonth = (month: number, year: number) => {
    return new Date(year, month).toLocaleDateString('pt-BR', {
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6">
      {/* Header with Back Button */}
      <div className="flex items-center justify-between mb-8">
        <button 
          onClick={() => navigate('/')}
          className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        
        <h1 className="text-xl font-bold text-gray-900">Progresso Mensal</h1>
        
        <div className="w-10" /> {/* Spacer for alignment */}
      </div>

      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Resumo do seu progresso
          </h2>
          <p className="text-gray-600">
            Compare seus resultados entre os últimos meses. Veja o quanto você está evoluindo.
          </p>
        </div>

        {/* Horizontal Layout for Mobile - Month Selector and Sort Button */}
        <div className="flex items-center justify-between gap-3">
          {/* Month/Year Selector - Left Side */}
          <div className="flex-shrink-0">
            <MonthYearSelector
              selectedDate={selectedDate}
              onDateChange={setSelectedDate}
            />
          </div>

          {/* Sort Button - Right Side */}
          <div className="relative flex-shrink-0">
            <button
              onClick={() => setIsSortOpen(!isSortOpen)}
              className="h-10 px-3 md:px-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all flex items-center gap-1.5 md:gap-2 text-sm"
            >
              <span className="text-gray-900 text-xs md:text-sm">Ordenar</span>
              <ChevronDown
                size={14}
                className={`text-gray-400 transition-transform ${
                  isSortOpen ? 'rotate-180' : ''
                }`}
              />
            </button>

            <AnimatePresence>
              {isSortOpen && (
                <>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 z-40"
                    onClick={() => setIsSortOpen(false)}
                  />
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-lg z-50 py-2"
                  >
                    {sortOptions.map(option => (
                      <button
                        key={option.value}
                        onClick={() => {
                          setSortBy(option.value);
                          setIsSortOpen(false);
                        }}
                        className={`w-full px-4 py-2 text-left text-sm transition-colors ${
                          sortBy === option.value
                            ? 'bg-gray-100 text-gray-900'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        {option.label}
                      </button>
                    ))}
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[#212121] text-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-start justify-between">
            <div>
              <h2 className="text-xl font-semibold mb-2">
                Seu melhor mês até agora foi {formatMonth(bestMonth.month, bestMonth.year)}
              </h2>
              <div className="space-y-4">
                <div>
                  <p className="text-white/80 mb-1">
                    {bestMonth.appointments.completed} de {bestMonth.appointments.total} compromissos concluídos
                  </p>
                  <ProgressBar
                    percentage={bestMonthPerformance}
                    color="#82E882"
                    bgColor="rgba(255,255,255,0.1)"
                  />
                </div>
                <p className="text-2xl font-bold">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(bestMonth.savings)} economizados
                </p>
              </div>
            </div>
            <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
              <CheckCircle size={32} className="text-[#212121]" />
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {sortData(mockData).map((data, index) => (
            <motion.div
              key={`${data.month}-${data.year}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-sm"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {formatMonth(data.month, data.year)}
              </h3>

              <div className="space-y-6">
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    {data.appointments.completed} de {data.appointments.total} compromissos concluídos
                  </p>
                  <ProgressBar
                    percentage={Math.round((data.appointments.completed / data.appointments.total) * 100)}
                    color="#82E882"
                    bgColor="#E0E0E0"
                    height={5}
                  />
                </div>

                <div className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-gray-100 rounded-lg">
                      <PiggyBank size={18} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Economizados</p>
                      <p className="font-medium text-gray-900">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(data.savings)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-gray-100 rounded-lg">
                      <Lightbulb size={18} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Ideias salvas</p>
                      <p className="font-medium text-gray-900">{data.ideas}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MonthlyProgressPage;