import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Calendar, ChevronLeft, ChevronRight, Clock, CheckCircle, Sun, Sunset, Moon } from 'lucide-react';
import TaskProgress from '../components/TaskProgress';
import TaskItem from '../components/TaskItem';
import Header from '../components/Header';
import AddTaskModal from '../components/AddTaskModal';
import ProgressCircle from '../components/ProgressCircle';
import { motion, AnimatePresence } from 'framer-motion';

interface Task {
  id: string;
  title: string;
  description?: string;
  time?: string;
  completed: boolean;
  type: 'appointment' | 'task';
  category?: string;
  date: string;
  completedAt?: string; // Track when task was completed
}

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Reunião com cliente',
    description: 'Discutir próximos passos do projeto e definir cronograma',
    time: '09:00',
    completed: true,
    type: 'appointment',
    category: 'Reunião',
    date: new Date().toISOString().split('T')[0],
    completedAt: '2024-03-15T09:30:00'
  },
  {
    id: '2',
    title: 'Almoço com equipe',
    description: 'Restaurante italiano na Rua Augusta',
    time: '12:30',
    completed: false,
    type: 'appointment',
    category: 'Compromisso',
    date: new Date().toISOString().split('T')[0]
  },
  {
    id: '3',
    title: 'Preparar apresentação',
    description: 'Slides para reunião de amanhã com stakeholders',
    completed: true,
    type: 'task',
    category: 'Tarefa',
    date: new Date().toISOString().split('T')[0],
    completedAt: '2024-03-15T14:15:00'
  },
  {
    id: '4',
    title: 'Revisar documentos',
    description: 'Contratos pendentes de análise',
    completed: false,
    type: 'task',
    category: 'Responsabilidade',
    date: new Date().toISOString().split('T')[0]
  },
  {
    id: '5',
    title: 'Consulta médica',
    description: 'Check-up anual',
    time: '14:00',
    completed: false,
    type: 'appointment',
    category: 'Compromisso',
    date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  },
  {
    id: '6',
    title: 'Reunião de planejamento',
    description: 'Definir metas do próximo trimestre',
    time: '10:00',
    completed: false,
    type: 'appointment',
    category: 'Reunião',
    date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  },
  {
    id: '7',
    title: 'Exercícios físicos',
    description: 'Academia - treino de pernas',
    time: '07:00',
    completed: true,
    type: 'appointment',
    category: 'Compromisso',
    date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    completedAt: '2024-03-14T07:45:00'
  },
  {
    id: '8',
    title: 'Estudar React',
    description: 'Revisar hooks e context',
    completed: true,
    type: 'task',
    category: 'Tarefa',
    date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    completedAt: '2024-03-14T20:30:00'
  },
  {
    id: '9',
    title: 'Chamada com fornecedor',
    description: 'Negociar novos preços',
    time: '19:30',
    completed: false,
    type: 'appointment',
    category: 'Chamada',
    date: new Date().toISOString().split('T')[0]
  },
  {
    id: '10',
    title: 'Leitura técnica',
    description: 'Artigo sobre arquitetura de software',
    time: '21:00',
    completed: false,
    type: 'task',
    category: 'Tarefa',
    date: new Date().toISOString().split('T')[0]
  }
];

const TasksPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [tasks, setTasks] = useState(mockTasks);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isCalendarExpanded, setIsCalendarExpanded] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Filter tasks for the selected date
  const getTasksForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return tasks.filter(task => task.date === dateString);
  };

  const tasksForSelectedDate = getTasksForDate(selectedDate);
  const completedTasks = tasksForSelectedDate.filter(task => task.completed).length;
  const totalTasks = tasksForSelectedDate.length;

  // Date navigation functions
  const navigateDay = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setSelectedDate(newDate);
  };

  const formatDateDisplay = (date: Date) => {
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    
    if (isToday) {
      return 'Hoje';
    }
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return 'Ontem';
    }
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const isTomorrow = date.toDateString() === tomorrow.toDateString();
    
    if (isTomorrow) {
      return 'Amanhã';
    }
    
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    });
  };

  // Calendar functions
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const days = [];
    
    // Add padding days from previous month
    const firstDayOfWeek = firstDay.getDay();
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month - 1, prevMonthLastDay - i);
      days.push({ date, isPadding: true });
    }
    
    // Add days of current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      days.push({ date, isPadding: false });
    }
    
    // Add padding days from next month
    const remainingDays = 42 - days.length; // 6 rows * 7 days
    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(year, month + 1, i);
      days.push({ date, isPadding: true });
    }
    
    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentMonth(newMonth);
  };

  const formatMonth = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', { 
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date) => {
    return date.toDateString() === selectedDate.toDateString();
  };

  const hasEvents = (date: Date) => {
    return getTasksForDate(date).length > 0;
  };

  const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  // Group tasks by time shifts (4h-12h, 12h-18h, 18h-4h)
  const groupTasksByShifts = (tasks: Task[]) => {
    const shifts = {
      morning: [] as Task[],    // 04:00 - 12:00
      afternoon: [] as Task[],  // 12:00 - 18:00
      night: [] as Task[],      // 18:00 - 04:00
      completed: [] as Task[]   // Completed tasks
    };

    tasks.forEach(task => {
      if (task.completed) {
        shifts.completed.push(task);
        return;
      }

      if (!task.time) {
        shifts.morning.push(task);
        return;
      }

      const hour = parseInt(task.time.split(':')[0]);
      if (hour >= 4 && hour < 12) {
        shifts.morning.push(task);
      } else if (hour >= 12 && hour < 18) {
        shifts.afternoon.push(task);
      } else {
        shifts.night.push(task);
      }
    });

    // Sort completed tasks by completion time (most recent first)
    shifts.completed.sort((a, b) => {
      if (!a.completedAt || !b.completedAt) return 0;
      return new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime();
    });

    // Sort other shifts by time
    [shifts.morning, shifts.afternoon, shifts.night].forEach(shift => {
      shift.sort((a, b) => {
        if (!a.time) return 1;
        if (!b.time) return -1;
        return a.time.localeCompare(b.time);
      });
    });

    return shifts;
  };

  const groupedTasks = groupTasksByShifts(tasksForSelectedDate);

  const handleComplete = (taskId: string) => {
    setTasks(tasks.map(task =>
      task.id === taskId ? { 
        ...task, 
        completed: !task.completed,
        completedAt: !task.completed ? new Date().toISOString() : undefined
      } : task
    ));
  };

  const handleEdit = (taskId: string, updates: Partial<Task>) => {
    setTasks(tasks.map(task =>
      task.id === taskId ? { ...task, ...updates } : task
    ));
  };

  const handleDelete = (taskId: string) => {
    if (confirm('Tem certeza que deseja excluir esta tarefa?')) {
      setTasks(tasks.filter(task => task.id !== taskId));
    }
  };

  const handleAddTask = (newTask: {
    title: string;
    description?: string;
    time?: string;
    category: string;
    type: 'appointment' | 'task';
  }) => {
    const task: Task = {
      id: Date.now().toString(),
      ...newTask,
      completed: false,
      date: selectedDate.toISOString().split('T')[0]
    };
    setTasks([task, ...tasks]);
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setIsCalendarExpanded(false);
  };

  const formatCompletionTime = (completedAt: string) => {
    const date = new Date(completedAt);
    return date.toLocaleTimeString('pt-BR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Shift Card Component
  const ShiftCard: React.FC<{
    title: string;
    timeRange: string;
    tasks: Task[];
    icon: React.ReactNode;
    iconColor: string;
    bgColor: string;
  }> = ({ title, timeRange, tasks, icon, iconColor, bgColor }) => {
    // Don't render if no tasks
    if (tasks.length === 0) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`${bgColor} rounded-2xl p-6 shadow-sm`}
      >
        {/* Shift Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className={`p-3 ${iconColor} rounded-xl`}>
            {icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-sm text-gray-600">{timeRange}</p>
            <p className="text-xs text-gray-500 mt-1">
              {tasks.length} {tasks.length === 1 ? 'tarefa' : 'tarefas'}
            </p>
          </div>
        </div>

        {/* Tasks List */}
        <div className="space-y-4">
          {tasks.map(task => (
            <TaskItem
              key={task.id}
              {...task}
              onComplete={handleComplete}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </div>
      </motion.div>
    );
  };

  // Completed Tasks Card Component
  const CompletedTasksCard: React.FC<{ tasks: Task[] }> = ({ tasks }) => {
    // Don't render if no completed tasks
    if (tasks.length === 0) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-[#B4EB00]/10 to-[#B4EB00]/5 rounded-2xl p-6 shadow-sm border border-[#B4EB00]/20"
      >
        {/* Completed Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-[#B4EB00] rounded-xl">
            <CheckCircle size={24} className="text-gray-900" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Tarefas Concluídas</h3>
            <p className="text-sm text-gray-600">Parabéns pelo progresso!</p>
            <p className="text-xs text-gray-500 mt-1">
              {tasks.length} {tasks.length === 1 ? 'tarefa concluída' : 'tarefas concluídas'}
            </p>
          </div>
        </div>

        {/* Completed Tasks List */}
        <div className="space-y-4">
          {tasks.map(task => (
            <motion.div
              key={task.id}
              initial={{ opacity: 0.6 }}
              animate={{ opacity: 1 }}
              className="bg-white/60 rounded-xl p-4 border border-[#B4EB00]/10"
            >
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#B4EB00] flex items-center justify-center flex-shrink-0 mt-0.5">
                  <CheckCircle size={16} className="text-gray-900" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 line-through">
                    {task.title}
                  </h4>
                  {task.description && (
                    <p className="text-sm text-gray-600 mt-1 line-through">
                      {task.description}
                    </p>
                  )}
                  <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                    {task.time && (
                      <div className="flex items-center gap-1">
                        <Clock size={12} />
                        <span>{task.time}</span>
                      </div>
                    )}
                    {task.completedAt && (
                      <span>
                        Concluída às {formatCompletionTime(task.completedAt)}
                      </span>
                    )}
                    <span className="px-2 py-1 bg-gray-200 rounded-full">
                      {task.category}
                    </span>
                  </div>
                </div>
                
                <button
                  onClick={() => handleComplete(task.id)}
                  className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                  title="Marcar como não concluída"
                >
                  <CheckCircle size={16} className="text-[#B4EB00]" />
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    );
  };

  return (
    <div className="relative min-h-screen bg-[#F7F7F7]">
      <Header onAddClick={() => setIsAddModalOpen(true)} />

      {/* Desktop Layout */}
      <div className="hidden md:flex max-w-7xl mx-auto pt-20 px-6 pb-6 gap-6">
        {/* Left Column */}
        <div className="w-1/2 space-y-6">
          {/* Progress Circle */}
          <div className="bg-white rounded-2xl p-6 shadow-sm">
            <div className="flex flex-col items-center">
              <ProgressCircle
                percentage={totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}
                size={160}
                color="#B4EB00"
                bgColor="#E5E7EB"
              />
              <p className="text-gray-600 mt-4">
                {totalTasks > 0 
                  ? `Você concluiu ${completedTasks} de ${totalTasks} hoje`
                  : 'Nenhum compromisso para esta data'
                }
              </p>
            </div>
          </div>

          {/* Calendar Expandable Section */}
          <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
            <AnimatePresence mode="wait">
              {isCalendarExpanded ? (
                <motion.div
                  key="expanded"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-4"
                >
                  {/* Calendar Header */}
                  <div className="flex items-center justify-between mb-4">
                    <button 
                      onClick={() => navigateMonth('prev')}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronLeft size={20} className="text-gray-600" />
                    </button>
                    
                    <h2 className="text-lg font-semibold text-gray-900">
                      {formatMonth(currentMonth)}
                    </h2>
                    
                    <button 
                      onClick={() => navigateMonth('next')}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronRight size={20} className="text-gray-600" />
                    </button>
                  </div>

                  {/* Calendar Grid */}
                  <div className="grid grid-cols-7 gap-1">
                    {weekDays.map((day) => (
                      <div
                        key={day}
                        className="text-center text-sm font-medium text-gray-600 py-2"
                      >
                        {day}
                      </div>
                    ))}
                    
                    {getDaysInMonth(currentMonth).map(({ date, isPadding }, index) => (
                      <button
                        key={index}
                        onClick={() => handleDateSelect(date)}
                        className={`
                          aspect-square p-1 relative rounded-lg transition-colors text-sm
                          ${isPadding ? 'text-gray-400' : 'text-gray-900'}
                          ${isSelected(date) ? 'bg-[#B4EB00] text-gray-900' : ''}
                          ${isToday(date) && !isSelected(date) ? 'bg-gray-100' : ''}
                          ${!isPadding ? 'hover:bg-gray-100' : ''}
                        `}
                      >
                        <span>{date.getDate()}</span>
                        {hasEvents(date) && !isPadding && (
                          <div className="absolute bottom-1 left-1/2 -translate-x-1/2 w-1 h-1 rounded-full bg-[#B4EB00]" />
                        )}
                      </button>
                    ))}
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="compact"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-4"
                >
                  <button
                    onClick={() => setIsCalendarExpanded(true)}
                    className="w-full flex items-center justify-center gap-2 py-3 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Calendar size={20} className="text-gray-600" />
                    <span className="font-medium text-gray-900">Calendário</span>
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-1/2 space-y-6">
          {/* Compact Horizontal Navigation */}
          <div className="flex items-center justify-between h-10">
            {/* Ver Semana - Left */}
            <button
              onClick={() => navigate('/tasks/week')}
              className="flex items-center gap-1.5 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all text-sm"
            >
              <Calendar size={14} className="text-gray-600" />
              <span className="text-sm">Ver Semana</span>
            </button>

            {/* Date Navigation - Center */}
            <div className="flex items-center gap-1">
              <button
                onClick={() => navigateDay('prev')}
                className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronLeft size={16} className="text-gray-600" />
              </button>
              
              <motion.h2 
                key={selectedDate.toDateString()}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="text-base font-medium text-gray-900 min-w-[70px] text-center px-2"
              >
                {formatDateDisplay(selectedDate)}
              </motion.h2>
              
              <button
                onClick={() => navigateDay('next')}
                className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronRight size={16} className="text-gray-600" />
              </button>
            </div>

            {/* Calendário - Right */}
            <button
              onClick={() => setIsCalendarExpanded(!isCalendarExpanded)}
              className="flex items-center gap-1.5 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all text-sm"
            >
              <Calendar size={14} className="text-gray-600" />
              <span className="text-sm">Calendário</span>
            </button>
          </div>

          {/* Tasks Content */}
          <motion.div 
            key={selectedDate.toDateString()}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {totalTasks > 0 ? (
              <>
                {/* Morning Shift Card */}
                <ShiftCard
                  title="MANHÃ"
                  timeRange="04:00h às 12:00h"
                  tasks={groupedTasks.morning}
                  icon={<Sun size={24} className="text-orange-600" />}
                  iconColor="bg-orange-100"
                  bgColor="bg-white"
                />

                {/* Afternoon Shift Card */}
                <ShiftCard
                  title="TARDE"
                  timeRange="12:00h às 18:00h"
                  tasks={groupedTasks.afternoon}
                  icon={<Sunset size={24} className="text-blue-600" />}
                  iconColor="bg-blue-100"
                  bgColor="bg-white"
                />

                {/* Night Shift Card */}
                <ShiftCard
                  title="NOITE"
                  timeRange="18:00h às 04:00h"
                  tasks={groupedTasks.night}
                  icon={<Moon size={24} className="text-purple-600" />}
                  iconColor="bg-purple-100"
                  bgColor="bg-white"
                />

                {/* Completed Tasks Card */}
                <CompletedTasksCard tasks={groupedTasks.completed} />
              </>
            ) : (
              <div className="bg-white rounded-2xl p-12 shadow-sm text-center">
                <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum compromisso
                </h3>
                <p className="text-gray-500 text-sm mb-4">
                  Você não tem compromissos agendados para esta data.
                </p>
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                >
                  <Plus size={16} />
                  Adicionar compromisso
                </button>
              </div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden max-w-lg mx-auto pt-20 px-4 pb-24">
        <div className="space-y-4">
          <TaskProgress completed={completedTasks} total={totalTasks} />
          
          {/* Compact Mobile Navigation */}
          <div className="flex items-center justify-between h-10 px-1">
            {/* Ver Semana - Left */}
            <button
              onClick={() => navigate('/tasks/week')}
              className="flex items-center gap-1 px-2.5 py-1.5 bg-white rounded-lg shadow-sm hover:shadow-md transition-all text-xs"
            >
              <Calendar size={12} className="text-gray-600" />
              <span>Ver Semana</span>
            </button>

            {/* Date Navigation - Center */}
            <div className="flex items-center gap-0.5">
              <button
                onClick={() => navigateDay('prev')}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronLeft size={14} className="text-gray-600" />
              </button>
              
              <motion.h2 
                key={selectedDate.toDateString()}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="text-sm font-medium text-gray-900 min-w-[60px] text-center px-1"
              >
                {formatDateDisplay(selectedDate)}
              </motion.h2>
              
              <button
                onClick={() => navigateDay('next')}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronRight size={14} className="text-gray-600" />
              </button>
            </div>

            {/* Calendário - Right */}
            <button
              onClick={() => setIsCalendarExpanded(!isCalendarExpanded)}
              className="flex items-center gap-1 px-2.5 py-1.5 bg-white rounded-lg shadow-sm hover:shadow-md transition-all text-xs"
            >
              <Calendar size={12} className="text-gray-600" />
              <span>Calendário</span>
            </button>
          </div>

          {/* Mobile Expandable Calendar */}
          <AnimatePresence>
            {isCalendarExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-2xl p-4 shadow-sm overflow-hidden"
              >
                {/* Calendar Header */}
                <div className="flex items-center justify-between mb-4">
                  <button 
                    onClick={() => navigateMonth('prev')}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <ChevronLeft size={20} className="text-gray-600" />
                  </button>
                  
                  <h2 className="text-lg font-semibold text-gray-900">
                    {formatMonth(currentMonth)}
                  </h2>
                  
                  <button 
                    onClick={() => navigateMonth('next')}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <ChevronRight size={20} className="text-gray-600" />
                  </button>
                </div>

                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1">
                  {weekDays.map((day) => (
                    <div
                      key={day}
                      className="text-center text-sm font-medium text-gray-600 py-2"
                    >
                      {day}
                    </div>
                  ))}
                  
                  {getDaysInMonth(currentMonth).map(({ date, isPadding }, index) => (
                    <button
                      key={index}
                      onClick={() => handleDateSelect(date)}
                      className={`
                        aspect-square p-1 relative rounded-lg transition-colors text-sm
                        ${isPadding ? 'text-gray-400' : 'text-gray-900'}
                        ${isSelected(date) ? 'bg-[#B4EB00] text-gray-900' : ''}
                        ${isToday(date) && !isSelected(date) ? 'bg-gray-100' : ''}
                        ${!isPadding ? 'hover:bg-gray-100' : ''}
                      `}
                    >
                      <span>{date.getDate()}</span>
                      {hasEvents(date) && !isPadding && (
                        <div className="absolute bottom-1 left-1/2 -translate-x-1/2 w-1 h-1 rounded-full bg-[#B4EB00]" />
                      )}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Mobile Tasks Content */}
          <motion.div 
            key={selectedDate.toDateString()}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {totalTasks > 0 ? (
              <>
                {/* Morning Shift Card */}
                <ShiftCard
                  title="MANHÃ"
                  timeRange="04:00h às 12:00h"
                  tasks={groupedTasks.morning}
                  icon={<Sun size={20} className="text-orange-600" />}
                  iconColor="bg-orange-100"
                  bgColor="bg-white"
                />

                {/* Afternoon Shift Card */}
                <ShiftCard
                  title="TARDE"
                  timeRange="12:00h às 18:00h"
                  tasks={groupedTasks.afternoon}
                  icon={<Sunset size={20} className="text-blue-600" />}
                  iconColor="bg-blue-100"
                  bgColor="bg-white"
                />

                {/* Night Shift Card */}
                <ShiftCard
                  title="NOITE"
                  timeRange="18:00h às 04:00h"
                  tasks={groupedTasks.night}
                  icon={<Moon size={20} className="text-purple-600" />}
                  iconColor="bg-purple-100"
                  bgColor="bg-white"
                />

                {/* Completed Tasks Card */}
                <CompletedTasksCard tasks={groupedTasks.completed} />
              </>
            ) : (
              <div className="bg-white rounded-2xl p-8 shadow-sm text-center">
                <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum compromisso
                </h3>
                <p className="text-gray-500 text-sm mb-4">
                  Você não tem compromissos agendados para esta data.
                </p>
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                >
                  <Plus size={16} />
                  Adicionar compromisso
                </button>
              </div>
            )}
          </motion.div>
        </div>
      </div>

      <AddTaskModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddTask}
      />
    </div>
  );
};

export default TasksPage;