import React, { useState, useEffect } from 'react';
import { ArrowLeft, Calendar, Clock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { format, addDays, isSameDay, isToday as checkIsToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { motion } from 'framer-motion';
import { CATEGORIES } from '@/lib/constants';

interface Task {
  id: string;
  title: string;
  description?: string;
  time?: string;
  completed: boolean;
  category: string;
  type: 'appointment' | 'task';
  date: string;
}

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Reunião com cliente',
    description: 'Discutir próximos passos do projeto',
    time: '09:00',
    completed: false,
    category: 'Reunião',
    type: 'appointment',
    date: new Date().toISOString().split('T')[0]
  },
  {
    id: '2',
    title: 'Almoço com equipe',
    description: 'Restaurante italiano na Rua Augusta',
    time: '12:30',
    completed: false,
    category: 'Compromisso',
    type: 'appointment',
    date: new Date().toISOString().split('T')[0]
  },
  {
    id: '3',
    title: 'Preparar apresentação',
    description: 'Slides para reunião de amanhã',
    time: '14:00',
    completed: true,
    category: 'Tarefa',
    type: 'task',
    date: new Date().toISOString().split('T')[0]
  },
  {
    id: '4',
    title: 'Revisar documentos',
    description: 'Contratos pendentes de análise',
    time: '10:00',
    completed: false,
    category: 'Responsabilidade',
    type: 'task',
    date: addDays(new Date(), 1).toISOString().split('T')[0]
  },
  {
    id: '5',
    title: 'Chamada com fornecedor',
    description: 'Negociar novos preços',
    time: '15:30',
    completed: false,
    category: 'Chamada',
    type: 'appointment',
    date: addDays(new Date(), 2).toISOString().split('T')[0]
  },
  {
    id: '6',
    title: 'Exercícios físicos',
    description: 'Academia - treino de pernas',
    time: '07:00',
    completed: false,
    category: 'Compromisso',
    type: 'appointment',
    date: addDays(new Date(), 3).toISOString().split('T')[0]
  },
  {
    id: '7',
    title: 'Dentista',
    description: 'Consulta de rotina',
    time: '16:00',
    completed: false,
    category: 'Compromisso',
    type: 'appointment',
    date: addDays(new Date(), 4).toISOString().split('T')[0]
  },
  {
    id: '8',
    title: 'Reunião de equipe',
    description: 'Planejamento semanal',
    time: '09:30',
    completed: false,
    category: 'Reunião',
    type: 'appointment',
    date: addDays(new Date(), 5).toISOString().split('T')[0]
  }
];

type FilterType = 'all' | 'appointments' | 'tasks' | 'meetings' | 'responsibilities';

const WeekView: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [tasks, setTasks] = useState(mockTasks);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute for real-time updates
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Generate 7 consecutive days starting from today
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(new Date(), i));

  const getTasksForDay = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return tasks.filter(task => {
      const matchesFilter = selectedFilter === 'all' ||
        (selectedFilter === 'appointments' && task.type === 'appointment') ||
        (selectedFilter === 'tasks' && task.type === 'task') ||
        (selectedFilter === 'meetings' && task.category === 'Reunião') ||
        (selectedFilter === 'responsibilities' && task.category === 'Responsabilidade');

      return matchesFilter && task.date === dateString;
    }).sort((a, b) => {
      // Sort by completion status first (incomplete first), then by time
      if (a.completed === b.completed) {
        if (!a.time) return 1;
        if (!b.time) return -1;
        return a.time.localeCompare(b.time);
      }
      return a.completed ? 1 : -1;
    });
  };

  const getCategoryColor = (category: string) => {
    const cat = CATEGORIES.tasks.find(c => c.name === category);
    return cat?.color || '#B4EB00';
  };

  const filters: { type: FilterType; label: string; count: number }[] = [
    { type: 'all', label: 'Todos', count: tasks.length },
    { type: 'appointments', label: 'Compromissos', count: tasks.filter(t => t.type === 'appointment').length },
    { type: 'tasks', label: 'Tarefas', count: tasks.filter(t => t.type === 'task').length },
    { type: 'meetings', label: 'Reuniões', count: tasks.filter(t => t.category === 'Reunião').length },
    { type: 'responsibilities', label: 'Responsabilidades', count: tasks.filter(t => t.category === 'Responsabilidade').length }
  ];

  const isToday = (date: Date) => {
    return checkIsToday(date);
  };

  const formatDayHeader = (date: Date) => {
    if (isToday(date)) {
      return {
        dayName: 'Hoje',
        dateString: format(date, "dd 'de' MMMM", { locale: ptBR }),
        isToday: true
      };
    }
    
    return {
      dayName: format(date, 'EEEE', { locale: ptBR }),
      dateString: format(date, "dd 'de' MMMM", { locale: ptBR }),
      isToday: false
    };
  };

  const toggleTaskComplete = (taskId: string) => {
    setTasks(tasks.map(task =>
      task.id === taskId ? { ...task, completed: !task.completed } : task
    ));
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-10 border-b border-gray-100">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <button 
              onClick={() => navigate('/tasks')}
              className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </button>
            <h1 className="text-xl font-bold">Visão Semanal</h1>
            <div className="w-10" />
          </div>

          {/* Date Range Display */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <Calendar size={18} className="text-gray-600" />
            <span className="font-medium text-gray-900">
              {format(weekDays[0], "dd 'de' MMMM", { locale: ptBR })} até{' '}
              {format(weekDays[6], "dd 'de' MMMM", { locale: ptBR })}
            </span>
          </div>

          {/* Filters */}
          <div className="overflow-x-auto pb-2">
            <div className="flex gap-2 min-w-max">
              {filters.map(filter => (
                <button
                  key={filter.type}
                  onClick={() => setSelectedFilter(filter.type)}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors text-sm ${
                    selectedFilter === filter.type
                      ? 'bg-[#B4EB00] text-gray-900 font-medium'
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="max-w-4xl mx-auto px-4 pt-44 pb-6">
        <div className="space-y-4">
          {weekDays.map((date, index) => {
            const dayTasks = getTasksForDay(date);
            const completedTasks = dayTasks.filter(task => task.completed).length;
            const totalTasks = dayTasks.length;
            const dayInfo = formatDayHeader(date);
            
            return (
              <motion.div
                key={format(date, 'yyyy-MM-dd')}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`bg-white rounded-2xl shadow-sm overflow-hidden ${
                  dayInfo.isToday ? 'ring-2 ring-[#B4EB00] ring-opacity-30' : ''
                }`}
              >
                {/* Day Header */}
                <div className={`p-4 border-b border-gray-100 ${
                  dayInfo.isToday ? 'bg-gradient-to-r from-[#B4EB00]/5 to-[#B4EB00]/10' : 'bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className={`text-lg font-semibold ${
                        dayInfo.isToday ? 'text-[#B4EB00]' : 'text-gray-900'
                      }`}>
                        {dayInfo.dayName}
                        {dayInfo.isToday && (
                          <span className="ml-2 px-2 py-1 bg-[#B4EB00] text-gray-900 text-xs rounded-full font-medium">
                            {format(currentTime, 'HH:mm')}
                          </span>
                        )}
                      </h2>
                      <p className="text-sm text-gray-600 mt-1">
                        {dayInfo.dateString}
                      </p>
                    </div>
                    {totalTasks > 0 && (
                      <div className="text-right">
                        <span className={`text-sm font-medium ${
                          dayInfo.isToday ? 'text-[#B4EB00]' : 'text-gray-600'
                        }`}>
                          {completedTasks}/{totalTasks}
                        </span>
                        <p className="text-xs text-gray-500">concluídas</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Tasks Content */}
                <div className="p-4">
                  {dayTasks.length > 0 ? (
                    <div className="space-y-3">
                      {dayTasks.map((task, taskIndex) => (
                        <motion.div
                          key={task.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: (index * 0.05) + (taskIndex * 0.02) }}
                          className={`flex items-start gap-3 p-3 rounded-xl transition-all ${
                            task.completed 
                              ? 'bg-gray-50 opacity-60' 
                              : 'hover:bg-gray-50 cursor-pointer'
                          }`}
                          onClick={() => toggleTaskComplete(task.id)}
                        >
                          {/* Completion Checkbox */}
                          <button
                            className={`w-5 h-5 rounded-full border-2 flex-shrink-0 flex items-center justify-center transition-colors mt-0.5 ${
                              task.completed
                                ? 'bg-[#B4EB00] border-[#B4EB00]'
                                : 'border-gray-300 hover:border-[#B4EB00]'
                            }`}
                            style={{
                              borderColor: !task.completed ? getCategoryColor(task.category) : undefined,
                              opacity: !task.completed ? 0.5 : 1
                            }}
                          >
                            {task.completed && (
                              <svg width="12\" height="12\" viewBox="0 0 12 12\" fill="none">
                                <path
                                  d="M10 3L4.5 8.5L2 6"
                                  stroke="white"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            )}
                          </button>

                          {/* Category Indicator */}
                          <div
                            className="w-1 h-12 rounded-full flex-shrink-0"
                            style={{ backgroundColor: getCategoryColor(task.category) }}
                          />

                          {/* Task Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1 min-w-0">
                                <h3 className={`font-medium text-sm leading-tight ${
                                  task.completed ? 'line-through text-gray-400' : 'text-gray-900'
                                }`}>
                                  {task.title}
                                </h3>
                                {task.description && (
                                  <p className={`text-xs mt-1 leading-relaxed ${
                                    task.completed ? 'text-gray-400' : 'text-gray-600'
                                  }`}>
                                    {task.description}
                                  </p>
                                )}
                                <div className="flex items-center gap-3 mt-2">
                                  {task.time && (
                                    <div className="flex items-center gap-1">
                                      <Clock size={12} className={task.completed ? 'text-gray-400' : 'text-gray-500'} />
                                      <span className={`text-xs ${
                                        task.completed ? 'text-gray-400' : 'text-gray-500'
                                      }`}>
                                        {task.time}
                                      </span>
                                    </div>
                                  )}
                                  <span className={`text-xs px-2 py-1 rounded-full ${
                                    task.completed 
                                      ? 'bg-gray-200 text-gray-400'
                                      : 'text-gray-600'
                                  }`}
                                  style={{
                                    backgroundColor: !task.completed ? `${getCategoryColor(task.category)}20` : undefined,
                                    color: !task.completed ? getCategoryColor(task.category) : undefined
                                  }}>
                                    {task.category}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Calendar size={32} className="mx-auto mb-3 text-gray-300" />
                      <p className="text-gray-500 text-sm">Nenhum compromisso agendado</p>
                      <p className="text-gray-400 text-xs mt-1">
                        {dayInfo.isToday ? 'Aproveite seu dia livre!' : 'Dia livre para relaxar'}
                      </p>
                    </div>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Summary Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8 bg-white rounded-2xl p-6 shadow-sm"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo da Semana</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-[#B4EB00]">
                {tasks.filter(t => t.completed).length}
              </p>
              <p className="text-sm text-gray-600">Concluídas</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => !t.completed).length}
              </p>
              <p className="text-sm text-gray-600">Pendentes</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-[#007AFF]">
                {tasks.filter(t => t.type === 'appointment').length}
              </p>
              <p className="text-sm text-gray-600">Compromissos</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-[#FF9500]">
                {tasks.filter(t => t.type === 'task').length}
              </p>
              <p className="text-sm text-gray-600">Tarefas</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default WeekView;