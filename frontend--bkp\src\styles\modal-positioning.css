/* 
 * Modal Positioning System - Comprehensive CSS
 * Ensures perfect centering and responsiveness across all devices
 */

/* Base modal overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

/* Base modal container */
.modal-container {
  width: 100%;
  max-width: 28rem; /* 448px */
  max-height: 90vh;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  /* Ensure proper positioning */
  position: relative;
  z-index: 1001;
}

/* Modal content structure */
.modal-header {
  flex-shrink: 0;
  padding: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 1.5rem;
}

.modal-footer {
  flex-shrink: 0;
  padding: 1.5rem;
  border-top: 1px solid #f3f4f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 16px;
  }
  
  .modal-container {
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 32px);
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 1rem;
  }
}

@media (max-width: 320px) {
  .modal-overlay {
    padding: 12px;
  }
  
  .modal-container {
    max-width: calc(100vw - 24px);
    border-radius: 0.75rem;
  }
}

/* Desktop optimizations */
@media (min-width: 769px) {
  .modal-container {
    max-width: 32rem; /* 512px */
  }
}

@media (min-width: 1440px) {
  .modal-container {
    max-width: 36rem; /* 576px */
  }
}

/* Animation support */
.modal-enter {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-exit {
  opacity: 1;
  transform: scale(1);
}

.modal-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accessibility improvements */
.modal-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Performance optimizations */
.modal-container {
  will-change: transform, opacity;
  contain: layout style paint;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal-enter-active,
  .modal-exit-active {
    transition: opacity 150ms ease-in-out;
  }
  
  .modal-enter,
  .modal-exit-active {
    transform: scale(1);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modal-container {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #374151;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .modal-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .modal-container {
    border: 2px solid #000;
  }
}

/* Focus trap utilities */
.modal-focus-trap {
  position: absolute;
  top: -1px;
  left: -1px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}