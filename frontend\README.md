# 🚀 Dupli Dashboard

Um dashboard moderno e responsivo para gerenciamento pessoal, construído com React, TypeScript e Tailwind CSS.

## 📋 Índice

- [Visão Geral](#visão-geral)
- [Tecnologias](#tecnologias)
- [Instalação](#instalação)
- [Estrutura do Projeto](#estrutura-do-projeto)
- [Sistema de Pop-ups](#sistema-de-pop-ups)
- [Componentes](#componentes)
- [Desenvolvimento](#desenvolvimento)
- [Deploy](#deploy)

## 🎯 Visão Geral

O Dupli Dashboard é uma aplicação web completa para organização pessoal, oferecendo:

- **Gerenciamento de Tarefas**: Organize compromissos e tarefas por turnos
- **Controle Financeiro**: Acompanhe receitas, despesas e economias
- **Banco de Ideias**: Capture e desenvolva suas ideias
- **Visão Anual**: Análise completa do progresso financeiro
- **Interface Responsiva**: Otimizada para desktop e mobile

## 🛠️ Tecnologias

### Core
- **React 18.2.0** - Biblioteca principal
- **TypeScript 5.2.2** - Tipagem estática
- **Vite 5.1.4** - Build tool e dev server
- **Tailwind CSS 3.4.1** - Framework CSS

### UI/UX
- **Framer Motion 11.0.8** - Animações
- **Lucide React 0.344.0** - Ícones
- **Recharts 2.12.2** - Gráficos e visualizações

### Backend/Database
- **Supabase 2.39.7** - Backend as a Service
- **Zustand 4.5.2** - Gerenciamento de estado

### Utilitários
- **date-fns 3.3.1** - Manipulação de datas
- **clsx 2.1.0** - Conditional classes
- **tailwind-merge 2.2.1** - Merge de classes Tailwind

## 🚀 Instalação

### Pré-requisitos
- Node.js 18+ 
- npm ou yarn

### Passos

1. **Clone o repositório**
```bash
git clone <repository-url>
cd dupli-dashboard
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas credenciais do Supabase:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **Execute o projeto**
```bash
npm run dev
```

## 📁 Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── AddTaskModal.tsx
│   ├── Navigation.tsx
│   ├── Header.tsx
│   └── ...
├── pages/              # Páginas da aplicação
│   ├── Dashboard.tsx
│   ├── TasksPage.tsx
│   ├── FinancesPage.tsx
│   └── ...
├── lib/                # Utilitários e configurações
│   ├── constants.ts
│   ├── utils.ts
│   ├── supabase.ts
│   └── theme.ts
├── stores/             # Gerenciamento de estado
│   ├── useTaskStore.ts
│   └── useTransactionStore.ts
├── hooks/              # Hooks customizados
│   └── useAuth.ts
├── styles/             # Estilos globais
│   ├── popup-positioning.css
│   └── index.css
└── types/              # Definições de tipos
    └── supabase.ts
```

## 🎨 Sistema de Pop-ups

### Especificações Técnicas Implementadas

O sistema de pop-ups foi completamente reformulado seguindo as melhores práticas modernas:

#### ✅ Posicionamento Centralizado
- **Centralização perfeita**: `position: fixed` + `top: 50%` + `left: 50%` + `transform: translate(-50%, -50%)`
- **Z-index hierárquico**: 1000 para overlay, 1001 para modal
- **Margem mínima**: 16px de todas as bordas da tela

#### ✅ Responsividade Avançada
```css
/* Mobile (até 768px) */
width: calc(95vw)
max-width: calc(95vw)
margin: 16px

/* Desktop (769px+) */
width: calc(80vw)
max-width: 32rem
```

#### ✅ Scroll Otimizado
- **Overflow vertical**: `overflow-y: auto`
- **Touch scrolling**: `-webkit-overflow-scrolling: touch`
- **Altura máxima**: `max-height: 90vh`
- **Flexbox layout**: `display: flex` + `flex-direction: column`

#### ✅ Componentes Atualizados

**Modais Corrigidos:**
- ✅ `AddTaskModal.tsx` - Modal de adicionar tarefas
- ✅ `AddTransactionModal.tsx` - Modal de transações
- ✅ `EditBudgetModal.tsx` - Modal de edição de orçamento
- ✅ `DateSelector.tsx` - Seletor de datas
- ✅ `MonthYearSelector.tsx` - Seletor mês/ano
- ✅ `MonthSelector.tsx` - Seletor de mês com visão anual

### Testes de Compatibilidade

#### ✅ Resoluções Testadas
- **320px** - iPhone SE, dispositivos pequenos
- **768px** - iPad, tablets
- **1024px** - Desktop padrão
- **1440px** - Desktop grande

#### ✅ Navegadores Suportados
- **iOS Safari** - Scroll touch otimizado
- **Chrome Android** - Performance nativa
- **Desktop Chrome/Firefox** - Funcionalidade completa

#### ✅ Funcionalidades Validadas
- **Conteúdo extenso** - Scroll interno funcional
- **Interações touch** - Gestos nativos preservados
- **Teclado virtual** - Ajuste automático no mobile
- **Orientação** - Suporte portrait/landscape

### Melhorias Implementadas

#### 🎯 Performance
- **Will-change** para otimização de animações
- **Contain** para isolamento de layout
- **Reduced motion** para acessibilidade

#### 🎯 Acessibilidade
- **Focus management** automático
- **Escape key** para fechar modais
- **ARIA labels** apropriados
- **Contrast ratios** adequados

#### 🎯 UX/UI
- **Animações suaves** com Framer Motion
- **Backdrop blur** para contexto visual
- **Loading states** durante transições
- **Error boundaries** para robustez

## 🧩 Componentes Principais

### Navigation
Sistema de navegação responsivo com:
- Menu desktop horizontal
- Menu mobile em floating bottom bar
- Indicadores de página ativa
- Transições suaves

### TasksPage
Gerenciamento de tarefas com:
- Organização por turnos (Manhã/Tarde/Noite)
- Cards visuais para cada turno
- Tarefas concluídas em seção separada
- Calendário integrado
- Visão semanal

### FinancesPage
Controle financeiro com:
- Quadrante financeiro (Receitas/Despesas/Economias/Disponível)
- Card de visão anual simplificado
- Gráficos interativos
- Distribuição por categorias

### IdeasPage
Banco de ideias com:
- Interface tipo notepad
- Sistema de favoritos
- Categorização flexível
- Editor de texto rico
- Auto-save

## 🔧 Desenvolvimento

### Scripts Disponíveis

```bash
# Desenvolvimento
npm run dev

# Build para produção
npm run build

# Preview do build
npm run preview

# Linting
npm run lint

# Type checking
npm run typecheck

# Testes
npm run test
```

### Padrões de Código

#### TypeScript
- Tipagem estrita habilitada
- Interfaces explícitas para props
- Tipos utilitários do React

#### Tailwind CSS
- Classes utilitárias preferenciais
- Componentes customizados em `@layer components`
- Variáveis CSS para temas

#### Estrutura de Componentes
```typescript
interface ComponentProps {
  // Props tipadas
}

const Component: React.FC<ComponentProps> = ({ props }) => {
  // Hooks no topo
  // Handlers
  // Render
};

export default Component;
```

## 🚀 Deploy

### Vercel (Recomendado)

1. **Conecte o repositório**
```bash
vercel --prod
```

2. **Configure as variáveis de ambiente**
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`

3. **Build automático**
O Vercel detecta automaticamente Vite e configura o build.

### Netlify

1. **Build settings**
```
Build command: npm run build
Publish directory: dist
```

2. **Environment variables**
Adicione as mesmas variáveis do Supabase.

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

## 📊 Performance

### Métricas Alvo
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3.0s
- **Cumulative Layout Shift**: < 0.1

### Otimizações Implementadas
- **Code splitting** por rotas
- **Lazy loading** de componentes pesados
- **Image optimization** com loading lazy
- **Bundle analysis** com Vite

## 🔒 Segurança

### Implementações
- **Row Level Security** no Supabase
- **Environment variables** para credenciais
- **Input validation** em formulários
- **XSS protection** com sanitização

### Próximos Passos
- [ ] Implementar autenticação completa
- [ ] Adicionar rate limiting
- [ ] Configurar CSP headers
- [ ] Audit de dependências regular

## 🐛 Troubleshooting

### Problemas Comuns

**Pop-ups não centralizam no mobile**
```css
/* Verificar se está usando as classes corretas */
.popup-base {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
```

**Scroll não funciona em iOS**
```css
/* Adicionar webkit-overflow-scrolling */
.popup-content {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
```

**Build falha no Vercel**
```bash
# Verificar versões do Node.js
node --version  # Deve ser 18+
npm --version   # Deve ser 8+
```

## 📝 Changelog

### v1.2.0 - Sistema de Pop-ups Reformulado
- ✅ Posicionamento centralizado em todas as resoluções
- ✅ Responsividade otimizada para mobile/desktop
- ✅ Scroll interno funcional com touch support
- ✅ Animações suaves com Framer Motion
- ✅ Compatibilidade iOS Safari/Chrome Android
- ✅ Acessibilidade melhorada

### v1.1.0 - Melhorias de UX
- ✅ Cards de turnos na página de tarefas
- ✅ Visão anual simplificada
- ✅ Editor de ideias aprimorado
- ✅ Navegação responsiva

### v1.0.0 - Release Inicial
- ✅ Dashboard funcional
- ✅ Gerenciamento de tarefas
- ✅ Controle financeiro
- ✅ Banco de ideias
- ✅ Integração Supabase

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para suporte e dúvidas:
- Abra uma issue no GitHub
- Consulte a documentação
- Verifique os troubleshooting guides

---

**Desenvolvido com ❤️ usando React + TypeScript + Tailwind CSS**