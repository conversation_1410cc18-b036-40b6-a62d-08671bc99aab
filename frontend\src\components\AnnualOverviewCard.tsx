import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON>A<PERSON><PERSON>, Tooltip, ResponsiveContainer } from 'recharts';
import { ArrowUpRight, TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';
import { useAnnualFinancialSummary } from '../hooks/useFinances';

interface AnnualOverviewProps {
  // Removido props data para usar dados reais
}

const AnnualOverviewCard: React.FC<AnnualOverviewProps> = () => {
  const navigate = useNavigate();
  const currentYear = new Date().getFullYear();
  const { data: annualData, isLoading, error } = useAnnualFinancialSummary(currentYear);

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm h-[280px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600 text-sm">Carregando dados anuais...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm h-[280px] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
          <p className="text-red-600 text-sm">Erro ao carregar dados anuais</p>
        </div>
      </div>
    );
  }

  // No data state
  if (!annualData || !annualData.monthlyData) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm h-[280px] flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 text-sm">Nenhum dado anual encontrado</p>
        </div>
      </div>
    );
  }

  const data = annualData.monthlyData;
  
  // Calcular tendência (comparar últimos 3 meses com 3 anteriores)
  const recentMonths = data.slice(-3);
  const previousMonths = data.slice(-6, -3);
  const recentAvg = recentMonths.reduce((sum, m) => sum + m.savings, 0) / 3;
  const previousAvg = previousMonths.reduce((sum, m) => sum + m.savings, 0) / 3;
  const trend = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;

  return (
    <div 
      onClick={() => navigate('/finances/annual')}
      className="bg-white rounded-2xl p-6 shadow-sm cursor-pointer hover:shadow-md transition-all group h-[280px] flex flex-col"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-1">Visão Anual</h2>
          <p className="text-sm text-gray-600">
            12 meses • {annualData.year}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* Indicador de progresso anual */}
          <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
            trend >= 0 
              ? 'bg-green-50 text-green-700' 
              : 'bg-red-50 text-red-700'
          }`}>
            {trend >= 0 ? (
              <TrendingUp size={12} />
            ) : (
              <TrendingDown size={12} />
            )}
            <span>{trend >= 0 ? '+' : ''}{trend.toFixed(1)}%</span>
          </div>
          {/* Ícone de expansão */}
          <ArrowUpRight size={20} className="text-gray-400 group-hover:text-gray-600 transition-colors" />
        </div>
      </div>

      {/* Gráfico */}
      <div className="flex-1 mb-4">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
            <XAxis 
              dataKey="month" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6C6C6C' }}
            />
            <YAxis hide />
            <Tooltip
              contentStyle={{
                backgroundColor: '#fff',
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0,0,0,0.1)',
                padding: '8px'
              }}
              formatter={(value: number, name: string) => [
                new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(value),
                name === 'income' ? 'Receitas' : 
                name === 'expenses' ? 'Despesas' : 'Economias'
              ]}
            />
            {/* Linha verde escura para Receitas */}
            <Line 
              type="monotone" 
              dataKey="income" 
              stroke="#22C55E" 
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 3, stroke: '#22C55E', strokeWidth: 2 }}
            />
            {/* Linha vermelha para Despesas */}
            <Line 
              type="monotone" 
              dataKey="expenses" 
              stroke="#EF4444" 
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 3, stroke: '#EF4444', strokeWidth: 2 }}
            />
            {/* Linha verde neon para Economias */}
            <Line 
              type="monotone" 
              dataKey="savings" 
              stroke="#B4EB00" 
              strokeWidth={3}
              dot={false}
              activeDot={{ r: 4, stroke: '#B4EB00', strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Legenda */}
      <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 rounded-full bg-[#22C55E]" />
          <span>Receitas</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 rounded-full bg-[#EF4444]" />
          <span>Despesas</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 rounded-full bg-[#B4EB00]" />
          <span>Economias</span>
        </div>
      </div>
    </div>
  );
};

export default AnnualOverviewCard;