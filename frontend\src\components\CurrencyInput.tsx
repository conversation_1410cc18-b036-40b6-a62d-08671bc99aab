import React, { useState, useEffect } from 'react';
import { applyCurrencyMask, parseCurrency, toDecimalString } from '../utils/currency';

interface CurrencyInputProps {
  value?: number;
  onChange: (value: number, decimalString: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  autoFocus?: boolean;
}

const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value = 0,
  onChange,
  placeholder = "0,00",
  className = "",
  disabled = false,
  autoFocus = false
}) => {
  const [displayValue, setDisplayValue] = useState('');

  // Sincronizar com valor externo
  useEffect(() => {
    if (value > 0) {
      setDisplayValue(new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value));
    } else {
      setDisplayValue('');
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const formatted = applyCurrencyMask(inputValue);
    const numeric = parseCurrency(formatted);
    
    setDisplayValue(formatted);
    onChange(numeric, toDecimalString(numeric));
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // Selecionar todo o texto ao focar
    e.target.select();
  };

  return (
    <div className="relative">
      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 text-sm">
        R$
      </span>
      <input
        type="text"
        value={displayValue}
        onChange={handleChange}
        onFocus={handleFocus}
        placeholder={placeholder}
        className={`pl-10 ${className}`}
        disabled={disabled}
        autoFocus={autoFocus}
        inputMode="numeric"
      />
    </div>
  );
};

export default CurrencyInput;
