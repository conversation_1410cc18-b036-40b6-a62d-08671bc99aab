import React, { useState } from 'react';
import { devAuth } from '../lib/dev-auth';
import { tokenUtils } from '../lib/api';

const DevTools: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Só mostrar em desenvolvimento
  if (import.meta.env.PROD || !import.meta.env.VITE_DEV_MODE) {
    return null;
  }

  const handleSetupAuth = async () => {
    setLoading(true);
    setMessage('');
    try {
      await devAuth.setupDevAuth();
      setMessage('Autenticação de desenvolvimento configurada com sucesso!');
      // Recarregar a página para aplicar as mudanças
      setTimeout(() => window.location.reload(), 1000);
    } catch (error: any) {
      setMessage(`Erro: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleClearAuth = () => {
    devAuth.clearDevAuth();
    setMessage('Dados de desenvolvimento limpos!');
    setTimeout(() => window.location.reload(), 1000);
  };

  const handleGenerateToken = async () => {
    setLoading(true);
    setMessage('');
    try {
      const result = await devAuth.generateDevToken();
      setMessage(`Token gerado: ${result.message}`);
    } catch (error: any) {
      setMessage(`Erro: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setLoading(true);
    setMessage('');
    try {
      // Testar conexão básica com o backend
      const response = await fetch('http://localhost:3000/finances/test');
      if (response.ok) {
        const data = await response.json();
        setMessage(`Conexão OK: ${data.message}`);
      } else {
        setMessage(`Erro de conexão: ${response.status} ${response.statusText}`);
      }
    } catch (error: any) {
      setMessage(`Erro de rede: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestCategories = async () => {
    setLoading(true);
    setMessage('');
    try {
      // Testar endpoint de categorias com headers de desenvolvimento
      const response = await fetch('http://localhost:3000/finances/categories', {
        headers: {
          'X-Dev-Mode': 'true',
          'X-Dev-User-Id': '1',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMessage(`Categorias OK: ${data.length} categorias encontradas`);
      } else {
        const errorText = await response.text();
        setMessage(`Erro categorias: ${response.status} - ${errorText}`);
      }
    } catch (error: any) {
      setMessage(`Erro de rede: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const isAuthenticated = tokenUtils.isAuthenticated();

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isOpen ? (
        <button
          onClick={() => setIsOpen(true)}
          className="bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors text-sm"
        >
          🛠️ Dev Tools
        </button>
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-gray-900">Ferramentas de Desenvolvimento</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            <div className="text-sm">
              <p className="text-gray-600">
                Status: {isAuthenticated ? (
                  <span className="text-green-600 font-medium">Autenticado</span>
                ) : (
                  <span className="text-red-600 font-medium">Não autenticado</span>
                )}
              </p>
            </div>

            <div className="space-y-2">
              <button
                onClick={handleTestConnection}
                disabled={loading}
                className="w-full px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 text-sm"
              >
                {loading ? 'Testando...' : 'Testar Conexão'}
              </button>

              <button
                onClick={handleTestCategories}
                disabled={loading}
                className="w-full px-3 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50 text-sm"
              >
                {loading ? 'Testando...' : 'Testar Categorias'}
              </button>

              <button
                onClick={handleSetupAuth}
                disabled={loading}
                className="w-full px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 text-sm"
              >
                {loading ? 'Configurando...' : 'Configurar Auth + Dados'}
              </button>

              <button
                onClick={handleGenerateToken}
                disabled={loading}
                className="w-full px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 text-sm"
              >
                {loading ? 'Gerando...' : 'Gerar Token'}
              </button>

              <button
                onClick={handleClearAuth}
                className="w-full px-3 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
              >
                Limpar Dados
              </button>
            </div>

            {message && (
              <div className={`text-xs p-2 rounded ${
                message.includes('Erro') 
                  ? 'bg-red-100 text-red-700' 
                  : 'bg-green-100 text-green-700'
              }`}>
                {message}
              </div>
            )}

            <div className="text-xs text-gray-500 border-t pt-2">
              <p>Disponível apenas em desenvolvimento</p>
              <p>Use o console: <code>devAuth.setupDevAuth()</code></p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DevTools;
