import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Share2, MoreHorizontal } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface IdeaNotepadProps {
  id: string;
  title: string;
  content: string;
  onClose: () => void;
  onSave: (id: string, updates: { title: string; content: string }) => void;
}

const IdeaNotepad: React.FC<IdeaNotepadProps> = ({
  id,
  title: initialTitle,
  content: initialContent,
  onClose,
  onSave,
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [showCharLimit, setShowCharLimit] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  
  const titleRef = useRef<HTMLTextAreaElement>(null);
  const contentRef = useRef<HTMLTextAreaElement>(null);
  const maxChars = 2000;

  // Auto-resize textarea
  const autoResize = (element: HTMLTextAreaElement) => {
    element.style.height = 'auto';
    element.style.height = element.scrollHeight + 'px';
  };

  // Auto-save functionality
  useEffect(() => {
    const autosaveInterval = setInterval(() => {
      if (title !== initialTitle || content !== initialContent) {
        setIsAutoSaving(true);
        handleSave();
        setTimeout(() => {
          setIsAutoSaving(false);
          setLastSaved(new Date());
        }, 500);
      }
    }, 3000); // Auto-save every 3 seconds

    return () => clearInterval(autosaveInterval);
  }, [title, content, initialTitle, initialContent]);

  // Focus on title when component mounts
  useEffect(() => {
    if (titleRef.current) {
      titleRef.current.focus();
      // Place cursor at end of title
      const length = title.length;
      titleRef.current.setSelectionRange(length, length);
    }
  }, []);

  const handleSave = () => {
    onSave(id, { title: title || 'Sem título', content });
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    if (newContent.length <= maxChars) {
      setContent(newContent);
      setShowCharLimit(false);
      autoResize(e.target);
    } else {
      setShowCharLimit(true);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTitle(e.target.value);
    autoResize(e.target);
  };

  const handleShare = async () => {
    const shareData = {
      title: title || 'Ideia',
      text: content,
    };

    if (navigator.share && navigator.canShare(shareData)) {
      try {
        await navigator.share(shareData);
      } catch (err) {
        // Compartilhamento cancelado
      }
    } else {
      // Fallback: copy to clipboard
      const textToCopy = `${title}\n\n${content}`;
      try {
        await navigator.clipboard.writeText(textToCopy);
        // You could show a toast notification here
        // Copiado para a área de transferência
      } catch (err) {
        console.error('Erro ao copiar:', err);
      }
    }
  };

  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Agora mesmo';
    if (diffInMinutes < 60) return `${diffInMinutes}min atrás`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    
    return date.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="fixed inset-0 bg-[#F7F7F7] z-50 flex flex-col"
    >
      {/* Header - Title text removed, layout preserved */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.1 }}
        className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-50 border-b border-gray-100"
      >
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Botão Voltar - Preserved */}
            <motion.button
              onClick={onClose}
              className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </motion.button>
            
            {/* Center space - Title removed but spacing preserved */}
            <div className="flex-1" />
            
            {/* Botões de ação - Preserved */}
            <div className="flex items-center gap-2">
              {/* Indicador de auto-save */}
              <AnimatePresence>
                {isAutoSaving && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="flex items-center gap-2 text-xs text-gray-500 mr-2"
                  >
                    <div className="w-2 h-2 bg-[#B4EB00] rounded-full animate-pulse" />
                    Salvando...
                  </motion.div>
                )}
                {lastSaved && !isAutoSaving && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-xs text-gray-500 mr-2 hidden md:block"
                  >
                    {formatLastSaved(lastSaved)}
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Botão Compartilhar */}
              <motion.button
                onClick={handleShare}
                className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
                whileTap={{ scale: 0.95 }}
              >
                <Share2 size={20} className="text-gray-600" />
              </motion.button>
              
              {/* Botão Concluído */}
              <motion.button
                onClick={() => {
                  handleSave();
                  onClose();
                }}
                className="w-10 h-10 bg-[#212121] rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
                whileTap={{ scale: 0.95 }}
              >
                <MoreHorizontal size={20} className="text-white" />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Content area - Ajustado para compensar header fixo */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.2 }}
        className="flex-1 overflow-hidden pt-20"
      >
        <div className="h-full max-w-4xl mx-auto px-4 py-6 flex flex-col">
          {/* Title input */}
          <textarea
            ref={titleRef}
            value={title}
            onChange={handleTitleChange}
            className="w-full text-3xl md:text-4xl font-bold bg-transparent text-gray-900 placeholder-gray-400 resize-none border-none outline-none leading-tight mb-4"
            placeholder="Título da sua ideia"
            rows={1}
            style={{ 
              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
              lineHeight: '1.2'
            }}
            onInput={(e) => autoResize(e.target as HTMLTextAreaElement)}
          />

          {/* Date indicator */}
          <div className="text-sm text-gray-500 mb-6">
            {new Date().toLocaleDateString('pt-BR', {
              weekday: 'long',
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            })}
          </div>

          {/* Content input */}
          <textarea
            ref={contentRef}
            value={content}
            onChange={handleContentChange}
            className="flex-1 w-full text-lg bg-transparent text-gray-900 placeholder-gray-400 resize-none border-none outline-none leading-relaxed"
            placeholder="Desenvolva sua ideia aqui... Você pode escrever sobre o problema que resolve, como funciona, tecnologias necessárias, modelo de negócio, ou qualquer detalhe que considere importante."
            style={{ 
              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
              lineHeight: '1.6',
              minHeight: 'calc(100vh - 280px)'
            }}
            onInput={(e) => autoResize(e.target as HTMLTextAreaElement)}
          />

          {/* Character count */}
          <div className="flex-shrink-0 mt-4 flex justify-between items-center">
            <div className="text-xs text-gray-400">
              ⌘ + S para salvar • ⌘ + ⇧ + S para compartilhar
            </div>
            <span className={`text-xs ${
              content.length > maxChars * 0.9 ? 'text-orange-500' : 'text-gray-400'
            }`}>
              {content.length.toLocaleString()} / {maxChars.toLocaleString()}
            </span>
          </div>
        </div>
      </motion.div>

      {/* Character limit warning */}
      <AnimatePresence>
        {showCharLimit && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="fixed bottom-20 left-1/2 -translate-x-1/2 px-6 py-3 bg-gray-900 text-white rounded-full shadow-lg"
          >
            <span className="text-sm">
              Limite de {maxChars.toLocaleString()} caracteres atingido
            </span>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default IdeaNotepad;