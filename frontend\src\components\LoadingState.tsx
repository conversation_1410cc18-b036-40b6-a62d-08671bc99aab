import React from 'react';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { ApiError } from '../lib/query-utils';

interface LoadingStateProps {
  isLoading: boolean;
  error?: ApiError | Error | null;
  isEmpty?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  onRetry?: () => void;
  children: React.ReactNode;
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  isLoading,
  error,
  isEmpty = false,
  emptyMessage = 'Nenhum item encontrado',
  emptyIcon,
  onRetry,
  children,
  className = '',
}) => {
  // Loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    const isApiError = error instanceof ApiError;
    const errorMessage = isApiError ? error.message : 'Erro desconhecido';
    const canRetry = isApiError && error.status !== 401 && error.status !== 403;

    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center max-w-md">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-6 h-6 text-red-600" />
          </div>
          
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Erro ao carregar dados
          </h3>
          
          <p className="text-gray-600 mb-4">
            {errorMessage}
          </p>

          {canRetry && onRetry && (
            <button
              onClick={onRetry}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Tentar novamente
            </button>
          )}

          {import.meta.env.DEV && (
            <details className="mt-4 text-left">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                Detalhes do erro (desenvolvimento)
              </summary>
              <div className="mt-2 p-3 bg-gray-50 rounded-lg text-xs">
                <p><strong>Tipo:</strong> {error.constructor.name}</p>
                <p><strong>Mensagem:</strong> {error.message}</p>
                {isApiError && error.status && (
                  <p><strong>Status:</strong> {error.status}</p>
                )}
                {isApiError && error.code && (
                  <p><strong>Código:</strong> {error.code}</p>
                )}
                <pre className="mt-2 whitespace-pre-wrap text-xs text-gray-600">
                  {error.stack}
                </pre>
              </div>
            </details>
          )}
        </div>
      </div>
    );
  }

  // Empty state
  if (isEmpty) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          {emptyIcon || (
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="w-6 h-6 bg-gray-300 rounded" />
            </div>
          )}
          
          <p className="text-gray-600">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  // Success state - render children
  return <>{children}</>;
};

// Specialized loading components for common use cases
export const PageLoadingState: React.FC<{ message?: string }> = ({ 
  message = 'Carregando página...' 
}) => (
  <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
    <div className="text-center">
      <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
      <p className="text-gray-600">{message}</p>
    </div>
  </div>
);

export const CardLoadingState: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => (
  <div className={`bg-white rounded-xl shadow-sm p-6 ${className}`}>
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
      <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
    </div>
  </div>
);

export const ListLoadingState: React.FC<{ 
  count?: number;
  className?: string;
}> = ({ count = 3, className = '' }) => (
  <div className={`space-y-4 ${className}`}>
    {Array.from({ length: count }).map((_, index) => (
      <CardLoadingState key={index} />
    ))}
  </div>
);

export default LoadingState;
