import React, { useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { Home, CheckCircle, DollarSign, Lightbulb, Settings, LogOut, User, Shield, BarChart3, Users, CreditCard } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const Navigation: React.FC = () => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const isActive = (path: string) => location.pathname === path;

  const navItems = [
    { path: '/dashboard', icon: Home, label: 'Início' },
    { path: '/tasks', icon: CheckCircle, label: 'Tare<PERSON>s' },
    { path: '/finances', icon: DollarSign, label: 'Fin<PERSON><PERSON><PERSON>' },
    { path: '/ideas', icon: Lightbulb, label: 'I<PERSON><PERSON>' },
    { path: '/profile', icon: Settings, label: 'Configura<PERSON><PERSON><PERSON>' }
  ];

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:block fixed top-0 left-0 right-0 h-[72px] bg-[#212121] z-50">
        <div className="max-w-7xl mx-auto px-4 h-full flex items-center justify-between">
          <div className="text-white text-xl font-bold">
            Dupli
          </div>
          <div className="flex items-center space-x-8">
            <ul className="flex items-center space-x-8">
              {navItems.map((item) => (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={`flex items-center space-x-2 font-light transition-colors ${
                      isActive(item.path)
                        ? 'text-[#B4EB00]'
                        : 'text-white hover:text-[#B4EB00]'
                    }`}
                  >
                    <item.icon size={20} />
                    <span>{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-2 text-white hover:text-[#B4EB00] transition-colors"
              >
                <User size={20} />
                <span className="font-light">{user?.name}</span>
              </button>

              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg overflow-hidden z-50">
                  {user?.is_admin && (
                    <>
                      <Link
                        to="/admin"
                        className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <BarChart3 size={16} />
                        <span>Admin</span>
                      </Link>
                      <hr className="border-gray-200" />
                    </>
                  )}
                  <Link
                    to="/profile"
                    className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <User size={16} />
                    <span>Perfil</span>
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                  >
                    <LogOut size={16} />
                    <span>Sair</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="md:hidden fixed bottom-4 left-4 right-4 h-[70px] bg-[#212121] rounded-full z-50 shadow-[0_0_30px_rgba(0,0,0,0.25)] backdrop-blur-lg bg-opacity-95">
        <ul className="h-full flex items-center justify-evenly px-4">
          {navItems.map((item) => (
            <li key={item.path}>
              <Link
                to={item.path}
                className="p-3 flex items-center justify-center"
              >
                <item.icon
                  size={24}
                  className={isActive(item.path) ? 'text-[#B4EB00]' : 'text-[#BBBBBB]'}
                />
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </>
  );
};

export default Navigation;