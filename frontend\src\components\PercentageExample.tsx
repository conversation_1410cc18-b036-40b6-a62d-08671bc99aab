import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { 
  calculatePercentageChange, 
  calculateProgressPercentage,
  calculateDistributionPercentages,
  formatPercentage,
  getTrendColor,
  calculateFinancialComparisons,
  calculateTaskComparisons 
} from '../utils/percentageCalculator';

interface PercentageExampleProps {
  data: {
    // Dados financeiros atuais e anteriores
    finances: {
      currentMonth: { income: number; expenses: number; savings: number };
      previousMonth: { income: number; expenses: number; savings: number };
      currentYear: { income: number; expenses: number; savings: number };
      previousYear: { income: number; expenses: number; savings: number };
      annualGoal: number;
    };
    // Dados de tarefas atuais e anteriores
    tasks: {
      currentWeek: { completed: number; total: number };
      previousWeek: { completed: number; total: number };
      currentMonth: { completed: number; total: number };
      previousMonth: { completed: number; total: number };
    };
    // Dados de categorias para distribuição
    categories: {
      name: string;
      value: number;
    }[];
  };
}

const PercentageExample: React.FC<PercentageExampleProps> = ({ data }) => {
  // Calcular comparações financeiras
  const financialComparisons = calculateFinancialComparisons(data.finances);
  
  // Calcular comparações de tarefas
  const taskComparisons = calculateTaskComparisons(data.tasks);
  
  // Calcular progresso da meta anual
  const annualProgress = calculateProgressPercentage(
    data.finances.currentYear.savings,
    data.finances.annualGoal
  );
  
  // Calcular distribuição de categorias
  const categoryValues = data.categories.map(cat => cat.value);
  const categoryPercentages = calculateDistributionPercentages(categoryValues);
  
  // Função para renderizar indicador de tendência
  const renderTrendIndicator = (
    result: { percentage: number; trend: 'up' | 'down' | 'stable' },
    inverted: boolean = false
  ) => {
    const colors = getTrendColor(result.trend, inverted);
    const Icon = result.trend === 'up' ? TrendingUp : 
                 result.trend === 'down' ? TrendingDown : Minus;
    
    return (
      <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${colors.bg} ${colors.text}`}>
        <Icon size={12} />
        <span>{formatPercentage(result.percentage)}</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold text-gray-900">
        Exemplo de Cálculos Percentuais com Dados Reais
      </h2>
      
      {/* Comparações Financeiras Mensais */}
      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Comparações Financeiras (Mês Atual vs Anterior)</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Receitas</span>
              {renderTrendIndicator(financialComparisons.monthly.income)}
            </div>
            <div className="text-2xl font-bold text-green-600">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(data.finances.currentMonth.income)}
            </div>
            <div className="text-sm text-gray-600">
              {financialComparisons.monthly.income.difference >= 0 ? '+' : ''}
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(financialComparisons.monthly.income.difference)}
            </div>
          </div>
          
          <div className="p-4 bg-red-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Despesas</span>
              {renderTrendIndicator(financialComparisons.monthly.expenses, true)}
            </div>
            <div className="text-2xl font-bold text-red-600">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(data.finances.currentMonth.expenses)}
            </div>
            <div className="text-sm text-gray-600">
              {financialComparisons.monthly.expenses.difference >= 0 ? '+' : ''}
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(financialComparisons.monthly.expenses.difference)}
            </div>
          </div>
          
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Economias</span>
              {renderTrendIndicator(financialComparisons.monthly.savings)}
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(data.finances.currentMonth.savings)}
            </div>
            <div className="text-sm text-gray-600">
              {financialComparisons.monthly.savings.difference >= 0 ? '+' : ''}
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(financialComparisons.monthly.savings.difference)}
            </div>
          </div>
        </div>
      </div>
      
      {/* Progresso da Meta Anual */}
      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Progresso da Meta Anual</h3>
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Economias anuais: {formatPercentage(annualProgress, false)} da meta
          </span>
          <span className="text-sm text-gray-600">
            {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL'
            }).format(data.finances.currentYear.savings)} / {' '}
            {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL'
            }).format(data.finances.annualGoal)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${annualProgress}%` }}
          />
        </div>
      </div>
      
      {/* Comparações de Tarefas */}
      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Comparações de Tarefas</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Taxa de Conclusão Semanal</span>
              {renderTrendIndicator(taskComparisons.weekly.completionRate)}
            </div>
            <div className="text-xl font-bold text-gray-900">
              {formatPercentage(
                (data.tasks.currentWeek.completed / data.tasks.currentWeek.total) * 100,
                false
              )}
            </div>
            <div className="text-sm text-gray-600">
              {data.tasks.currentWeek.completed} de {data.tasks.currentWeek.total} tarefas
            </div>
          </div>
          
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Taxa de Conclusão Mensal</span>
              {renderTrendIndicator(taskComparisons.monthly.completionRate)}
            </div>
            <div className="text-xl font-bold text-gray-900">
              {formatPercentage(
                (data.tasks.currentMonth.completed / data.tasks.currentMonth.total) * 100,
                false
              )}
            </div>
            <div className="text-sm text-gray-600">
              {data.tasks.currentMonth.completed} de {data.tasks.currentMonth.total} tarefas
            </div>
          </div>
        </div>
      </div>
      
      {/* Distribuição de Categorias */}
      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Distribuição por Categorias</h3>
        <div className="space-y-3">
          {data.categories.map((category, index) => (
            <div key={category.name} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-blue-500 rounded-full" />
                <span className="text-sm font-medium text-gray-700">{category.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${categoryPercentages[index]}%` }}
                  />
                </div>
                <span className="text-sm font-medium text-gray-900 w-12 text-right">
                  {formatPercentage(categoryPercentages[index], false)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Comparação Anual */}
      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Comparação Anual (Este Ano vs Anterior)</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {formatPercentage(financialComparisons.yearly.income.percentage)}
            </div>
            <div className="text-sm text-gray-600">Receitas</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-red-600 mb-1">
              {formatPercentage(financialComparisons.yearly.expenses.percentage)}
            </div>
            <div className="text-sm text-gray-600">Despesas</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {formatPercentage(financialComparisons.yearly.savings.percentage)}
            </div>
            <div className="text-sm text-gray-600">Economias</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PercentageExample;
