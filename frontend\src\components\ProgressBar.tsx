import React from 'react';
import { motion } from 'framer-motion';

interface ProgressBarProps {
  percentage: number;
  color?: string;
  bgColor?: string;
  height?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  percentage,
  color = '#B4EB00',
  bgColor = '#E5E5E5',
  height = 8
}) => {
  return (
    <div 
      className="relative rounded-full overflow-hidden"
      style={{ height, backgroundColor: bgColor }}
    >
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: `${percentage}%` }}
        transition={{ duration: 1, ease: "easeOut" }}
        className="absolute left-0 top-0 h-full rounded-full"
        style={{ backgroundColor: color }}
      />
    </div>
  );
};

export default ProgressBar;