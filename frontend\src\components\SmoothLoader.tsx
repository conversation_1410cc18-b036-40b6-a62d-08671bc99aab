import React from 'react';

interface SmoothLoaderProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
}

const SmoothLoader: React.FC<SmoothLoaderProps> = ({ 
  isLoading, 
  children, 
  fallback,
  delay = 300 
}) => {
  const [showLoader, setShowLoader] = React.useState(false);

  React.useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (isLoading) {
      // Só mostra o loader após um delay para evitar "piscar" em loads rápidos
      timer = setTimeout(() => {
        setShowLoader(true);
      }, delay);
    } else {
      setShowLoader(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isLoading, delay]);

  if (isLoading && showLoader) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (isLoading && !showLoader) {
    // Durante o delay, mostra o conteúdo anterior ou nada
    return fallback ? <>{fallback}</> : null;
  }

  return <>{children}</>;
};

export default SmoothLoader;