import React, { useState, useEffect } from 'react';
import { Loader2, <PERSON><PERSON>ard, <PERSON>tings, CheckCircle, AlertCircle, ExternalLink, Key, Zap } from 'lucide-react';
import { authenticatedApi } from '../../lib/api';
import { toast } from '../../utils/toast';
import { Link } from 'react-router-dom';

interface StripeConfig {
  public_key?: string;
  secret_key?: string;
  webhook_endpoint_secret?: string;
  test_mode: boolean;
  is_configured: boolean;
}

interface PaymentSettings {
  stripe_public_key?: string;
  stripe_secret_key?: string;
  stripe_webhook_secret?: string;
  is_active: boolean;
}

export const AdminPaymentSettings: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [stripeConfig, setStripeConfig] = useState<StripeConfig>({
    test_mode: true,
    is_configured: false
  });
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    is_active: false
  });

  useEffect(() => {
    fetchPaymentData();
  }, []);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);

      // Fetch both Stripe config and payment settings
      const [stripeResponse, settingsResponse] = await Promise.all([
        authenticatedApi.get('admin/stripe-config'),
        authenticatedApi.get('admin/payment-settings')
      ]);

      if (!stripeResponse.ok || !settingsResponse.ok) {
        const errorStatus = stripeResponse.ok ? settingsResponse.status : stripeResponse.status;
        throw new Error(`HTTP ${errorStatus}: ${stripeResponse.ok ? settingsResponse.statusText : stripeResponse.statusText}`);
      }

      const stripeData = await stripeResponse.json();
      const settingsData = await settingsResponse.json();

      setStripeConfig(stripeData || { test_mode: true, is_configured: false });
      setPaymentSettings(settingsData || { is_active: false });
    } catch (error: any) {
      console.error('Erro ao buscar configurações:', error);
      
      // Melhor tratamento de erro
      if (error.message?.includes('401')) {
        toast.error('Sessão expirada. Faça login novamente.');
      } else if (error.message?.includes('403')) {
        toast.error('Acesso negado. Você não tem permissão para acessar configurações de pagamento.');
      } else if (error.message?.includes('404')) {
        toast.error('Endpoint não encontrado. Verifique se o servidor está configurado corretamente.');
      } else if (error.message?.includes('500')) {
        toast.error('Erro interno do servidor. Tente novamente mais tarde.');
      } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
        toast.error('Erro de conexão com o servidor. Verifique sua conexão com a internet.');
      } else {
        toast.error('Erro ao carregar configurações de pagamento');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSavePaymentSettings = async () => {
    try {
      setSaving(true);
      await authenticatedApi.put('admin/payment-settings', { json: paymentSettings });
      toast.success('Configurações de pagamento salvas com sucesso');
      fetchPaymentData(); // Refresh data
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      toast.error('Erro ao salvar configurações de pagamento');
    } finally {
      setSaving(false);
    }
  };

  const handleTestStripeConnection = async () => {
    if (!stripeConfig.secret_key) {
      toast.error('Configure a chave secreta do Stripe primeiro');
      return;
    }

    try {
      setTesting(true);
      await authenticatedApi.post('admin/stripe-config/test', {
        json: {
          secret_key: stripeConfig.secret_key,
          test_mode: stripeConfig.test_mode
        }
      });
      toast.success('Conexão com Stripe testada com sucesso!');
    } catch (error) {
      console.error('Erro ao testar Stripe:', error);
      toast.error('Falha ao conectar com Stripe. Verifique suas credenciais.');
    } finally {
      setTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Configurações de Pagamento
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Configure métodos de pagamento e gateways
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {paymentSettings.is_active ? (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Ativo
            </span>
          ) : (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
              <AlertCircle className="h-3 w-3 mr-1" />
              Inativo
            </span>
          )}
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCard className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Gateway Principal</p>
              <p className="text-lg font-semibold">Stripe</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <Settings className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <p className="text-lg font-semibold">
                {stripeConfig.is_configured ? 'Configurado' : 'Pendente'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Zap className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Modo</p>
              <p className="text-lg font-semibold">
                {stripeConfig.test_mode ? 'Teste' : 'Produção'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('general')}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'general'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Configurações Gerais
            </button>
            <button
              onClick={() => setActiveTab('stripe')}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'stripe'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Stripe
            </button>
          </nav>
        </div>
        
        <div className="p-6">
          {/* General Settings Tab */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Configurações Gerais de Pagamento</h3>
                <p className="text-sm text-gray-600">
                  Configure as opções globais de pagamento da plataforma
                </p>
              </div>

              {/* Global Payment Toggle */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <label className="text-base font-medium text-gray-900">Sistema de Pagamento</label>
                  <p className="text-sm text-gray-500">
                    Ativar ou desativar o sistema de pagamentos da plataforma
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={paymentSettings.is_active}
                  onChange={(e) => 
                    setPaymentSettings(prev => ({ ...prev, is_active: e.target.checked }))
                  }
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
              </div>

              {/* Stripe Keys (Quick View) */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Chaves de API</h3>
                <div className="grid gap-4">
                  <div>
                    <label htmlFor="stripe_public_key" className="block text-sm font-medium text-gray-700 mb-1">Chave Pública do Stripe</label>
                    <div className="relative">
                      <input
                        id="stripe_public_key"
                        type="password"
                        value={paymentSettings.stripe_public_key || ''}
                        onChange={(e) => 
                          setPaymentSettings(prev => ({ ...prev, stripe_public_key: e.target.value }))
                        }
                        placeholder="pk_live_... ou pk_test_..."
                        className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <Key className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="stripe_secret_key" className="block text-sm font-medium text-gray-700 mb-1">Chave Secreta do Stripe</label>
                    <div className="relative">
                      <input
                        id="stripe_secret_key"
                        type="password"
                        value={paymentSettings.stripe_secret_key || ''}
                        onChange={(e) => 
                          setPaymentSettings(prev => ({ ...prev, stripe_secret_key: e.target.value }))
                        }
                        placeholder="sk_live_... ou sk_test_..."
                        className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <Key className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="stripe_webhook_secret" className="block text-sm font-medium text-gray-700 mb-1">Webhook Secret</label>
                    <div className="relative">
                      <input
                        id="stripe_webhook_secret"
                        type="password"
                        value={paymentSettings.stripe_webhook_secret || ''}
                        onChange={(e) => 
                          setPaymentSettings(prev => ({ ...prev, stripe_webhook_secret: e.target.value }))
                        }
                        placeholder="whsec_..."
                        className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <Key className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end space-x-4">
                <button 
                  onClick={fetchPaymentData}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Cancelar
                </button>
                <button 
                  onClick={handleSavePaymentSettings} 
                  disabled={saving}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Salvar Configurações
                </button>
              </div>
            </div>
          )}
          
          {/* Stripe Settings Tab */}
          {activeTab === 'stripe' && (
            <div className="space-y-6">
              <div className="flex items-center mb-4">
                <CreditCard className="h-5 w-5 mr-2 text-gray-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Configuração do Stripe</h3>
                  <p className="text-sm text-gray-600">
                    Configure sua integração com o Stripe para processar pagamentos
                  </p>
                </div>
              </div>
              
              {/* Status Alert */}
              <div className={`p-4 rounded-lg border flex items-start space-x-3 ${
                stripeConfig.is_configured 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-yellow-200 bg-yellow-50'
              }`}>
                <AlertCircle className="h-4 w-4 mt-0.5 text-yellow-600" />
                <p className="text-sm text-gray-700">
                  {stripeConfig.is_configured
                    ? 'Stripe está configurado e pronto para uso.'
                    : 'Stripe ainda não está configurado. Complete a configuração para habilitar pagamentos.'
                  }
                </p>
              </div>

              {/* Current Configuration */}
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Chave Pública</label>
                  <div className="p-3 bg-gray-50 rounded-md text-sm font-mono">
                    {stripeConfig.public_key ? 
                      `${stripeConfig.public_key.substring(0, 12)}...` : 
                      'Não configurada'
                    }
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Modo de Operação</label>
                  <div className="p-3 bg-gray-50 rounded-md text-sm">
                    {stripeConfig.test_mode ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                        Modo de Teste
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Modo de Produção
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-wrap gap-4">
                <button
                  onClick={handleTestStripeConnection} 
                  disabled={testing || !stripeConfig.secret_key}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {testing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Zap className="h-4 w-4 mr-2" />
                  Testar Conexão
                </button>
                
                <Link 
                  to="/admin/stripe-config"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Configurar Stripe
                </Link>
              </div>

              {/* Help Text */}
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-blue-900 mb-2">
                  Primeiros Passos
                </h4>
                <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                  <li>Crie uma conta no Stripe em stripe.com</li>
                  <li>Obtenha suas chaves de API no dashboard do Stripe</li>
                  <li>Configure suas chaves na página de configuração do Stripe</li>
                  <li>Teste a conexão para verificar se está funcionando</li>
                  <li>Ative o sistema de pagamentos nas configurações gerais</li>
                </ol>
              </div>
              
              {/* Additional Payment Methods (Future) */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Métodos de Pagamento Adicionais</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Configure métodos de pagamento alternativos
                </p>
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Em breve</p>
                  <p className="text-sm">
                    Suporte para PayPal, PIX e outros métodos de pagamento serão adicionados em futuras atualizações.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPaymentSettings;
