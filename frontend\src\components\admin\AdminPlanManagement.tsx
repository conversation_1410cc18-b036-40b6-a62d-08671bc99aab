import React, { useState, useEffect } from 'react';
import { Loader2, CreditCard, Edit, Plus, DollarSign, Calendar, CheckCircle, X } from 'lucide-react';
import { authenticatedApi } from '../../lib/api';
import { toast } from '../../utils/toast';

interface Plan {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  currency: string;
  billing_period: 'monthly' | 'yearly';
  features: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
}

interface UpdatePlanData {
  name?: string;
  description?: string;
  price?: string;
  features?: string[];
  is_active?: boolean;
  sort_order?: number;
}

export const AdminPlanManagement: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<UpdatePlanData>({});
  const [updating, setUpdating] = useState(false);
  const [newFeature, setNewFeature] = useState('');

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await authenticatedApi.get('admin/plans');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      // Normalizar dados para garantir que features seja sempre um array
      const normalizedPlans = Array.isArray(data) ? data.map(plan => ({
        ...plan,
        features: Array.isArray(plan.features) ? plan.features : []
      })) : [];
      setPlans(normalizedPlans);
    } catch (error: any) {
      console.error('Erro ao buscar planos:', error);
      
      // Melhor tratamento de erro
      if (error.message?.includes('401')) {
        toast.error('Sessão expirada. Faça login novamente.');
      } else if (error.message?.includes('403')) {
        toast.error('Acesso negado. Você não tem permissão para gerenciar planos.');
      } else if (error.message?.includes('404')) {
        toast.error('Endpoint não encontrado. Verifique se o servidor está configurado corretamente.');
      } else if (error.message?.includes('500')) {
        toast.error('Erro interno do servidor. Tente novamente mais tarde.');
      } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
        toast.error('Erro de conexão com o servidor. Verifique sua conexão com a internet.');
      } else {
        toast.error('Erro ao carregar planos');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleEditPlan = (plan: Plan) => {
    setSelectedPlan(plan);
    setEditFormData({
      name: plan.name,
      description: plan.description,
      price: plan.price.toString(),
      features: Array.isArray(plan.features) ? [...plan.features] : [],
      is_active: plan.is_active,
      sort_order: plan.sort_order,
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdatePlan = async () => {
    if (!selectedPlan) return;

    try {
      setUpdating(true);
      await authenticatedApi.put(`admin/plans/${selectedPlan.id}`, { json: editFormData });

      toast.success('Plano atualizado com sucesso');
      setIsEditDialogOpen(false);
      fetchPlans(); // Refresh the list
    } catch (error) {
      console.error('Erro ao atualizar plano:', error);
      toast.error('Erro ao atualizar plano');
    } finally {
      setUpdating(false);
    }
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      setEditFormData(prev => ({
        ...prev,
        features: [...(prev.features || []), newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (index: number) => {
    setEditFormData(prev => ({
      ...prev,
      features: prev.features?.filter((_, i) => i !== index) || []
    }));
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(price);
  };

  const getBillingPeriodLabel = (period: string) => {
    return period === 'monthly' ? 'Mensal' : 'Anual';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Gestão de Planos
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Gerencie planos de assinatura e preços
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 border">
            <CreditCard className="h-4 w-4 mr-2" />
            {plans.length} planos
          </div>
          <button
            onClick={() => {/* TODO: Implementar criação de plano */}}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Adicionar Plano
          </button>
        </div>
      </div>

      {/* Plans Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {plans.map((plan) => (
          <div key={plan.id} className={`relative bg-white rounded-lg shadow p-6 space-y-4 ${!plan.is_active ? 'opacity-60' : ''}`}>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                <div className="flex items-center space-x-2">
                  {plan.is_active ? (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Ativo
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                      Inativo
                    </span>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-600">{plan.description}</p>
            </div>
            {/* Preço */}
            <div className="text-center py-4">
              <div className="text-3xl font-bold text-blue-600">
                {formatPrice(plan.price, plan.currency)}
              </div>
              <div className="text-sm text-gray-500">
                por {getBillingPeriodLabel(plan.billing_period).toLowerCase()}
              </div>
            </div>

            {/* Funcionalidades */}
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">
                Funcionalidades:
              </h4>
              <div className="space-y-1">
                {Array.isArray(plan.features) && plan.features.length > 0 ? (
                  <>
                    {plan.features.slice(0, 4).map((feature, index) => (
                      <div key={index} className="flex items-center text-sm">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                    {plan.features.length > 4 && (
                      <div className="text-sm text-gray-500">
                        +{plan.features.length - 4} mais...
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-sm text-gray-500">
                    Nenhuma funcionalidade configurada
                  </div>
                )}
              </div>
            </div>

            {/* Informações Adicionais */}
            <div className="flex items-center justify-between text-xs text-gray-500 pt-4 border-t">
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                Ordem: {plan.sort_order}
              </div>
              <div className="flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                {plan.currency.toUpperCase()}
              </div>
            </div>

            {/* Ações */}
            <div className="pt-4">
              <button
                onClick={() => handleEditPlan(plan)}
                className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Edit className="h-3 w-3 mr-2" />
                Editar Plano
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Edit Plan Modal */}
      {isEditDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Editar Plano</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Altere as informações do plano selecionado.
                </p>
              </div>
              <button
                onClick={() => setIsEditDialogOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            {selectedPlan && (
              <div className="p-6 space-y-6">
                {/* Nome e Descrição */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Nome do Plano</label>
                    <input
                      id="name"
                      type="text"
                      value={editFormData.name || ''}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Nome do plano"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">Preço</label>
                    <input
                      id="price"
                      type="number"
                      step="0.01"
                      value={editFormData.price || ''}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, price: e.target.value }))}
                      placeholder="0.00"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                  <textarea
                    id="description"
                    value={editFormData.description || ''}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Descrição do plano"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Configurações */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="sort_order" className="block text-sm font-medium text-gray-700 mb-1">Ordem de Exibição</label>
                    <input
                      id="sort_order"
                      type="number"
                      value={editFormData.sort_order || 0}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                      placeholder="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <input
                      id="is_active"
                      type="checkbox"
                      checked={editFormData.is_active || false}
                      onChange={(e) => setEditFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <label htmlFor="is_active" className="text-sm font-medium text-gray-700">Plano Ativo</label>
                  </div>
                </div>

                {/* Funcionalidades */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Funcionalidades</label>
                  <div className="space-y-2">
                    {editFormData.features?.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="flex-1 p-2 bg-gray-50 rounded text-sm">
                          {feature}
                        </div>
                        <button
                          onClick={() => removeFeature(index)}
                          className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                        >
                          Remover
                        </button>
                      </div>
                    ))}
                  </div>
                  
                  {/* Adicionar nova funcionalidade */}
                  <div className="flex items-center space-x-2 mt-3">
                    <input
                      type="text"
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      placeholder="Nova funcionalidade..."
                      onKeyPress={(e) => e.key === 'Enter' && addFeature()}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button 
                      onClick={addFeature}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Adicionar
                    </button>
                  </div>
                </div>

                {/* Informações do Plano */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Informações do Plano</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><strong>ID:</strong> {selectedPlan.id}</p>
                    <p><strong>Slug:</strong> {selectedPlan.slug}</p>
                    <p><strong>Moeda:</strong> {selectedPlan.currency.toUpperCase()}</p>
                    <p><strong>Período:</strong> {getBillingPeriodLabel(selectedPlan.billing_period)}</p>
                    <p><strong>Criado em:</strong> {new Date(selectedPlan.created_at).toLocaleDateString('pt-BR')}</p>
                  </div>
                </div>
              </div>
            )}
            <div className="flex justify-end space-x-2 p-6 border-t bg-gray-50">
              <button
                onClick={() => setIsEditDialogOpen(false)}
                disabled={updating}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancelar
              </button>
              <button 
                onClick={handleUpdatePlan} 
                disabled={updating}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {updating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Salvar Alterações
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPlanManagement;
