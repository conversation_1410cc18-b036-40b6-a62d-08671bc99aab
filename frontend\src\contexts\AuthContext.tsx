import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authApi, tokenUtils, AuthResponse } from '../lib/api';
import { cacheUtils } from '../utils/cacheUtils';

interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  timezone: string;
  is_admin?: boolean;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    timezone?: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Função para renovar autenticação
  const refreshAuth = async (): Promise<void> => {
    try {
      const response = await authApi.refreshToken();
      
      // Handle nested response structure: response.data.data or response.data
      const authData = response.data?.data || response.data || response;
      
      tokenUtils.setTokens(authData);
      setUser(authData.user);
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      tokenUtils.clearTokens();
      setUser(null);
      throw error;
    }
  };

  // Verificar autenticação ao carregar a aplicação
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Detectar e lidar com loops de loading
        if (cacheUtils.detectLoadingLoop()) {
          console.error('Loop de loading detectado - executando reset de emergência');
          cacheUtils.emergencyReset();
          return;
        }
        
        // Verificar versão do cache e invalidar se necessário
        const currentVersion = '2.0.0'; // Incrementar quando houver mudanças na autenticação
        if (cacheUtils.shouldInvalidateCache(currentVersion)) {
          cacheUtils.clearAllAuthData();
        }
        
        // Limpar dados corrompidos automaticamente
        cacheUtils.cleanCorruptedData();
        
        const accessToken = localStorage.getItem('access_token');
        const userData = tokenUtils.getUser();
        
        // Timeout para evitar loading infinito
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Authentication timeout')), 10000); // 10 segundos
        });
        
        if (accessToken && userData) {
          // Se temos token e dados do usuário, verificar se o token ainda é válido
          if (!tokenUtils.isTokenExpired(accessToken)) {
            setUser(userData);
            // Reset loop detector on successful load
            cacheUtils.resetLoopDetector();
          } else {
            // Token expirado, tentar refresh com timeout
            try {
              await Promise.race([refreshAuth(), timeoutPromise]);
              cacheUtils.resetLoopDetector();
            } catch (refreshError) {
              console.error('Erro ao renovar token durante inicialização:', refreshError);
              tokenUtils.clearTokens();
            }
          }
        } else if (accessToken && !userData) {
          // Temos token mas não temos dados do usuário - limpar tokens corrompidos
          console.warn('Token sem dados do usuário detectado - limpando tokens');
          tokenUtils.clearTokens();
        } else {
          // Nenhum token - reset loop detector
          cacheUtils.resetLoopDetector();
        }
        
      } catch (error) {
        console.error('Erro ao inicializar autenticação:', error);
        tokenUtils.clearTokens();
      } finally {
        // Garantir que isLoading sempre será resetado
        setTimeout(() => setIsLoading(false), 100);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    try {
      const response: any = await authApi.login({ email, password });
      
      // Handle nested response structure: response.data.data or response.data
      const authData = response.data?.data || response.data || response;
      
      tokenUtils.setTokens(authData);
      setUser(authData.user);
    } catch (error: any) {
      
      // Tratar diferentes tipos de erro
      let errorMessage = 'Erro interno do servidor. Tente novamente.';
      
      if (error.response?.status === 401) {
        errorMessage = 'Email ou senha incorretos';
      } else if (error.response?.status === 400) {
        errorMessage = 'Dados inválidos';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      throw new Error(errorMessage);
    }
  };

  const register = async (userData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    timezone?: string;
  }): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Definir timezone padrão se não fornecido
      const registrationData = {
        ...userData,
        timezone: userData.timezone || 'America/Sao_Paulo',
      };
      
      const response: any = await authApi.register(registrationData);
      
      // Handle nested response structure: response.data.data or response.data
      const responseData = response.data?.data || response.data || response;
      
      alert('Conta criada com sucesso! Faça login para continuar.');
      window.location.href = '/login';
    } catch (error: any) {
      console.error('Erro no registro:', error);
      
      // Tratar diferentes tipos de erro
      if (error.response?.status === 409) {
        throw new Error('Este email já está em uso');
      } else if (error.response?.status === 400) {
        throw new Error('Dados inválidos');
      } else {
        throw new Error('Erro interno do servidor. Tente novamente.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authApi.logout();
    } catch (error) {
      console.error('Erro no logout:', error);
    } finally {
      tokenUtils.clearTokens();
      setUser(null);
      setIsLoading(false);
    }
  };

  const updateUser = (userData: Partial<User>): void => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      // Atualizar também no localStorage
      localStorage.setItem('user_data', JSON.stringify(updatedUser));
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshAuth,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
