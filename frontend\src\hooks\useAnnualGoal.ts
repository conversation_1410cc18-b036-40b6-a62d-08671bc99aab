import { useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';

interface SetAnnualGoalData {
  goalAmount: number;
  year?: number;
}

interface AnnualGoalResponse {
  id: number;
  amount: string;
  year: number;
  user_id?: number | null | undefined;
  created_at: Date;
  updated_at: Date;
}

// Função para definir meta anual
const setAnnualGoal = async (data: SetAnnualGoalData): Promise<AnnualGoalResponse> => {
  const response = await authenticatedApi.post('config/annual-savings-goal', {
    json: {
      amount: data.goalAmount.toString(),
      year: data.year || new Date().getFullYear()
    }
  });
  return response.json();
};

// Hook para definir meta anual
export const useSetAnnualGoal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: setAnnualGoal,
    onSuccess: () => {
      // Invalidar cache do dashboard para atualizar os dados
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Também invalidar cache de finanças se existir
      queryClient.invalidateQueries({ queryKey: ['finances'] });
    },
    onError: (error) => {
      console.error('Erro ao definir meta anual:', error);
    }
  });
};
