import { useQuery } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';

// Simplified dashboard data structure
interface SimplifiedDashboardData {
  user: {
    name: string;
    timezone: string;
  };
  monthlyProgress?: {
    appointments?: {
      completed: number;
      total: number;
    };
    savings: number;
    ideas: number;
  };
}

export const useDashboardData = () => {
  return useQuery({
    queryKey: ['dashboard-simple'],
    queryFn: async (): Promise<SimplifiedDashboardData> => {
      // For now, we'll create a simplified response that only has user data
      // The monthly progress section still needs that data
      const response = await authenticatedApi.get('dashboard');
      const responseData = await response.json();
      
      // Com a correção no backend, a resposta agora é diretamente o objeto de dashboard
      const fullData = responseData as any;
      
      // Return only the data we actually need
      return {
        user: fullData.user || { name: '<PERSON><PERSON><PERSON><PERSON>', timezone: 'America/Sao_Paulo' },
        monthlyProgress: fullData.monthlyProgress
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
    retry: (failureCount, error: any) => {
      // Não tentar novamente para erros de autenticação
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
  });
};
