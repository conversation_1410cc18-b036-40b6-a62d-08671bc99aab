import { useState, useEffect } from 'react';

/**
 * Hook para debounce de estados de loading
 * Evita o "piscar" de loading states muito rápidos
 */
export const useDebounceLoading = (isLoading: boolean, delay: number = 300) => {
    const [debouncedLoading, setDebouncedLoading] = useState(false);

    useEffect(() => {
        let timer: NodeJS.Timeout;

        if (isLoading) {
            // Só mostra loading após o delay
            timer = setTimeout(() => {
                setDebouncedLoading(true);
            }, delay);
        } else {
            // Remove loading imediatamente quando termina
            setDebouncedLoading(false);
        }

        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [isLoading, delay]);

    return debouncedLoading;
};