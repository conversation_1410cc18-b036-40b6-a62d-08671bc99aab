import { useQuery } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import { useMemo } from 'react';

interface MonthlyComparisonData {
  currentMonth: {
    income: number;
    expenses: number;
    savings: number;
    balance: number;
  };
  previousMonth: {
    income: number;
    expenses: number;
    savings: number;
    balance: number;
  };
  percentageChanges: {
    income: number;
    expenses: number;
    savings: number;
    balance: number;
  };
}

export const useMonthlyComparison = () => {
  return useQuery({
    queryKey: ['monthly-comparison'],
    queryFn: async (): Promise<MonthlyComparisonData> => {
      const response = await authenticatedApi.get('finances/monthly-comparison');
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    enabled: false, // Desabilitado até endpoint estar disponível
  });
};

// Hook para calcular percentuais com fallback usando dados do summary
export const useFinancialPercentages = (summaryData?: any) => {
  return useMemo(() => {
    // Sempre usar valores de fallback para demonstração até API estar completa
    return {
      savingsChange: 15,
      incomeChange: 8,
      expensesChange: -3,
      balanceChange: 12,
      isUsingFallback: true
    };
    
    // TODO: Implementar cálculo real quando endpoint estiver disponível
    // if (!summaryData) {
    //   return fallbackValues;
    // }
    // 
    // const calculatePercentage = (current: number, previous: number) => {
    //   if (previous === 0) return current > 0 ? 100 : 0;
    //   return ((current - previous) / previous) * 100;
    // };
    // 
    // return {
    //   savingsChange: calculatePercentage(summaryData.currentMonth?.savings || 0, summaryData.previousMonth?.savings || 0),
    //   incomeChange: calculatePercentage(summaryData.currentMonth?.income || 0, summaryData.previousMonth?.income || 0),
    //   expensesChange: calculatePercentage(summaryData.currentMonth?.expenses || 0, summaryData.previousMonth?.expenses || 0),
    //   balanceChange: calculatePercentage(summaryData.currentMonth?.balance || 0, summaryData.previousMonth?.balance || 0),
    //   isUsingFallback: false
    // };
  }, [summaryData]);
};

// Hook para formatar percentuais
export const useFormattedPercentages = (percentages: ReturnType<typeof useFinancialPercentages>) => {
  return useMemo(() => {
    const formatPercentage = (value: number) => {
      const sign = value >= 0 ? '+' : '';
      return `${sign}${value.toFixed(1)}%`;
    };

    return {
      savings: formatPercentage(percentages.savingsChange),
      income: formatPercentage(percentages.incomeChange),
      expenses: formatPercentage(percentages.expensesChange),
      balance: formatPercentage(percentages.balanceChange),
      isUsingFallback: percentages.isUsingFallback
    };
  }, [percentages]);
};
