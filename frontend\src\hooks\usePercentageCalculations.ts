import { useMemo } from 'react';
import { 
  calculatePercentageChange, 
  calculateProgressPercentage,
  calculateDistributionPercentages,
  calculateFinancialComparisons,
  calculateTaskComparisons,
  calculateAverageGrowthRate,
  PercentageResult 
} from '../utils/percentageCalculator';

/**
 * Hook para cálculos de percentual baseado em dados financeiros
 */
export function useFinancialPercentages(data: {
  currentMonth: { income: number; expenses: number; savings: number };
  previousMonth: { income: number; expenses: number; savings: number };
  currentYear: { income: number; expenses: number; savings: number };
  previousYear: { income: number; expenses: number; savings: number };
  annualGoal?: number;
  monthlyData?: number[];
}) {
  return useMemo(() => {
    const comparisons = calculateFinancialComparisons(data);
    
    const annualProgress = data.annualGoal ? 
      calculateProgressPercentage(data.currentYear.savings, data.annualGoal) : 0;
    
    const averageGrowthRate = data.monthlyData ? 
      calculateAverageGrowthRate(data.monthlyData) : 0;
    
    return {
      monthly: comparisons.monthly,
      yearly: comparisons.yearly,
      annualProgress,
      averageGrowthRate,
      // Função helper para obter percentual formatado
      getFormattedPercentage: (result: PercentageResult) => {
        return `${result.percentage >= 0 ? '+' : ''}${result.percentage.toFixed(1)}%`;
      }
    };
  }, [data]);
}

/**
 * Hook para cálculos de percentual baseado em dados de tarefas
 */
export function useTaskPercentages(data: {
  currentWeek: { completed: number; total: number };
  previousWeek: { completed: number; total: number };
  currentMonth: { completed: number; total: number };
  previousMonth: { completed: number; total: number };
  weeklyData?: number[];
}) {
  return useMemo(() => {
    const comparisons = calculateTaskComparisons(data);
    
    const currentWeekProgress = data.currentWeek.total > 0 ? 
      (data.currentWeek.completed / data.currentWeek.total) * 100 : 0;
    
    const currentMonthProgress = data.currentMonth.total > 0 ? 
      (data.currentMonth.completed / data.currentMonth.total) * 100 : 0;
    
    const averageWeeklyCompletion = data.weeklyData ? 
      calculateAverageGrowthRate(data.weeklyData) : 0;
    
    return {
      weekly: comparisons.weekly,
      monthly: comparisons.monthly,
      currentWeekProgress,
      currentMonthProgress,
      averageWeeklyCompletion,
      // Função helper para determinar se performance está melhorando
      isImproving: () => {
        return comparisons.weekly.completionRate.trend === 'up' ||
               comparisons.monthly.completionRate.trend === 'up';
      }
    };
  }, [data]);
}

/**
 * Hook para cálculos de distribuição de categorias
 */
export function useCategoryDistribution(categories: { name: string; value: number }[]) {
  return useMemo(() => {
    const values = categories.map(cat => cat.value);
    const percentages = calculateDistributionPercentages(values);
    
    return categories.map((category, index) => ({
      ...category,
      percentage: percentages[index],
      isSignificant: percentages[index] >= 5 // Considera significativo se >= 5%
    }));
  }, [categories]);
}

/**
 * Hook para cálculos de progresso de meta
 */
export function useGoalProgress(current: number, target: number) {
  return useMemo(() => {
    const percentage = calculateProgressPercentage(current, target);
    const remaining = Math.max(target - current, 0);
    const isOnTrack = percentage >= 75; // Considera no caminho certo se >= 75%
    
    return {
      percentage,
      remaining,
      isOnTrack,
      status: percentage >= 100 ? 'completed' : 
              percentage >= 75 ? 'on-track' : 
              percentage >= 50 ? 'moderate' : 'behind'
    };
  }, [current, target]);
}

/**
 * Hook para comparações gerais de período
 */
export function usePeriodComparison(
  currentPeriod: number,
  previousPeriod: number,
  label: string = 'Período'
) {
  return useMemo(() => {
    const comparison = calculatePercentageChange(currentPeriod, previousPeriod);
    
    return {
      ...comparison,
      label,
      isPositive: comparison.percentage > 0,
      isNegative: comparison.percentage < 0,
      isStable: comparison.percentage === 0,
      formattedPercentage: `${comparison.percentage >= 0 ? '+' : ''}${comparison.percentage.toFixed(1)}%`,
      formattedDifference: comparison.difference >= 0 ? 
        `+${comparison.difference.toFixed(2)}` : 
        comparison.difference.toFixed(2)
    };
  }, [currentPeriod, previousPeriod, label]);
}

/**
 * Hook para análise de tendências de múltiplos períodos
 */
export function useTrendAnalysis(data: number[], labels?: string[]) {
  return useMemo(() => {
    if (data.length < 2) {
      return {
        trend: 'stable' as const,
        averageGrowth: 0,
        isConsistent: false,
        periods: []
      };
    }
    
    const periods = [];
    let positiveChanges = 0;
    let negativeChanges = 0;
    
    for (let i = 1; i < data.length; i++) {
      const change = calculatePercentageChange(data[i], data[i - 1]);
      periods.push({
        value: data[i],
        change,
        label: labels ? labels[i] : `Período ${i + 1}`
      });
      
      if (change.percentage > 0) positiveChanges++;
      else if (change.percentage < 0) negativeChanges++;
    }
    
    const averageGrowth = calculateAverageGrowthRate(data);
    const isConsistent = Math.abs(positiveChanges - negativeChanges) >= data.length * 0.6;
    
    const trend = averageGrowth > 5 ? 'up' : 
                  averageGrowth < -5 ? 'down' : 'stable';
    
    return {
      trend,
      averageGrowth,
      isConsistent,
      periods,
      summary: {
        positiveChanges,
        negativeChanges,
        stableChanges: data.length - 1 - positiveChanges - negativeChanges
      }
    };
  }, [data, labels]);
}

/**
 * Hook para cálculos de KPIs (Key Performance Indicators)
 */
export function useKPICalculations(data: {
  revenue: { current: number; previous: number; target: number };
  expenses: { current: number; previous: number; target: number };
  profit: { current: number; previous: number; target: number };
  efficiency: { current: number; previous: number; target: number };
}) {
  return useMemo(() => {
    const revenue = {
      change: calculatePercentageChange(data.revenue.current, data.revenue.previous),
      progress: calculateProgressPercentage(data.revenue.current, data.revenue.target)
    };
    
    const expenses = {
      change: calculatePercentageChange(data.expenses.current, data.expenses.previous),
      progress: calculateProgressPercentage(data.expenses.current, data.expenses.target)
    };
    
    const profit = {
      change: calculatePercentageChange(data.profit.current, data.profit.previous),
      progress: calculateProgressPercentage(data.profit.current, data.profit.target)
    };
    
    const efficiency = {
      change: calculatePercentageChange(data.efficiency.current, data.efficiency.previous),
      progress: calculateProgressPercentage(data.efficiency.current, data.efficiency.target)
    };
    
    return {
      revenue,
      expenses,
      profit,
      efficiency,
      overall: {
        improving: [revenue, profit, efficiency].filter(kpi => kpi.change.trend === 'up').length,
        declining: [revenue, profit, efficiency].filter(kpi => kpi.change.trend === 'down').length,
        stable: [revenue, profit, efficiency].filter(kpi => kpi.change.trend === 'stable').length
      }
    };
  }, [data]);
}

/**
 * Hook para substituir percentuais hardcoded por cálculos dinâmicos
 */
export function useReplacedPercentages(
  realData: any,
  fallbackPercentages: number[] = [91, 98, 105, 112]
) {
  return useMemo(() => {
    // Se não há dados reais, usar valores de fallback
    if (!realData || Object.keys(realData).length === 0) {
      return fallbackPercentages.map(percentage => ({
        value: percentage,
        isReal: false,
        trend: 'stable' as const,
        formatted: `${percentage}%`
      }));
    }
    
    // Calcular percentuais baseados em dados reais
    // Este é um exemplo genérico - você deve adaptar conforme seus dados
    const calculatedPercentages = [];
    
    // Exemplo de cálculo baseado em dados reais
    if (realData.financial) {
      const income = realData.financial.income || 0;
      const expenses = realData.financial.expenses || 0;
      const savings = realData.financial.savings || 0;
      const total = income + expenses + savings;
      
      if (total > 0) {
        calculatedPercentages.push({
          value: (income / total) * 100,
          isReal: true,
          trend: 'up' as const,
          formatted: `${((income / total) * 100).toFixed(1)}%`
        });
        
        calculatedPercentages.push({
          value: (expenses / total) * 100,
          isReal: true,
          trend: 'down' as const,
          formatted: `${((expenses / total) * 100).toFixed(1)}%`
        });
        
        calculatedPercentages.push({
          value: (savings / total) * 100,
          isReal: true,
          trend: 'up' as const,
          formatted: `${((savings / total) * 100).toFixed(1)}%`
        });
      }
    }
    
    // Preencher com valores de fallback se necessário
    while (calculatedPercentages.length < fallbackPercentages.length) {
      const index = calculatedPercentages.length;
      calculatedPercentages.push({
        value: fallbackPercentages[index],
        isReal: false,
        trend: 'stable' as const,
        formatted: `${fallbackPercentages[index]}%`
      });
    }
    
    return calculatedPercentages;
  }, [realData, fallbackPercentages]);
}
