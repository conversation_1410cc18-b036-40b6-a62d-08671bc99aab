import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import { createQueryOptions, createMutationOptions, parseApiError } from '../lib/query-utils';
import { toast } from '../utils/toast';
import { useAuth } from '../contexts/AuthContext';

// Tipos do perfil
export interface ProfileData {
  name: string;
  email: string;
  phone: string;
  timezone: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface AssistantSettings {
  ai_humor: string;
  response_size: string;
  reminder_time: string;
  reminder_interval: string;
}

export interface UpdateAssistantSettingsData {
  ai_humor: string;
  response_size: string;
  reminder_time: string;
  reminder_interval: string;
}

// Hook para buscar dados do perfil
export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: async (): Promise<ProfileData> => {
      try {
        const response = await authenticatedApi.get('profile');
        return await response.json();
      } catch (error) {
        throw parseApiError(error);
      }
    },
    ...createQueryOptions({
      staleTime: 10 * 60 * 1000, // 10 minutos
      gcTime: 15 * 60 * 1000,
      refetchOnWindowFocus: false,
    }),
  });
};

// Hook para atualizar informações pessoais
export const useUpdatePersonalInfo = () => {
  const queryClient = useQueryClient();
  const { updateUser } = useAuth();

  return useMutation({
    mutationFn: async (data: Partial<ProfileData>): Promise<void> => {
      try {
        await authenticatedApi.put('profile/personal-info', { json: data });
      } catch (error) {
        throw parseApiError(error);
      }
    },
    onSuccess: (_, variables) => {
      // Invalidar cache do perfil
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      // Atualizar dados do usuário no AuthContext (especialmente o nome para o dropdown)
      updateUser(variables);
      toast.success('Informações atualizadas com sucesso!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atualizar informações');
    },
    ...createMutationOptions(),
  });
};

// Hook para alterar senha
export const useChangePassword = () => {
  return useMutation({
    mutationFn: async (data: ChangePasswordData): Promise<void> => {
      try {
        await authenticatedApi.post('profile/change-password', { json: data });
      } catch (error) {
        throw parseApiError(error);
      }
    },
    onSuccess: () => {
      toast.success('Senha alterada com sucesso!');
    },
    onError: (error: any) => {
      let errorMessage = 'Erro ao alterar senha';
      
      if (error.status === 400) {
        errorMessage = 'Senha atual incorreta ou dados inválidos';
      } else if (error.status === 401) {
        errorMessage = 'Senha atual incorreta';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
    },
    ...createMutationOptions(),
  });
};

// Hook para buscar configurações do assistente
export const useAssistantSettings = () => {
  return useQuery({
    queryKey: ['assistant-settings'],
    queryFn: async (): Promise<AssistantSettings> => {
      try {
        const response = await authenticatedApi.get('profile/assistant-settings');
        return await response.json();
      } catch (error) {
        throw parseApiError(error);
      }
    },
    ...createQueryOptions({
      staleTime: 15 * 60 * 1000, // 15 minutos - configurações mudam menos
      gcTime: 20 * 60 * 1000,
      refetchOnWindowFocus: false,
    }),
  });
};

// Hook para atualizar configurações do assistente
export const useUpdateAssistantSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateAssistantSettingsData): Promise<void> => {
      try {
        await authenticatedApi.put('profile/assistant-settings', { json: data });
      } catch (error) {
        throw parseApiError(error);
      }
    },
    onSuccess: () => {
      // Invalidar cache das configurações do assistente
      queryClient.invalidateQueries({ queryKey: ['assistant-settings'] });
      toast.success('Configurações do assistente atualizadas com sucesso!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erro ao atualizar configurações do assistente');
    },
    ...createMutationOptions(),
  });
};