import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import { createQueryOptions, createMutationOptions, parseApiError } from '../lib/query-utils';
import {
  TaskListResponseDto,
  TaskResponseDto,
  CreateTaskDto,
  UpdateTaskDto,
  TaskCategoryResponseDto,
  CreateTaskCategoryDto,
  UpdateTaskCategoryDto,
  PaginationParams,
} from '../types/api';

// Hook para listar tasks
export const useTasks = (params?: PaginationParams) => {
  return useQuery({
    queryKey: ['tasks', params],
    queryFn: async (): Promise<TaskListResponseDto> => {
      try {
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.start_date) searchParams.append('start_date', params.start_date);
        if (params?.end_date) searchParams.append('end_date', params.end_date);

        const response = await authenticatedApi.get(`tasks?${searchParams.toString()}`);
        const responseData = await response.json() as TaskListResponseDto;

        return responseData;
      } catch (error) {
        throw parseApiError(error);
      }
    },
    ...createQueryOptions({
      staleTime: 5 * 60 * 1000, // 5 minutos - tasks mudam frequentemente
      gcTime: 10 * 60 * 1000, // 10 minutos
      refetchOnWindowFocus: false,
    }),
  });
};

// Hook para buscar uma task específica
export const useTask = (id: number) => {
  return useQuery({
    queryKey: ['tasks', id],
    queryFn: async (): Promise<TaskResponseDto> => {
      try {
        const response = await authenticatedApi.get(`tasks/${id}`);
        return response.json();
      } catch (error) {
        throw parseApiError(error);
      }
    },
    enabled: !!id,
    ...createQueryOptions(),
  });
};

// Hook para criar task
export const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTaskDto): Promise<TaskResponseDto> => {
      try {
        const response = await authenticatedApi.post('tasks', { json: data });
        return response.json();
      } catch (error) {
        throw parseApiError(error);
      }
    },
    onSuccess: () => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-simple'] });
    },
    ...createMutationOptions(),
  });
};

// Hook para atualizar task
export const useUpdateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateTaskDto }): Promise<TaskResponseDto> => {
      const response = await authenticatedApi.put(`tasks/${id}`, { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'], exact: false });
      queryClient.invalidateQueries({ queryKey: ['dashboard'], exact: false });
      // Atualizar cache da task específica
      queryClient.setQueryData(['tasks', data.id], data);
    },
  });
};

// Hook para deletar task
export const useDeleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`tasks/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para marcar task como completa
export const useCompleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<TaskResponseDto> => {
      const response = await authenticatedApi.patch(`tasks/${id}/complete`);
      return response.json();
    },
    // Optimistic update to reduce flickering
    onMutate: async (taskId: number) => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ['tasks'] });

      // Snapshot the previous values for all possible query keys
      const previousTasksWithoutParams = queryClient.getQueryData(['tasks', undefined]);
      const previousTasksWithParams = queryClient.getQueryData(['tasks']);

      // Update cache for both possible query key variations
      const updateCache = (queryKey: any[]) => {
        queryClient.setQueryData(queryKey, (oldData: TaskListResponseDto | undefined) => {
          if (!oldData || !oldData.tasks) {
            return oldData;
          }
          return {
            ...oldData,
            tasks: oldData.tasks.map((task) => {
              if (task.id === taskId) {
                // Toggle the completed state optimistically
                return {
                  ...task,
                  completed_at: task.completed_at ? null : new Date(),
                };
              }
              return task;
            }),
          };
        });
      };

      // Update both cache variations
      updateCache(['tasks', undefined]);
      updateCache(['tasks']);

      // Return a context object with the snapshotted values
      return {
        previousTasksWithoutParams,
        previousTasksWithParams
      };
    },
    onError: (err, taskId, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousTasksWithoutParams) {
        queryClient.setQueryData(['tasks', undefined], context.previousTasksWithoutParams);
      }
      if (context?.previousTasksWithParams) {
        queryClient.setQueryData(['tasks'], context.previousTasksWithParams);
      }
    },
    onSuccess: (data) => {
      // Update individual task cache
      queryClient.setQueryData(['tasks', data.id], data);

      // Update both possible tasks list cache variations with the server response
      const updateTasksCache = (queryKey: any[]) => {
        queryClient.setQueryData(queryKey, (oldData: TaskListResponseDto | undefined) => {
          if (!oldData || !oldData.tasks) {
            return oldData;
          }
          return {
            ...oldData,
            tasks: oldData.tasks.map((task) =>
              task.id === data.id ? data : task
            ),
          };
        });
      };

      updateTasksCache(['tasks', undefined]);
      updateTasksCache(['tasks']);

      // Invalidate dashboard cache to update task counts
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para listar categorias de tasks
export const useTaskCategories = () => {
  return useQuery({
    queryKey: ['task-categories'],
    queryFn: async (): Promise<TaskCategoryResponseDto[]> => {
      const response = await authenticatedApi.get('tasks/categories');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutos (categorias mudam menos)
  });
};

// Hook para criar categoria de task
export const useCreateTaskCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTaskCategoryDto): Promise<TaskCategoryResponseDto> => {
      const response = await authenticatedApi.post('tasks/categories', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['task-categories'] });
    },
  });
};

// Hook para atualizar categoria de task
export const useUpdateTaskCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateTaskCategoryDto }): Promise<TaskCategoryResponseDto> => {
      const response = await authenticatedApi.put(`tasks/categories/${id}`, { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['task-categories'] });
    },
  });
};

// Hook para deletar categoria de task
export const useDeleteTaskCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`tasks/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['task-categories'] });
    },
  });
};
