export const APP_NAME = 'Dupli';

export const THEME = {
  colors: {
    primary: '#B4EB00',
    secondary: '#212121',
    error: '#FF3B30',
    success: '#4CAF50',
    warning: '#F9D449',
    background: '#F7F7F7',
    card: '#FFFFFF',
    text: {
      primary: '#1C1C1C',
      secondary: '#6C6C6C',
    },
  },
  spacing: {
    page: '1rem',
    section: '1.5rem',
    card: '1rem',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
  },
  transitions: {
    default: '150ms cubic-bezier(0.4, 0, 0.2, 1)',
    smooth: '300ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
  },
} as const;

export const DATE_FORMATS = {
  shortDate: 'dd/MM/yyyy',
  longDate: "dd 'de' MMMM 'de' yyyy",
  monthYear: "MMMM 'de' yyyy",
  time: 'HH:mm',
} as const;

export const CATEGORIES = {
  tasks: [
    { name: 'Reunião', color: '#FF3B30' },
    { name: 'Compromisso', color: '#4CAF50' },
    { name: 'Tarefa', color: '#B4EB00' },
    { name: 'Responsabilidade', color: '#FF9500' },
    { name: 'Agenda', color: '#007AFF' },
    { name: 'Chamada', color: '#5856D6' }
  ],
  finances: [
    { name: 'Alimentação', color: '#B4EB00' },
    { name: 'Transporte', color: '#212121' },
    { name: 'Lazer', color: '#6C6C6C' },
    { name: 'Moradia', color: '#BBBBBB' },
    { name: 'Outros', color: '#E5E7EB' }
  ],
  ideas: [
    { name: 'App', color: '#FF3B30' },
    { name: 'Projeto pessoal', color: '#4CAF50' },
    { name: 'Estudo', color: '#B4EB00' },
    { name: 'Negócio', color: '#FF9500' },
    { name: 'Outro', color: '#6C6C6C' }
  ]
} as const;

export const MODAL_ANIMATION = {
  overlay: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  content: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 }
  }
} as const;

export const PAGE_ANIMATION = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
} as const;