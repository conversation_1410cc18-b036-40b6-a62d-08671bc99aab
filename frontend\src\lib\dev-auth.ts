import { api, tokenUtils } from './api';

interface DevAuthResponse {
  access_token: string;
  user: {
    id: number;
    email: string;
    name: string;
    timezone: string;
  };
  message: string;
}

export const devAuth = {
  /**
   * Gera um token de desenvolvimento para testes
   * Só funciona em modo de desenvolvimento
   */
  generateDevToken: async (userId?: number, email?: string): Promise<DevAuthResponse> => {
    if (import.meta.env.PROD) {
      throw new Error('Tokens de desenvolvimento não estão disponíveis em produção');
    }

    // Usar token fixo para desenvolvimento (bypass da autenticação)
    const devToken = 'dev-token-bypass';
    const userData = {
      id: 1,
      email: '<EMAIL>',
      name: 'Dev User',
      timezone: 'America/Sao_Paulo'
    };

    tokenUtils.setTokens({
      access_token: devToken,
      refresh_token: '',
      user: userData
    });

    return {
      access_token: devToken,
      user: userData,
      message: 'Token de desenvolvimento aplicado (bypass de autenticação)'
    };
  },

  /**
   * Cria ou obtém um usuário de teste
   */
  getOrCreateTestUser: async () => {
    if (import.meta.env.PROD) {
      throw new Error('Usuário de teste não está disponível em produção');
    }

    const response = await api.get('dev/test-user');
    return response.json();
  },

  /**
   * Popula o banco com dados de teste
   */
  seedTestData: async (userId?: number) => {
    if (import.meta.env.PROD) {
      throw new Error('Seed de dados não está disponível em produção');
    }

    const response = await api.post('dev/seed-data', {
      json: { userId }
    });

    return response.json();
  },

  /**
   * Configura autenticação de desenvolvimento automaticamente
   * Útil para testes e desenvolvimento
   */
  setupDevAuth: async () => {
    if (import.meta.env.PROD) {
      console.warn('Setup de desenvolvimento ignorado em produção');
      return;
    }

    try {
      console.log('Configurando autenticação de desenvolvimento...');

      // Gerar token de desenvolvimento (bypass simples)
      const authData = await devAuth.generateDevToken();
      console.log('Token de desenvolvimento gerado:', authData.message);

      // Não tentar criar dados de teste por enquanto
      console.log('Autenticação de desenvolvimento configurada com sucesso');

      return authData;
    } catch (error) {
      console.error('Erro ao configurar autenticação de desenvolvimento:', error);
      throw error;
    }
  },

  /**
   * Limpa dados de desenvolvimento
   */
  clearDevAuth: () => {
    tokenUtils.clearTokens();
    console.log('Dados de desenvolvimento limpos');
  }
};

// Adicionar função global para facilitar testes no console do navegador
if (import.meta.env.DEV) {
  (window as any).devAuth = devAuth;
  console.log('devAuth disponível globalmente para testes. Use devAuth.setupDevAuth() para configurar automaticamente.');
}
