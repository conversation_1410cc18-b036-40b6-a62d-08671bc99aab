import { QueryClient } from '@tanstack/react-query';

// Create a more robust query client with better error handling and timeout
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      gcTime: 10 * 60 * 1000, // 10 minutos (anteriormente cacheTime)
      
      // Timeout para queries - importante para evitar loading infinito
      networkMode: 'always',
      
      retry: (failureCount, error: any) => {
        // Não tentar novamente para erros de autenticação
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          return false;
        }
        
        // Não tentar novamente para erros de rede (ex: servidor down)
        if (error?.name === 'TypeError' && error?.message?.includes('fetch')) {
          return false;
        }
        
        // Não tentar novamente para timeout
        if (error?.name === 'AbortError' || error?.message?.includes('timeout')) {
          return false;
        }
        
        // Tentar até 2 vezes para outros erros de servidor (5xx)
        if (error?.response?.status >= 500) {
          return failureCount < 2;
        }
        
        // Não tentar para outros erros
        return false;
      },
      
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: false,
      networkMode: 'always',
    },
  },
});
