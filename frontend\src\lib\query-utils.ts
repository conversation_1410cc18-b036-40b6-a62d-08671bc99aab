import { UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';

// Standardized error handling for API responses
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Parse error from API response
export const parseApiError = (error: any): ApiError => {
  // Network errors
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return new ApiError(
      'Erro de conexão com o servidor. Verifique sua conexão com a internet.',
      0,
      'NETWORK_ERROR'
    );
  }

  // HTTP errors from ky
  if (error.response) {
    const status = error.response.status;
    
    switch (status) {
      case 401:
        return new ApiError(
          'Sessão expirada. Faça login novamente.',
          401,
          'UNAUTHORIZED'
        );
      case 403:
        return new ApiError(
          'Acesso negado. Você não tem permissão para acessar este recurso.',
          403,
          'FORBIDDEN'
        );
      case 404:
        return new ApiError(
          'Recurso não encontrado.',
          404,
          'NOT_FOUND'
        );
      case 422:
        return new ApiError(
          'Dados inválidos. Verifique as informações enviadas.',
          422,
          'VALIDATION_ERROR'
        );
      case 500:
        return new ApiError(
          'Erro interno do servidor. Tente novamente mais tarde.',
          500,
          'INTERNAL_ERROR'
        );
      default:
        return new ApiError(
          `Erro do servidor (${status}). Tente novamente mais tarde.`,
          status,
          'HTTP_ERROR'
        );
    }
  }

  // Generic error
  return new ApiError(
    error.message || 'Erro desconhecido',
    undefined,
    'UNKNOWN_ERROR',
    error
  );
};

// Standardized retry logic
export const createRetryFn = (maxRetries = 2) => {
  return (failureCount: number, error: any) => {
    const apiError = parseApiError(error);
    
    // Don't retry authentication errors
    if (apiError.status === 401 || apiError.status === 403) {
      return false;
    }
    
    // Don't retry client errors (4xx except 401, 403)
    if (apiError.status && apiError.status >= 400 && apiError.status < 500) {
      return false;
    }
    
    // Retry network errors and server errors
    return failureCount < maxRetries;
  };
};

// Standardized query options
export const createQueryOptions = <T>(
  options: Partial<UseQueryOptions<T, any, T, any>> = {}
): Partial<UseQueryOptions<T, any, T, any>> => ({
  staleTime: 10 * 60 * 1000, // 10 minutes - otimizado conforme especificações
  gcTime: 15 * 60 * 1000, // 15 minutes - cache apropriado
  refetchOnWindowFocus: false, // evita re-fetch desnecessário
  retry: createRetryFn(),
  ...options,
});

// Standardized mutation options
export const createMutationOptions = <TData, TError, TVariables>(
  options: Partial<UseMutationOptions<TData, TError, TVariables>> = {}
): Partial<UseMutationOptions<TData, TError, TVariables>> => ({
  retry: false,
  ...options,
});

// Helper to handle loading states consistently
export const createLoadingState = (isLoading: boolean, error: any) => {
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      isEmpty: false,
    };
  }

  if (error) {
    return {
      isLoading: false,
      error: parseApiError(error),
      isEmpty: false,
    };
  }

  return {
    isLoading: false,
    error: null,
    isEmpty: false,
  };
};

// Helper to check if data is empty
export const isEmpty = (data: any): boolean => {
  if (!data) return true;
  if (Array.isArray(data)) return data.length === 0;
  if (typeof data === 'object') return Object.keys(data).length === 0;
  return false;
};

// Standardized error messages for common scenarios
export const ERROR_MESSAGES = {
  NETWORK: 'Erro de conexão. Verifique sua internet.',
  UNAUTHORIZED: 'Sessão expirada. Faça login novamente.',
  FORBIDDEN: 'Acesso negado.',
  NOT_FOUND: 'Recurso não encontrado.',
  VALIDATION: 'Dados inválidos.',
  SERVER: 'Erro do servidor. Tente novamente.',
  UNKNOWN: 'Erro desconhecido.',
} as const;
