import React, { useState } from 'react';
import { ArrowLeft, Plus, TrendingUp, Target, X, Check, Search, Filter, ChevronDown, ChevronUp, Calendar, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import MonthYearSelector from '../components/MonthYearSelector';
import CurrencyInput from '../components/CurrencyInput';
import { useFinances, useCreateFinance, useFinanceCategories, useDeleteFinance } from '../hooks/useFinances';
import { useAnnualSavingsGoal } from '../hooks/useConfig';
import { CreateFinanceDto } from '../types/api';

interface SavingEntry {
  id: string;
  amount: number;
  purpose: string;
  date: string;
}

interface SavingTransaction {
  id: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  purpose: string;
  date: string;
  balanceAfter: number;
}

interface MonthlyGoal {
  target: number;
  current: number;
}

// Dados do gráfico são calculados a partir da API (dados reais)

const motivationalMessages = [
  "Bora guardar mais hoje?",
  "Pequenas economias constroem grandes liberdades",
  "Cada real guardado é um passo rumo aos seus sonhos",
  "Sua disciplina de hoje é a sua segurança de amanhã",
  "Economizar é investir no seu futuro",
  "Grandes conquistas começam com pequenas economias"
];

const CofrinhoPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentMessage] = useState(() =>
    motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)]
  );
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [transactionType, setTransactionType] = useState<'deposit' | 'withdrawal'>('deposit');
  const [newSaving, setNewSaving] = useState({
    amount: 0,
    amountString: '',
    description: ''
  });
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Transaction history state
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    type: '',
    startDate: '',
    endDate: '',
  });

  // Fetch data from API
  const { data: financesData, isLoading, error } = useFinances();
  const { data: categoriesData } = useFinanceCategories();
  const { data: annualGoalData, isLoading: isLoadingGoal } = useAnnualSavingsGoal();
  const createFinanceMutation = useCreateFinance();
  const deleteFinanceMutation = useDeleteFinance();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando economias...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar suas economias. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Filter savings (transactions marked as savings)
  const savings = financesData?.finances.filter(finance => finance.is_saving) || [];
  const savingsCategories = categoriesData?.filter(cat => cat.transaction_type === 'expense') || [];

  // Generate 12-month chart data from real savings (deposits - withdrawals)
  const generateChartData = () => {
    const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
    const currentYear = new Date().getFullYear();

    return months.map((month, index) => {
      const monthSavings = savings
        .filter(saving => {
          const savingDate = new Date(saving.transaction_date);
          return savingDate.getMonth() === index && savingDate.getFullYear() === currentYear;
        })
        .reduce((sum, saving) => {
          // Depósitos (expense) são positivos, retiradas (income) são negativas
          const amount = parseFloat(saving.amount);
          return saving.transaction_type === 'expense' ? sum + amount : sum - amount;
        }, 0);

      return { month, amount: monthSavings };
    });
  };

  const chartData = generateChartData();

  // Calculate current month savings (deposits - withdrawals)
  const currentMonth = selectedDate.getMonth();
  const currentYear = selectedDate.getFullYear();
  const currentMonthSavings = savings
    .filter(saving => {
      const savingDate = new Date(saving.transaction_date);
      return savingDate.getMonth() === currentMonth && savingDate.getFullYear() === currentYear;
    })
    .reduce((sum, saving) => {
      // Depósitos (expense) são positivos, retiradas (income) são negativas
      const amount = parseFloat(saving.amount);
      return saving.transaction_type === 'expense' ? sum + amount : sum - amount;
    }, 0);

  // Calculate total savings balance (deposits - withdrawals)
  const totalSavings = savings.reduce((sum, saving) => {
    const amount = parseFloat(saving.amount);
    return saving.transaction_type === 'expense' ? sum + amount : sum - amount;
  }, 0);
  const averageSavings = savings.length > 0 ? totalSavings / 12 : 0; // Assuming 12 months

  // Calculate monthly goal from annual goal
  const monthlyGoalTarget = annualGoalData ? parseFloat(annualGoalData.amount) / 12 : 0;
  const hasAnnualGoal = !!annualGoalData;
  const goalProgress = monthlyGoalTarget > 0 ? (currentMonthSavings / monthlyGoalTarget) * 100 : 0;

  // Filter transactions
  const filteredTransactions = savings.filter(saving => {
    const matchesSearch = (saving.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !filters.type || 
      (filters.type === 'deposit' && saving.transaction_type === 'expense') ||
      (filters.type === 'withdrawal' && saving.transaction_type === 'income');
    const savingDate = new Date(saving.transaction_date).toISOString().split('T')[0];
    const matchesDateRange = (!filters.startDate || savingDate >= filters.startDate) &&
                           (!filters.endDate || savingDate <= filters.endDate);

    return matchesSearch && matchesType && matchesDateRange;
  });

  const handleAddSaving = async () => {
    if (newSaving.amount <= 0) return;

    try {
      const savingData: CreateFinanceDto = {
        transaction_type: transactionType === 'deposit' ? 'expense' : 'income',
        description: `${transactionType === 'deposit' ? 'Depósito' : 'Retirada'} - ${newSaving.description || 'Cofrinho'}`,
        amount: newSaving.amountString,
        is_saving: true,
        transaction_date: new Date().toISOString()
      };

      await createFinanceMutation.mutateAsync(savingData);
      setNewSaving({ amount: 0, amountString: '', description: '' });
      setTransactionType('deposit');
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Erro ao processar transação do cofrinho:', error);
    }
  };

  const toggleExpand = (id: number) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const clearFilters = () => {
    setFilters({
      type: '',
      startDate: '',
      endDate: '',
    });
  };

  const handleEdit = (id: number, updates: any) => {
    // Implementar edição se necessário
    // Edit saving functionality
    setExpandedId(null);
  };

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja excluir esta transação?')) {
      try {
        await deleteFinanceMutation.mutateAsync(id);
        setExpandedId(null); // Fechar o painel expandido após exclusão
      } catch (error) {
        console.error('Erro ao excluir transação:', error);
        alert('Erro ao excluir transação. Tente novamente.');
      }
    }
  };

  const handleNewCofrinho = () => {
    setIsAddModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      {/* Header Padronizado Global */}
      <div className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-50">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          {/* Botão Voltar */}
          <button 
            onClick={() => navigate('/finances')}
            className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>

          {/* Título Centralizado */}
          <h1 className="text-xl font-bold text-gray-900">Cofrinho</h1>

          {/* Botão + com redirecionamento para novo cofrinho */}
          <button
            onClick={handleNewCofrinho}
            className="w-10 h-10 bg-[#212121] rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
          >
            <span className="text-white text-xl font-light">+</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 pt-20 space-y-6">
        {/* Motivational Message */}
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {currentMessage}
          </h2>
          <p className="text-gray-600 text-lg">
            Continue construindo seu futuro financeiro
          </p>
        </div>

        {/* Card Principal com Seletor de Data Integrado */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              Economizado este mês
            </h2>
            
            {/* Seletor de Data Discreto */}
            <div className="flex justify-center mb-4">
              <MonthYearSelector
                selectedDate={selectedDate}
                onDateChange={setSelectedDate}
              />
            </div>
            
            {/* Valor Total */}
            <p className="text-4xl font-bold text-[#4CAF50] mb-4">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(currentMonthSavings)}
            </p>
            
            {/* Métricas */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Total acumulado</p>
                <p className="font-semibold text-gray-900">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(totalSavings)}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Média mensal</p>
                <p className="font-semibold text-gray-900">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(averageSavings)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Monthly Goal */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
              <Target size={20} className="text-[#B4EB00]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Meta Mensal</h3>
          </div>

          {hasAnnualGoal ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Progresso</span>
                <span className="font-semibold text-gray-900">
                  {Math.round(goalProgress)}%
                </span>
              </div>

              <div className="w-full bg-gray-100 rounded-full h-3 overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min(goalProgress, 100)}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                  className="h-full bg-gradient-to-r from-[#B4EB00] to-[#9FD700] rounded-full"
                />
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-600">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(currentMonthSavings)}
                </span>
                <span className="text-gray-600">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(monthlyGoalTarget)}
                </span>
              </div>

              <div className="text-center pt-2">
                <p className="text-xs text-gray-500">
                  Meta anual: {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(parseFloat(annualGoalData?.amount || '0'))}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600 mb-4">
                Defina sua meta anual de economia para acompanhar seu progresso mensal
              </p>
              <button
                onClick={() => navigate('/finances/annual')}
                className="px-6 py-3 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors font-medium"
              >
                Definir Meta Anual
              </button>
            </div>
          )}
        </div>

        {/* 12-Month History Chart */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
              <TrendingUp size={20} className="text-[#B4EB00]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Histórico de 12 Meses</h3>
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                <XAxis 
                  dataKey="month" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6C6C6C' }}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6C6C6C' }}
                  tickFormatter={(value) => 
                    new Intl.NumberFormat('pt-BR', {
                      notation: 'compact',
                      compactDisplay: 'short'
                    }).format(value)
                  }
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: 'none',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0,0,0,0.1)',
                    padding: '8px'
                  }}
                  formatter={(value: number) => [
                    new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(value),
                    'Economizado'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="amount" 
                  stroke="#B4EB00" 
                  strokeWidth={3}
                  dot={{ fill: '#B4EB00', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#B4EB00', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Transaction History */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
              <Calendar size={20} className="text-[#B4EB00]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Histórico de Transações</h3>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar transação..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
              />
            </div>
            <button 
              onClick={() => setIsFilterOpen(true)}
              className="h-12 px-6 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
            >
              <Filter size={20} className="text-gray-600" />
              <span>Filtros</span>
              {(filters.type || filters.startDate || filters.endDate) && (
                <span className="w-2 h-2 rounded-full bg-[#B4EB00]" />
              )}
            </button>
          </div>

          {/* Transaction List */}
          <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
            {filteredTransactions.map(transaction => (
              <div
                key={transaction.id}
                className="overflow-hidden border border-gray-100 rounded-xl hover:border-[#B4EB00] transition-colors"
              >
                <div className="p-4 flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        transaction.transaction_type === 'expense' 
                          ? 'bg-[#4CAF50]/10 text-[#4CAF50]' 
                          : 'bg-[#FF6B6B]/10 text-[#FF6B6B]'
                      }`}>
                        {transaction.transaction_type === 'expense' ? 'Depósito' : 'Retirada'}
                      </span>
                      <h3 className="font-medium text-[#1C1C1C]">{transaction.description || 'Cofrinho'}</h3>
                    </div>
                    <div className="flex items-center gap-4 mt-1">
                      <span className="text-sm text-[#6C6C6C]">
                        {new Date(transaction.transaction_date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}
                      </span>
                      {transaction.category_name && (
                        <span className="text-sm text-[#6C6C6C]">
                          {transaction.category_name}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className={`font-medium ${
                      transaction.transaction_type === 'expense' 
                        ? 'text-[#4CAF50]' 
                        : 'text-[#FF6B6B]'
                    }`}>
                      {transaction.transaction_type === 'expense' ? '+' : '-'}{new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(parseFloat(transaction.amount))}
                    </span>
                    <button
                      onClick={() => toggleExpand(transaction.id)}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      {expandedId === transaction.id ? (
                        <ChevronUp size={20} className="text-gray-600" />
                      ) : (
                        <ChevronDown size={20} className="text-gray-600" />
                      )}
                    </button>
                  </div>
                </div>

                <AnimatePresence>
                  {expandedId === transaction.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="border-t border-gray-100"
                    >
                      <div className="p-4 space-y-4">
                        <div>
                          <label className="block text-sm text-gray-600 mb-1">Valor</label>
                          <input
                            type="number"
                            value={parseFloat(transaction.amount)}
                            onChange={(e) => handleEdit(transaction.id, { amount: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                            step="0.01"
                          />
                        </div>

                        <div>
                          <label className="block text-sm text-gray-600 mb-1">Descrição</label>
                          <input
                            type="text"
                            value={transaction.description || ''}
                            onChange={(e) => handleEdit(transaction.id, { description: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                          />
                        </div>

                        <div>
                          <label className="block text-sm text-gray-600 mb-1">Data</label>
                          <input
                            type="date"
                            value={new Date(transaction.transaction_date).toISOString().split('T')[0]}
                            onChange={(e) => handleEdit(transaction.id, { transaction_date: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                          />
                        </div>

                        <div className="flex justify-end gap-2 pt-2">
                          <button
                            onClick={() => handleDelete(transaction.id)}
                            disabled={deleteFinanceMutation.isPending}
                            className="px-4 py-2 text-[#FF3B30] hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                          >
                            {deleteFinanceMutation.isPending ? (
                              <>
                                <div className="w-4 h-4 border-2 border-[#FF3B30] border-t-transparent rounded-full animate-spin" />
                                Excluindo...
                              </>
                            ) : (
                              'Excluir'
                            )}
                          </button>
                          <button
                            onClick={() => setExpandedId(null)}
                            className="px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                          >
                            Salvar
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Action Button */}
        <div className="text-center">
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-[#B4EB00] to-[#9FD700] text-gray-900 rounded-2xl font-semibold hover:shadow-lg transition-all"
          >
            <Plus size={24} />
            <span>Gerenciar Cofrinho</span>
          </button>
        </div>
      </div>

      {/* Filter Modal */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
              onClick={() => setIsFilterOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
            >
              <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-gray-900">Filtros</h2>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X size={20} className="text-gray-600" />
                  </button>
                </div>

                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tipo de operação
                    </label>
                    <select
                      value={filters.type}
                      onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="">Todos os tipos</option>
                      <option value="deposit">Depósitos</option>
                      <option value="withdrawal">Retiradas</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data inicial
                    </label>
                    <input
                      type="date"
                      value={filters.startDate}
                      onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data final
                    </label>
                    <input
                      type="date"
                      value={filters.endDate}
                      onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                  <button
                    onClick={clearFilters}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Limpar
                  </button>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="flex-1 px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                  >
                    Aplicar
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Add Saving Modal */}
      <AnimatePresence>
        {isAddModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
              onClick={() => setIsAddModalOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
              className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
            >
              <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {transactionType === 'deposit' ? 'Novo Depósito' : 'Nova Retirada'}
                  </h2>
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X size={20} className="text-gray-600" />
                  </button>
                </div>

                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  {/* Tipo de Transação */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tipo de Operação
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        type="button"
                        onClick={() => setTransactionType('deposit')}
                        className={`px-4 py-3 rounded-xl border-2 transition-all ${
                          transactionType === 'deposit'
                            ? 'border-[#B4EB00] bg-[#B4EB00]/10 text-gray-900'
                            : 'border-gray-200 bg-white text-gray-600 hover:border-gray-300'
                        }`}
                      >
                        💰 Depósito
                      </button>
                      <button
                        type="button"
                        onClick={() => setTransactionType('withdrawal')}
                        className={`px-4 py-3 rounded-xl border-2 transition-all ${
                          transactionType === 'withdrawal'
                            ? 'border-[#FF6B6B] bg-[#FF6B6B]/10 text-gray-900'
                            : 'border-gray-200 bg-white text-gray-600 hover:border-gray-300'
                        }`}
                      >
                        💸 Retirada
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Valor (R$)
                    </label>
                    <CurrencyInput
                      value={newSaving.amount}
                      onChange={(value, decimalString) => setNewSaving({ ...newSaving, amount: value, amountString: decimalString })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent text-lg"
                      placeholder="0,00"
                      autoFocus
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Descrição (opcional)
                    </label>
                    <input
                      type="text"
                      value={newSaving.description}
                      onChange={(e) => setNewSaving({ ...newSaving, description: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                      placeholder="Ex: Reserva de emergência"
                    />
                  </div>


                </div>

                <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleAddSaving}
                    disabled={createFinanceMutation.isPending || newSaving.amount <= 0}
                    className="flex-1 px-4 py-3 bg-gradient-to-r from-[#B4EB00] to-[#9FD700] text-gray-900 rounded-xl font-medium hover:shadow-md transition-all flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Check size={20} />
                    {createFinanceMutation.isPending ? 'Processando...' : 
                     transactionType === 'deposit' ? 'Depositar' : 'Retirar'}
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CofrinhoPage;