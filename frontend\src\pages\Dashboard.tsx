import React, { useState } from 'react';
import { Eye, CheckCircle, DollarSign, Lightbulb, PiggyBank, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useDashboardData } from '../hooks/useDashboard';
import AnnualGoalModal from '../components/AnnualGoalModal';
import ProgressBar from '../components/ProgressBar';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [isAnnualGoalModalOpen, setIsAnnualGoalModalOpen] = useState(false);
  const { data, isLoading, error } = useDashboardData();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dashboard</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados do dashboard. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
  );
};
  // Se não há dados, não renderizar
  if (!data) {
    return (
      <>
        <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Carregando dados...</p>
          </div>
        </div>
        {/* Annual Goal Modal */}
        <AnnualGoalModal
          isOpen={isAnnualGoalModalOpen}
          onClose={() => setIsAnnualGoalModalOpen(false)}
        />
      </>
    );
  }

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return '☀️ Bom dia';
    if (hour < 18) return '🌤️ Boa tarde';
    return '🌙 Boa noite';
  };

  const cards = [
    {
      title: 'Compromissos',
      icon: <CheckCircle size={24} className="text-white" />,
      iconBg: 'bg-gray-900',
      content: null,
      route: '/tasks',
      bg: 'bg-white'
    },
    {
      title: 'Finanças',
      icon: <DollarSign size={24} className="text-gray-900" />,
      iconBg: 'bg-[#B4EB00]',
      content: null,
      route: '/finances',
      bg: 'bg-[#B4EB00]'
    },
    {
      title: 'Ideias',
      icon: <Lightbulb size={24} className="text-white" />,
      iconBg: 'bg-gray-900',
      content: null,
      route: '/ideas',
      bg: 'bg-[#212121]'
    }
  ];

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        <div className="pt-8 pb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            {getGreeting()}, {data.user?.name || 'Usuário'}!
          </h1>
          <p className="text-gray-600">
            Vamos deixar tudo organizado para hoje?
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {cards.map((card, index) => (
            <div
              key={card.title}
              onClick={() => navigate(card.route)}
              className={`${card.bg} rounded-2xl p-6 shadow-sm cursor-pointer hover:shadow-md transition-all`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${card.iconBg}`}>
                    {card.icon}
                  </div>
                  <h2 className={`text-lg font-medium ${
                    card.bg === 'bg-[#212121]' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {card.title}
                  </h2>
                </div>
                <div className="flex flex-col items-center">
                  <Eye size={18} className={card.bg === 'bg-[#212121]' ? 'text-white' : 'text-gray-600'} />
                  <span className={`text-xs mt-0.5 ${
                    card.bg === 'bg-[#212121]' ? 'text-white' : 'text-gray-600'
                  }`}>
                    ver
                  </span>
                </div>
              </div>
              {card.content}
            </div>
          ))}
        </div>

        <div
          onClick={() => navigate('/progresso-mensal')}
          className="mt-6 bg-white rounded-2xl p-6 shadow-sm cursor-pointer hover:shadow-md transition-all"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-medium text-gray-900">
              Seu progresso esse mês
            </h2>
            <Eye size={18} className="text-gray-600" />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <p className="text-gray-600 text-sm mb-2">
                {data.monthlyProgress?.appointments?.completed || 0} de {data.monthlyProgress?.appointments?.total || 0} compromissos concluídos
              </p>
              <ProgressBar
                percentage={((data.monthlyProgress?.appointments?.completed || 0) / (data.monthlyProgress?.appointments?.total || 1)) * 100}
                color="#1ED760"
                bgColor="#E5E7EB"
                height={6}
              />
            </div>

            <div className="flex lg:flex-col justify-between gap-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <PiggyBank size={20} className="text-gray-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Economizados</p>
                  <p className="font-medium text-gray-900">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(data.monthlyProgress?.savings || 0)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <Lightbulb size={20} className="text-gray-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Ideias salvas</p>
                  <p className="font-medium text-gray-900">
                    {data.monthlyProgress?.ideas || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Annual Goal Modal */}
      <AnnualGoalModal
        isOpen={isAnnualGoalModalOpen}
        onClose={() => setIsAnnualGoalModalOpen(false)}
      />
    </div>
  );
};

export default Dashboard;
