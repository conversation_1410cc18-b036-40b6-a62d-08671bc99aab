import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Star, Trash2, Share2, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { formatDate } from '@/lib/utils';
import { useIdea, useUpdateIdea, useDeleteIdea } from '../hooks/useIdeas';

interface Idea {
  id: string;
  title: string;
  description: string;
  content?: string;
  date: string;
  isFavorite: boolean;
  category: string;
}

const IdeaDetailPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const ideaId = id ? parseInt(id) : 0;
  
  // Use real API hooks
  const { data: idea, isLoading, error } = useIdea(ideaId);
  const updateIdeaMutation = useUpdateIdea();
  const deleteIdeaMutation = useDeleteIdea();
  
  const handleToggleFavorite = async () => {
    if (!idea) return;
    try {
      await updateIdeaMutation.mutateAsync({
        id: idea.id,
        data: { is_favorite: !idea.is_favorite }
      });
    } catch (error) {
      console.error('Erro ao atualizar favorito:', error);
    }
  };
  
  const handleDelete = async () => {
    if (!idea) return;
    if (confirm('Tem certeza que deseja excluir esta ideia?')) {
      try {
        await deleteIdeaMutation.mutateAsync(idea.id);
        navigate('/ideas');
      } catch (error) {
        console.error('Erro ao excluir ideia:', error);
      }
    }
  };
  
  const handleShare = () => {
    if (!idea) return;
    navigator.clipboard.writeText(window.location.href);
    // Optionally show a toast notification
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#B4EB00] mx-auto mb-4" />
          <p className="text-gray-600">Carregando ideia...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar ideia</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar a ideia. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors mr-2"
          >
            Tentar novamente
          </button>
          <button
            onClick={() => navigate('/ideas')}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Voltar para lista
          </button>
        </div>
      </div>
    );
  }

  if (!idea) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Ideia não encontrada</h1>
          <button
            onClick={() => navigate('/ideas')}
            className="text-[#B4EB00] hover:underline"
          >
            Voltar para lista de ideias
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-white"
    >
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-10">
        <div className="max-w-2xl mx-auto px-4">
          <div className="h-16 flex items-center justify-between">
            <button
              onClick={() => navigate('/ideas')}
              className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </button>
            <div className="flex items-center gap-2">
              <button 
                onClick={handleShare}
                className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              >
                <Share2 size={20} className="text-gray-600" />
              </button>
              <button 
                onClick={handleToggleFavorite}
                disabled={updateIdeaMutation.isPending}
                className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                <Star
                  size={20}
                  className={idea.is_favorite ? 'fill-[#F9D449] text-[#F9D449]' : 'text-gray-400'}
                />
              </button>
              <button 
                onClick={handleDelete}
                disabled={deleteIdeaMutation.isPending}
                className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                <Trash2 size={20} className="text-red-500" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto px-4 pt-24 pb-16">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{idea.title}</h1>
            <div className="flex items-center gap-3 mt-3">
              <span className="text-sm text-gray-500">
                {formatDate(idea.created_at)}
              </span>
              {idea.category_name && (
                <span className="px-2 py-1 bg-gray-100 rounded-full text-sm text-gray-600">
                  {idea.category_name}
                </span>
              )}
            </div>
          </div>

          <div className="prose prose-lg">
            <p className="text-gray-600 whitespace-pre-wrap">
              {idea.content || idea.description}
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default IdeaDetailPage;