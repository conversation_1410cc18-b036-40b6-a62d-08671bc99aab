import React, { useState, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ChevronDown, PiggyBank, Lightbulb, CheckCircle, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import MonthYearSelector from '../components/MonthYearSelector';
import ProgressBar from '../components/ProgressBar';
import { useDashboardData } from '../hooks/useDashboard';
import { useFinances } from '../hooks/useFinances';
import { useTasks } from '../hooks/useTasks';
import { useIdeas } from '../hooks/useIdeas';

interface MonthData {
  month: number;
  year: number;
  appointments: {
    completed: number;
    total: number;
  };
  savings: number;
  ideas: number;
}

// Removed mock data - will be generated from real API data

type SortOption = 'performance' | 'savings' | 'ideas' | 'chronological';

const MonthlyProgressPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [sortBy, setSortBy] = useState<SortOption>('chronological');
  const [isSortOpen, setIsSortOpen] = useState(false);

  // Fetch data from APIs
  const { data: dashboardData, isLoading: isDashboardLoading, error: dashboardError } = useDashboardData();
  const { data: financesData, isLoading: isFinancesLoading } = useFinances();
  const { data: tasksData, isLoading: isTasksLoading } = useTasks();
  const { data: ideasData, isLoading: isIdeasLoading } = useIdeas();

  const isLoading = isDashboardLoading || isFinancesLoading || isTasksLoading || isIdeasLoading;

  const sortOptions: { value: SortOption; label: string }[] = [
    { value: 'performance', label: 'Maior desempenho' },
    { value: 'savings', label: 'Mais economias' },
    { value: 'ideas', label: 'Mais ideias' },
    { value: 'chronological', label: 'Cronológico' },
  ];

  // Generate monthly data from real API data
  const monthlyData = useMemo((): MonthData[] => {
    if (!financesData || !tasksData || !ideasData) return [];

    const currentDate = new Date();
    const months: MonthData[] = [];

    // Generate data for last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const month = date.getMonth();
      const year = date.getFullYear();

      // Filter data for this month
      const monthFinances = financesData.finances.filter(finance => {
        const financeDate = new Date(finance.transaction_date);
        return financeDate.getMonth() === month && financeDate.getFullYear() === year;
      });

      const monthTasks = tasksData.tasks.filter(task => {
        if (!task.task_date) return false;
        const taskDate = new Date(task.task_date);
        return taskDate.getMonth() === month && taskDate.getFullYear() === year;
      });

      const monthIdeas = ideasData.ideas.filter(idea => {
        const ideaDate = new Date(idea.created_at);
        return ideaDate.getMonth() === month && ideaDate.getFullYear() === year;
      });

      // Calculate savings for this month (deposits - withdrawals)
      const savings = monthFinances
        .filter(f => f.is_saving)
        .reduce((sum, f) => {
          const amount = parseFloat(f.amount);
          // Depósitos (expense) são positivos, retiradas (income) são negativas
          return f.transaction_type === 'expense' ? sum + amount : sum - amount;
        }, 0);

      // Calculate task completion
      const completedTasks = monthTasks.filter(task => task.completed_at).length;
      const totalTasks = monthTasks.length;

      months.push({
        month,
        year,
        appointments: {
          completed: completedTasks,
          total: totalTasks || 1 // Avoid division by zero
        },
        savings,
        ideas: monthIdeas.length
      });
    }

    return months;
  }, [financesData, tasksData, ideasData]);

  const sortData = (data: MonthData[]): MonthData[] => {
    const sorted = [...data];
    switch (sortBy) {
      case 'performance':
        return sorted.sort((a, b) => 
          (b.appointments.completed / b.appointments.total) - 
          (a.appointments.completed / a.appointments.total)
        );
      case 'savings':
        return sorted.sort((a, b) => b.savings - a.savings);
      case 'ideas':
        return sorted.sort((a, b) => b.ideas - a.ideas);
      default:
        return sorted.sort((a, b) => {
          const dateA = new Date(a.year, a.month);
          const dateB = new Date(b.year, b.month);
          return dateB.getTime() - dateA.getTime();
        });
    }
  };

  const getBestMonth = () => {
    if (monthlyData.length === 0) return null;
    return monthlyData.reduce((best, current) => {
      const bestPerformance = best.appointments.completed / best.appointments.total;
      const currentPerformance = current.appointments.completed / current.appointments.total;
      return currentPerformance > bestPerformance ? current : best;
    });
  };

  const bestMonth = getBestMonth();
  const bestMonthPerformance = bestMonth ? Math.round((bestMonth.appointments.completed / bestMonth.appointments.total) * 100) : 0;

  const formatMonth = (month: number, year: number) => {
    return new Date(year, month).toLocaleDateString('pt-BR', {
      month: 'long',
      year: 'numeric',
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando progresso mensal...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (dashboardError) {
    return (
      <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados de progresso. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6">
      {/* Header with Back Button */}
      <div className="flex items-center justify-between mb-8">
        <button 
          onClick={() => navigate('/')}
          className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        
        <h1 className="text-xl font-bold text-gray-900">Progresso Mensal</h1>
        
        <div className="w-10" /> {/* Spacer for alignment */}
      </div>

      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Resumo do seu progresso
          </h2>
          <p className="text-gray-600">
            Compare seus resultados entre os últimos meses. Veja o quanto você está evoluindo.
          </p>
        </div>

        {/* Horizontal Layout for Mobile - Month Selector and Sort Button */}
        <div className="flex items-center justify-between gap-3">
          {/* Month/Year Selector - Left Side */}
          <div className="flex-shrink-0">
            <MonthYearSelector
              selectedDate={selectedDate}
              onDateChange={setSelectedDate}
            />
          </div>

          {/* Sort Button - Right Side */}
          <div className="relative flex-shrink-0">
            <button
              onClick={() => setIsSortOpen(!isSortOpen)}
              className="h-10 px-3 md:px-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all flex items-center gap-1.5 md:gap-2 text-sm"
            >
              <span className="text-gray-900 text-xs md:text-sm">Ordenar</span>
              <ChevronDown
                size={14}
                className={`text-gray-400 transition-transform ${
                  isSortOpen ? 'rotate-180' : ''
                }`}
              />
            </button>

            <AnimatePresence>
              {isSortOpen && (
                <>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 z-40"
                    onClick={() => setIsSortOpen(false)}
                  />
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-lg z-50 py-2"
                  >
                    {sortOptions.map(option => (
                      <button
                        key={option.value}
                        onClick={() => {
                          setSortBy(option.value);
                          setIsSortOpen(false);
                        }}
                        className={`w-full px-4 py-2 text-left text-sm transition-colors ${
                          sortBy === option.value
                            ? 'bg-gray-100 text-gray-900'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        {option.label}
                      </button>
                    ))}
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>

        {bestMonth && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-[#212121] text-white rounded-2xl p-6 shadow-sm"
          >
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-xl font-semibold mb-2">
                  Seu melhor mês até agora foi {formatMonth(bestMonth.month, bestMonth.year)}
                </h2>
                <div className="space-y-4">
                  <div>
                    <p className="text-white/80 mb-1">
                      {bestMonth.appointments.completed} de {bestMonth.appointments.total} compromissos concluídos
                    </p>
                    <ProgressBar
                      percentage={bestMonthPerformance}
                      color="#82E882"
                      bgColor="rgba(255,255,255,0.1)"
                    />
                  </div>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(bestMonth.savings)} economizados
                  </p>
                </div>
              </div>
              <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
                <CheckCircle size={32} className="text-[#212121]" />
              </div>
            </div>
          </motion.div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {monthlyData.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-600 mb-2">Nenhum dado encontrado</p>
              <p className="text-sm text-gray-500">
                Comece a usar o aplicativo para ver seu progresso mensal aqui.
              </p>
            </div>
          ) : (
            sortData(monthlyData).map((data, index) => (
            <motion.div
              key={`${data.month}-${data.year}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-sm"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {formatMonth(data.month, data.year)}
              </h3>

              <div className="space-y-6">
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    {data.appointments.completed} de {data.appointments.total} compromissos concluídos
                  </p>
                  <ProgressBar
                    percentage={Math.round((data.appointments.completed / data.appointments.total) * 100)}
                    color="#82E882"
                    bgColor="#E0E0E0"
                    height={5}
                  />
                </div>

                <div className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-gray-100 rounded-lg">
                      <PiggyBank size={18} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Economizados</p>
                      <p className="font-medium text-gray-900">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(data.savings)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="p-1.5 bg-gray-100 rounded-lg">
                      <Lightbulb size={18} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Ideias salvas</p>
                      <p className="font-medium text-gray-900">{data.ideas}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default MonthlyProgressPage;