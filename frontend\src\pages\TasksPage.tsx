import React, { useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Calendar, ChevronLeft, ChevronRight, Clock, CheckCircle, Sun, Sunset, Moon, AlertCircle, Maximize2 } from 'lucide-react';
import TaskProgress from '../components/TaskProgress';
import TaskItem from '../components/TaskItem';
import Header from '../components/Header';
import AddTaskModal from '../components/AddTaskModal';
import ProgressCircle from '../components/ProgressCircle';
import LoadingState, { PageLoadingState } from '../components/LoadingState';
import ErrorBoundary from '../components/ErrorBoundary';
import { motion, AnimatePresence } from 'framer-motion';
import { useTasks, useCompleteTask, useDeleteTask, useTaskCategories } from '../hooks/useTasks';
import { TaskResponseDto } from '../types/api';
import { isEmpty } from '../lib/query-utils';

const TasksPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isCalendarExpanded, setIsCalendarExpanded] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Fetch tasks and categories
  const { data: tasksData, isLoading: tasksLoading, error: tasksError } = useTasks();
  const { data: categories } = useTaskCategories();
  const completeTaskMutation = useCompleteTask();
  const deleteTaskMutation = useDeleteTask();

  const tasks = tasksData?.tasks || [];

  // Filter tasks for the selected date using useMemo to prevent unnecessary re-calculations
  const tasksForSelectedDate = useMemo(() => {
    const dateString = selectedDate.toISOString().split('T')[0];
    return tasks.filter(task => {
      if (!task.task_date) return false;
      const taskDate = new Date(task.task_date).toISOString().split('T')[0];
      return taskDate === dateString;
    });
  }, [tasks, selectedDate]);

  const { completedTasks, totalTasks } = useMemo(() => {
    const completed = tasksForSelectedDate.filter(task => task.completed_at).length;
    const total = tasksForSelectedDate.length;
    return { completedTasks: completed, totalTasks: total };
  }, [tasksForSelectedDate]);



  // Date navigation functions
  const navigateDay = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setSelectedDate(newDate);
  };

  // Helper functions for date handling
  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const formatDateDisplay = (date: Date) => {
    const today = new Date();

    if (isToday(date)) {
      return 'Hoje';
    }

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isYesterday) {
      return 'Ontem';
    }

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const isTomorrow = date.toDateString() === tomorrow.toDateString();

    if (isTomorrow) {
      return 'Amanhã';
    }

    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    });
  };

  // Calendar functions
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const days = [];

    // Add padding days from previous month
    const firstDayOfWeek = firstDay.getDay();
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month - 1, prevMonthLastDay - i);
      days.push({ date, isPadding: true });
    }

    // Add days of current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      days.push({ date, isPadding: false });
    }

    // Add padding days from next month
    const remainingDays = 42 - days.length; // 6 rows * 7 days
    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(year, month + 1, i);
      days.push({ date, isPadding: true });
    }

    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentMonth(newMonth);
  };

  const formatMonth = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const isSelected = (date: Date) => {
    return date.toDateString() === selectedDate.toDateString();
  };

  const getTasksForDate = useCallback((date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return tasks.filter(task => {
      if (!task.task_date) return false;
      const taskDate = new Date(task.task_date).toISOString().split('T')[0];
      return taskDate === dateString;
    });
  }, [tasks]);

  const hasEvents = useCallback((date: Date) => {
    return getTasksForDate(date).length > 0;
  }, [getTasksForDate]);

  const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  // Function to get current week days
  const getWeekDays = useCallback(() => {
    const today = new Date();
    const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Get the start of the week (Sunday)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay);
    
    const weekDaysArray = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      
      weekDaysArray.push({
        date,
        dayName: weekDays[date.getDay()].toLowerCase() + '.',
        dayNumber: date.getDate()
      });
    }
    
    return weekDaysArray;
  }, [weekDays]);

  // Group tasks by time shifts using useMemo to prevent unnecessary re-calculations
  const groupedTasks = useMemo(() => {
    const shifts = {
      morning: [] as TaskResponseDto[],    // 04:00 - 12:00
      afternoon: [] as TaskResponseDto[],  // 12:00 - 18:00
      night: [] as TaskResponseDto[],      // 18:00 - 04:00
      completed: [] as TaskResponseDto[]   // Completed tasks
    };

    tasksForSelectedDate.forEach(task => {
      if (task.completed_at) {
        shifts.completed.push(task);
        return;
      }

      if (!task.task_date) {
        shifts.morning.push(task);
        return;
      }

      const taskDate = new Date(task.task_date);
      const hour = taskDate.getHours();

      if (hour >= 4 && hour < 12) {
        shifts.morning.push(task);
      } else if (hour >= 12 && hour < 18) {
        shifts.afternoon.push(task);
      } else {
        shifts.night.push(task);
      }
    });

    // Sort completed tasks by completion time (most recent first)
    shifts.completed.sort((a, b) => {
      if (!a.completed_at || !b.completed_at) return 0;
      return new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime();
    });

    // Sort other shifts by time
    [shifts.morning, shifts.afternoon, shifts.night].forEach(shift => {
      shift.sort((a, b) => {
        if (!a.task_date) return 1;
        if (!b.task_date) return -1;
        return new Date(a.task_date).getTime() - new Date(b.task_date).getTime();
      });
    });

    return shifts;
  }, [tasksForSelectedDate]);

  const handleComplete = useCallback(async (taskId: number) => {
    try {
      await completeTaskMutation.mutateAsync(taskId);
    } catch (error) {
      // You could add a toast notification here for better UX
    }
  }, [completeTaskMutation]);

  const handleEdit = useCallback((taskId: number, updates: Partial<TaskResponseDto>) => {
    // Implementar edição de tarefa se necessário
  }, []);

  const handleDelete = useCallback(async (taskId: number) => {
    if (confirm('Tem certeza que deseja excluir esta tarefa?')) {
      try {
        await deleteTaskMutation.mutateAsync(taskId);
      } catch (error) {
        // You could add a toast notification here for better UX
      }
    }
  }, [deleteTaskMutation]);

  // Handle loading and error states
  if (tasksLoading || tasksError) {
    return (
      <LoadingState
        isLoading={tasksLoading}
        error={tasksError}
        onRetry={() => window.location.reload()}
        className="min-h-screen bg-[#F7F7F7]"
      >
        {/* This won't render due to loading/error state */}
        <div />
      </LoadingState>
    );
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setIsCalendarExpanded(false);
  };

const formatCompletionTime = (completedAt: Date | string) => {
    const dateObj = completedAt instanceof Date ? completedAt : new Date(completedAt);
    return dateObj.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Shift Card Component
  const ShiftCard: React.FC<{
    title: string;
    timeRange: string;
    tasks: TaskResponseDto[];
    icon: React.ReactNode;
    iconColor: string;
    bgColor: string;
  }> = ({ title, timeRange, tasks, icon, iconColor, bgColor }) => {
    // Don't render if no tasks
    if (tasks.length === 0) return null;

    return (
      <div className={`${bgColor} rounded-2xl p-6 shadow-sm`}>
        {/* Shift Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className={`p-3 ${iconColor} rounded-xl`}>
            {icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-sm text-gray-600">{timeRange}</p>
            <p className="text-xs text-gray-500 mt-1">
              {tasks.length} {tasks.length === 1 ? 'tarefa' : 'tarefas'}
            </p>
          </div>
        </div>

        {/* Tasks List */}
        <div className="space-y-4">
          {tasks.map(task => (
            <TaskItem
              key={task.id}
              id={task.id.toString()}
              title={task.name}
              description={task.description}
              time={task.task_date ? new Date(task.task_date).toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
              }) : undefined}
              completed={!!task.completed_at}
              category={task.category_name || 'Tarefa'}
              onComplete={() => handleComplete(task.id)}
              onEdit={(id, updates) => handleEdit(task.id, updates)}
              onDelete={() => handleDelete(task.id)}
            />
          ))}
        </div>
      </div>
    );
  };

  // Completed Tasks Card Component
  const CompletedTasksCard: React.FC<{ tasks: TaskResponseDto[] }> = ({ tasks }) => {
    // Don't render if no completed tasks
    if (tasks.length === 0) return null;

    return (
      <div className="bg-gradient-to-br from-[#B4EB00]/10 to-[#B4EB00]/5 rounded-2xl p-6 shadow-sm border border-[#B4EB00]/20">
        {/* Completed Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-[#B4EB00] rounded-xl">
            <CheckCircle size={24} className="text-gray-900" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Tarefas Concluídas</h3>
            <p className="text-sm text-gray-600">Parabéns pelo progresso!</p>
            <p className="text-xs text-gray-500 mt-1">
              {tasks.length} {tasks.length === 1 ? 'tarefa concluída' : 'tarefas concluídas'}
            </p>
          </div>
        </div>

        {/* Completed Tasks List */}
        <div className="space-y-4">
          {tasks.map(task => (
            <div
              key={task.id}
              className="bg-white/60 rounded-xl p-4 border border-[#B4EB00]/10"
            >
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#B4EB00] flex items-center justify-center flex-shrink-0 mt-0.5">
                  <CheckCircle size={16} className="text-gray-900" />
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 line-through">
                    {task.name}
                  </h4>
                  {task.description && (
                    <p className="text-sm text-gray-600 mt-1 line-through">
                      {task.description}
                    </p>
                  )}
                  <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                    {task.task_date && (
                      <div className="flex items-center gap-1">
                        <Clock size={12} />
                        <span>{new Date(task.task_date).toLocaleTimeString('pt-BR', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}</span>
                      </div>
                    )}
                    {task.completed_at && (
                      <span>
                        Concluída às {formatCompletionTime(task.completed_at)}
                      </span>
                    )}
                    {task.category_name && (
                      <span className="px-2 py-1 bg-gray-200 rounded-full">
                        {task.category_name}
                      </span>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => handleComplete(task.id)}
                  className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                  title="Marcar como não concluída"
                >
                  <CheckCircle size={16} className="text-[#B4EB00]" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="relative min-h-screen bg-[#F7F7F7]">
      <Header onAddClick={() => setIsAddModalOpen(true)} />

      {/* Desktop Layout */}
      <div className="hidden md:flex max-w-7xl mx-auto pt-20 px-6 pb-6 gap-6">
        {/* Left Column */}
        <div className="w-1/2 space-y-6">
          {/* Progress Circle */}
          <div className="bg-white rounded-2xl p-6 shadow-sm">
            <div className="flex flex-col items-center">
              <ProgressCircle
                percentage={totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}
                size={160}
                color="#B4EB00"
                bgColor="#E5E7EB"
              />
              <p className="text-gray-600 mt-4">
                {totalTasks > 0
                  ? `Você concluiu ${completedTasks} de ${totalTasks} hoje`
                  : 'Nenhum compromisso para esta data'
                }
              </p>
            </div>
          </div>

          {/* Calendar Expandable Section */}
          <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
            <AnimatePresence mode="wait">
              {isCalendarExpanded ? (
                <motion.div
                  key="expanded"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-4"
                >
                  {/* Calendar Header */}
                  <div className="flex items-center justify-between mb-4">
                    <button
                      onClick={() => navigateMonth('prev')}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronLeft size={20} className="text-gray-600" />
                    </button>

                    <h2 className="text-lg font-semibold text-gray-900">
                      {formatMonth(currentMonth)}
                    </h2>

                    <button
                      onClick={() => navigateMonth('next')}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronRight size={20} className="text-gray-600" />
                    </button>
                  </div>

                  {/* Calendar Grid */}
                  <div className="grid grid-cols-7 gap-1">
                    {weekDays.map((day) => (
                      <div
                        key={day}
                        className="text-center text-sm font-medium text-gray-600 py-2"
                      >
                        {day}
                      </div>
                    ))}

                    {getDaysInMonth(currentMonth).map(({ date, isPadding }, index) => (
                      <button
                        key={index}
                        onClick={() => handleDateSelect(date)}
                        className={`
                          aspect-square p-1 relative rounded-lg transition-colors text-sm
                          ${isPadding ? 'text-gray-400' : 'text-gray-900'}
                          ${isSelected(date) ? 'bg-[#B4EB00] text-gray-900' : ''}
                          ${isToday(date) && !isSelected(date) ? 'bg-gray-100' : ''}
                          ${!isPadding ? 'hover:bg-gray-100' : ''}
                        `}
                      >
                        <span>{date.getDate()}</span>
                        {hasEvents(date) && !isPadding && (
                          <div className="absolute bottom-1 left-1/2 -translate-x-1/2 w-1 h-1 rounded-full bg-[#B4EB00]" />
                        )}
                      </button>
                    ))}
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="compact"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-4"
                >
                  <button
                    onClick={() => setIsCalendarExpanded(true)}
                    className="w-full flex items-center justify-center gap-2 py-3 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Calendar size={20} className="text-gray-600" />
                    <span className="font-medium text-gray-900">Calendário</span>
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-1/2 space-y-6">
          {/* Quick Date Navigation */}
          {
          /*
          <div className="bg-white rounded-2xl p-4 shadow-sm hidden">
            <div className="flex items-center justify-between mb-3 ">
              <h3 className="text-sm font-medium text-gray-700">Navegação Rápida</h3>
              <button
                onClick={() => setIsCalendarExpanded(!isCalendarExpanded)}
                className="flex items-center gap-1.5 px-2 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Calendar size={12} />
                <span>Calendário</span>
              </button>
            </div>

            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setSelectedDate(new Date())}
                className={`px-3 py-1.5 text-xs rounded-lg transition-colors ${
                  isToday(selectedDate)
                    ? 'bg-[#B4EB00] text-gray-900'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Hoje
              </button>

              <button
                onClick={() => {
                  const tomorrow = new Date();
                  tomorrow.setDate(tomorrow.getDate() + 1);
                  setSelectedDate(tomorrow);
                }}
                className={`px-3 py-1.5 text-xs rounded-lg transition-colors ${
                  (() => {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    return selectedDate.toDateString() === tomorrow.toDateString();
                  })()
                    ? 'bg-[#B4EB00] text-gray-900'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Amanhã
              </button>

              <button
                onClick={() => {
                  const nextWeek = new Date();
                  nextWeek.setDate(nextWeek.getDate() + 7);
                  setSelectedDate(nextWeek);
                }}
                className="px-3 py-1.5 text-xs bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-lg transition-colors"
              >
                +7 dias
              </button>
            </div>
          </div>
          */}

          {/* Compact Horizontal Navigation */}
          <div className="flex items-center justify-between h-10">
            {/* Ver Semana - Left */}
            <button
              onClick={() => navigate('/tasks/week')}
              className="flex items-center gap-1.5 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all text-sm"
            >
              <Calendar size={14} className="text-gray-600" />
              <span className="text-sm">Ver Semana</span>
            </button>

            {/* Date Navigation - Center */}
            <div className="flex items-center gap-1">
              <button
                onClick={() => navigateDay('prev')}
                className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronLeft size={16} className="text-gray-600" />
              </button>

              <h2 className="text-base font-medium text-gray-900 min-w-[70px] text-center px-2">
                {formatDateDisplay(selectedDate)}
              </h2>

              <button
                onClick={() => navigateDay('next')}
                className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ChevronRight size={16} className="text-gray-600" />
              </button>
            </div>

            {/* Calendário - Right */}
            <button
              onClick={() => setIsCalendarExpanded(!isCalendarExpanded)}
              className="flex items-center gap-1.5 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all text-sm"
            >
              <Calendar size={14} className="text-gray-600" />
              <span className="text-sm">Calendário</span>
            </button>
          </div>

          {/* Tasks Content */}
          <div className="space-y-6">
            {totalTasks > 0 ? (
              <>
                {/* Morning Shift Card */}
                <ShiftCard
                  title="MANHÃ"
                  timeRange="04:00h às 12:00h"
                  tasks={groupedTasks.morning}
                  icon={<Sun size={24} className="text-orange-600" />}
                  iconColor="bg-orange-100"
                  bgColor="bg-white"
                />

                {/* Afternoon Shift Card */}
                <ShiftCard
                  title="TARDE"
                  timeRange="12:00h às 18:00h"
                  tasks={groupedTasks.afternoon}
                  icon={<Sunset size={24} className="text-blue-600" />}
                  iconColor="bg-blue-100"
                  bgColor="bg-white"
                />

                {/* Night Shift Card */}
                <ShiftCard
                  title="NOITE"
                  timeRange="18:00h às 04:00h"
                  tasks={groupedTasks.night}
                  icon={<Moon size={24} className="text-purple-600" />}
                  iconColor="bg-purple-100"
                  bgColor="bg-white"
                />

                {/* Completed Tasks Card */}
                <CompletedTasksCard tasks={groupedTasks.completed} />
              </>
            ) : (
              <div className="bg-white rounded-2xl p-12 shadow-sm text-center">
                <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum compromisso
                </h3>
                <p className="text-gray-500 text-sm mb-4">
                  Você não tem compromissos agendados para esta data.
                </p>
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors"
                >
                  <Plus size={16} />
                  Adicionar compromisso
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden max-w-md mx-auto pt-20 px-4 pb-6">

        {/* Mobile Calendar Compact - Separate Card */}
        <div className="bg-white rounded-xl p-4 mb-4 shadow-sm">
          <AnimatePresence mode="wait">
            {isCalendarExpanded ? (
              <motion.div
                key="expanded"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Calendar Header */}
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={() => navigateMonth('prev')}
                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <ChevronLeft size={16} className="text-gray-600" />
                  </button>

                  <h3 className="text-sm font-semibold text-gray-900">
                    {formatMonth(currentMonth)}
                  </h3>

                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => setIsCalendarExpanded(false)}
                      className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronRight size={16} className="text-gray-600 rotate-90" />
                    </button>
                    <button
                      onClick={() => navigateMonth('next')}
                      className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronRight size={16} className="text-gray-600" />
                    </button>
                  </div>
                </div>

                {/* Full Calendar Grid */}
                <div className="grid grid-cols-7 gap-0.5">
                  {weekDays.map((day) => (
                    <div
                      key={day}
                      className="text-center text-xs font-medium text-gray-600 py-1"
                    >
                      {day}
                    </div>
                  ))}

                  {getDaysInMonth(currentMonth).map(({ date, isPadding }, index) => (
                    <button
                      key={index}
                      onClick={() => handleDateSelect(date)}
                      className={`
                        aspect-square p-1 relative rounded-lg transition-colors text-xs
                        ${isPadding ? 'text-gray-400' : 'text-gray-900'}
                        ${isSelected(date) ? 'bg-[#B4EB00] text-gray-900' : ''}
                        ${isToday(date) && !isSelected(date) ? 'bg-gray-200' : ''}
                        ${!isPadding ? 'hover:bg-gray-200' : ''}
                      `}
                    >
                      <span>{date.getDate()}</span>
                      {hasEvents(date) && !isPadding && (
                        <div className="absolute bottom-0.5 left-1/2 -translate-x-1/2 w-1 h-1 rounded-full bg-[#B4EB00]" />
                      )}
                    </button>
                  ))}
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="compact"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Mobile Navigation Header */}
                <div className="flex items-center justify-between mb-4">
                  {/* Ver calendário - Left */}
                  <button
                    onClick={() => setIsCalendarExpanded(true)}
                    className="flex items-center gap-1.5 px-3 py-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-sm font-medium text-gray-900">Ver calendário</span>
                  </button>

                  {/* Date Navigation - Center */}
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => navigateDay('prev')}
                      className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronLeft size={16} className="text-gray-600" />
                    </button>
                    
                    <button
                      onClick={() => navigateDay('next')}
                      className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <ChevronRight size={16} className="text-gray-600" />
                    </button>
                  </div>

                  {/* Ver semana - Right */}
                  <button
                    onClick={() => navigate('/tasks/week')}
                    className="flex items-center gap-1.5 px-3 py-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-sm font-medium text-gray-900">Ver semana</span>
                  </button>
                </div>

                {/* Week View */}
                <div className="grid grid-cols-7 gap-1">
                  {getWeekDays().map(({ date, dayName, dayNumber }) => (
                    <button
                      key={date.toISOString()}
                      onClick={() => handleDateSelect(date)}
                      className={`
                        flex flex-col items-center py-2 px-1 rounded-xl transition-colors
                        ${isSelected(date) ? 'bg-[#B4EB00] text-gray-900' : 'hover:bg-gray-50'}
                      `}
                    >
                      <span className="text-xs text-gray-600 mb-1">
                        {dayName}
                      </span>
                      <span className={`text-sm font-medium ${
                        isSelected(date) ? 'text-gray-900' : 'text-gray-900'
                      }`}>
                        {dayNumber}
                      </span>
                      {hasEvents(date) && (
                        <div className="w-1 h-1 rounded-full bg-[#B4EB00] mt-1" />
                      )}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Mobile Tasks Content */}
        <div className="space-y-4">
          {totalTasks > 0 ? (
            <>
              {/* Mobile Shift Cards */}
              <ShiftCard
                title="MANHÃ"
                timeRange="04:00h às 12:00h"
                tasks={groupedTasks.morning}
                icon={<Sun size={20} className="text-orange-600" />}
                iconColor="bg-orange-100"
                bgColor="bg-white"
              />

              <ShiftCard
                title="TARDE"
                timeRange="12:00h às 18:00h"
                tasks={groupedTasks.afternoon}
                icon={<Sunset size={20} className="text-blue-600" />}
                iconColor="bg-blue-100"
                bgColor="bg-white"
              />

              <ShiftCard
                title="NOITE"
                timeRange="18:00h às 04:00h"
                tasks={groupedTasks.night}
                icon={<Moon size={20} className="text-purple-600" />}
                iconColor="bg-purple-100"
                bgColor="bg-white"
              />

              <CompletedTasksCard tasks={groupedTasks.completed} />
            </>
          ) : (
            <div className="bg-white rounded-2xl p-8 shadow-sm text-center">
              <Calendar size={40} className="mx-auto mb-3 text-gray-300" />
              <h3 className="text-base font-medium text-gray-900 mb-2">
                Nenhum compromisso
              </h3>
              <p className="text-gray-500 text-sm mb-4">
                Você não tem compromissos para esta data.
              </p>
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="inline-flex items-center gap-2 px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors text-sm"
              >
                <Plus size={16} />
                Adicionar compromisso
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Add Task Modal */}
      <AddTaskModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        selectedDate={selectedDate}
      />
    </div>
  );
};

// Wrap with ErrorBoundary for better error handling
const TasksPageWithErrorBoundary: React.FC = () => (
  <ErrorBoundary>
    <TasksPage />
  </ErrorBoundary>
);

export default TasksPageWithErrorBoundary;


