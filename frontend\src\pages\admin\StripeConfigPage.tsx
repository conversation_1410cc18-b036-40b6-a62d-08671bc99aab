import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Key, AlertCircle, Eye, EyeOff, CheckCircle } from 'lucide-react';
import { Button, Input, Label, Card, CardHeader, CardTitle, CardContent } from '../../primitives';
import { toast } from '../../utils/toast';
import { authenticatedApi } from '../../lib/api';

interface StripeConfig {
  public_key: string;
  secret_key: string;
  webhook_endpoint_secret: string;
  test_mode: boolean;
}

const StripeConfigPage: React.FC = () => {
  const [config, setConfig] = useState<StripeConfig>({
    public_key: '',
    secret_key: '',
    webhook_endpoint_secret: '',
    test_mode: true
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showKeys, setShowKeys] = useState({
    secret: false,
    webhook: false
  });

  const [testResult, setTestResult] = useState<{
    status: 'success' | 'error' | null;
    message: string;
  }>({ status: null, message: '' });

  // Carregar configurações existentes
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const response = await authenticatedApi.get('/admin/stripe-config');
        const data = response.data;
        
        if (data) {
          setConfig({
            public_key: data.public_key || '',
            secret_key: data.secret_key || '',
            webhook_endpoint_secret: data.webhook_endpoint_secret || '',
            test_mode: data.test_mode ?? true
          });
        }
      } catch (error: any) {
        console.error('Erro ao carregar configurações Stripe:', error);
        if (error.response?.status !== 404) {
          toast.error('Erro ao carregar configurações do Stripe');
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, []);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      await authenticatedApi.post('/admin/stripe-config', config);
      toast.success('Configurações do Stripe salvas com sucesso!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao salvar configurações';
      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    setTestResult({ status: null, message: '' });
    
    if (!config.secret_key) {
      setTestResult({
        status: 'error',
        message: 'Chave secreta é obrigatória para testar a conexão'
      });
      return;
    }

    try {
      const response = await authenticatedApi.post('/admin/stripe-config/test', {
        secret_key: config.secret_key,
        test_mode: config.test_mode
      });

      setTestResult({
        status: 'success',
        message: response.data.message || 'Conexão com Stripe testada com sucesso!'
      });
    } catch (error: any) {
      setTestResult({
        status: 'error',
        message: error.response?.data?.message || 'Erro ao testar conexão com Stripe'
      });
    }
  };

  const toggleKeyVisibility = (key: 'secret' | 'webhook') => {
    setShowKeys(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F7F7F7] p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Configurações do Stripe</h1>
          <p className="text-gray-600">
            Configure as chaves de API do Stripe para processar pagamentos
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Formulário Principal */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="w-5 h-5" />
                  Chaves de API
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSave} className="space-y-6">
                  {/* Modo de Teste */}
                  <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
                    <input
                      type="checkbox"
                      id="test_mode"
                      checked={config.test_mode}
                      onChange={(e) => setConfig({ ...config, test_mode: e.target.checked })}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="test_mode" className="text-sm font-medium text-gray-700">
                      Modo de Teste
                    </label>
                    <span className="text-xs text-gray-500">
                      {config.test_mode ? '(Usando chaves de teste)' : '(Usando chaves de produção)'}
                    </span>
                  </div>

                  {/* Chave Pública */}
                  <div>
                    <Label>
                      Chave Pública {config.test_mode ? '(Teste)' : '(Produção)'}
                    </Label>
                    <Input
                      type="text"
                      value={config.public_key}
                      onChange={(e) => setConfig({ ...config, public_key: e.target.value })}
                      placeholder={config.test_mode ? "pk_test_..." : "pk_live_..."}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Começa com "{config.test_mode ? 'pk_test_' : 'pk_live_'}"
                    </p>
                  </div>

                  {/* Chave Secreta */}
                  <div>
                    <Label>
                      Chave Secreta {config.test_mode ? '(Teste)' : '(Produção)'}
                    </Label>
                    <div className="relative">
                      <Input
                        type={showKeys.secret ? 'text' : 'password'}
                        value={config.secret_key}
                        onChange={(e) => setConfig({ ...config, secret_key: e.target.value })}
                        placeholder={config.test_mode ? "sk_test_..." : "sk_live_..."}
                        required
                        className="pr-12"
                      />
                      <button
                        type="button"
                        onClick={() => toggleKeyVisibility('secret')}
                        className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
                      >
                        {showKeys.secret ? (
                          <EyeOff className="w-4 h-4 text-gray-500" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-500" />
                        )}
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Começa com "{config.test_mode ? 'sk_test_' : 'sk_live_'}"
                    </p>
                  </div>

                  {/* Webhook Endpoint Secret */}
                  <div>
                    <Label>Webhook Endpoint Secret</Label>
                    <div className="relative">
                      <Input
                        type={showKeys.webhook ? 'text' : 'password'}
                        value={config.webhook_endpoint_secret}
                        onChange={(e) => setConfig({ ...config, webhook_endpoint_secret: e.target.value })}
                        placeholder="whsec_..."
                        className="pr-12"
                      />
                      <button
                        type="button"
                        onClick={() => toggleKeyVisibility('webhook')}
                        className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
                      >
                        {showKeys.webhook ? (
                          <EyeOff className="w-4 h-4 text-gray-500" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-500" />
                        )}
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Usado para validar webhooks do Stripe
                    </p>
                  </div>

                  {/* Resultado do Teste */}
                  {testResult.status && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`p-4 rounded-xl border ${
                        testResult.status === 'success'
                          ? 'bg-green-50 border-green-200 text-green-800'
                          : 'bg-red-50 border-red-200 text-red-800'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        {testResult.status === 'success' ? (
                          <CheckCircle className="w-5 h-5" />
                        ) : (
                          <AlertCircle className="w-5 h-5" />
                        )}
                        <span className="text-sm font-medium">{testResult.message}</span>
                      </div>
                    </motion.div>
                  )}

                  {/* Ações */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={handleTestConnection}
                      disabled={!config.secret_key}
                      className="flex-1"
                    >
                      Testar Conexão
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={isSaving}
                      className="flex-1"
                    >
                      {isSaving ? 'Salvando...' : 'Salvar Configurações'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Informações Laterais */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status da Configuração</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Chave Pública</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      config.public_key 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {config.public_key ? 'Configurada' : 'Pendente'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Chave Secreta</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      config.secret_key 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {config.secret_key ? 'Configurada' : 'Pendente'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Webhook Secret</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      config.webhook_endpoint_secret 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {config.webhook_endpoint_secret ? 'Configurado' : 'Opcional'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Modo</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      config.test_mode 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {config.test_mode ? 'Teste' : 'Produção'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Instruções */}
            <Card>
              <CardHeader>
                <CardTitle>Como Configurar</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 text-sm text-gray-600">
                  <div>
                    <p className="font-medium text-gray-900 mb-1">1. Dashboard do Stripe</p>
                    <p>Acesse sua conta no Stripe Dashboard</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 mb-1">2. API Keys</p>
                    <p>Vá em Developers → API keys para encontrar suas chaves</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 mb-1">3. Webhooks (Opcional)</p>
                    <p>Configure webhooks em Developers → Webhooks</p>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <p className="text-yellow-800 text-xs">
                      <strong>Atenção:</strong> Mantenha o modo de teste ativado até que tudo esteja funcionando corretamente.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Segurança */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <AlertCircle className="w-5 h-5" />
                  Segurança
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Nunca compartilhe suas chaves secretas</p>
                  <p>• Use HTTPS em produção</p>
                  <p>• Monitore logs de acesso</p>
                  <p>• Revise permissões regularmente</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StripeConfigPage;
