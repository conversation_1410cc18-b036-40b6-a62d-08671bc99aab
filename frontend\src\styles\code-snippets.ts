/**
 * EXACT CODE SNIPPETS FOR REUSE
 * 
 * Copy and paste these exact snippets to use the shared Tailwind utility constants
 */

// =======================
// IMPORT STATEMENTS
// =======================

// Import individual constants (most common usage)
import { accentColour, accentHover, inputBase, cardBase } from './constants';

// Import grouped constants for more options
import { COLORS, STYLES, TAILWIND_CLASSES } from './constants';

// =======================
// EXACT USAGE SNIPPETS
// =======================

// 1. ACCENT COLOR USAGE
const ExampleButton = () => (
  <button className="bg-accent hover:bg-accent-90 text-white px-4 py-2 rounded-xl">
    Primary Button
  </button>
);

// 2. INPUT BASE USAGE
const ExampleInput = () => (
  <input 
    type="text" 
    className={inputBase}
    placeholder="Enter text..."
  />
);

// Alternative input usage with the full class string:
const ExampleInputDirect = () => (
  <input 
    type="text" 
    className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-150"
    placeholder="Enter text..."
  />
);

// 3. CARD BASE USAGE
const ExampleCard = () => (
  <div className={cardBase}>
    <h3>Card Title</h3>
    <p>Card content goes here</p>
  </div>
);

// Alternative card usage with the full class string:
const ExampleCardDirect = () => (
  <div className="bg-white rounded-2xl p-6 shadow-sm">
    <h3>Card Title</h3>
    <p>Card content goes here</p>
  </div>
);

// =======================
// COMBINED USAGE EXAMPLES
// =======================

// Form with accent colors and input base
const ExampleForm = () => (
  <div className={cardBase}>
    <h2 className="text-accent text-xl font-semibold mb-4">Contact Form</h2>
    <div className="space-y-4">
      <input 
        className={inputBase}
        type="text" 
        placeholder="Name"
      />
      <input 
        className={inputBase}
        type="email" 
        placeholder="Email"
      />
      <textarea 
        className={`${inputBase} h-24 resize-none`}
        placeholder="Message"
      />
      <button className="bg-accent hover:bg-accent-90 text-white px-6 py-3 rounded-xl transition-all duration-150">
        Send Message
      </button>
    </div>
  </div>
);

// Dashboard card with interactive styles
const ExampleDashboardCard = () => (
  <div className={`${cardBase} hover:shadow-md transition-shadow duration-150 cursor-pointer`}>
    <div className="flex items-center justify-between mb-3">
      <h3 className="text-lg font-semibold">Statistics</h3>
      <div className="w-3 h-3 bg-accent rounded-full"></div>
    </div>
    <p className="text-gray-600">Some dashboard content here</p>
  </div>
);

// =======================
// EXACT CONSTANTS VALUES
// =======================

export const EXACT_VALUES = {
  accentColour: '#B4EB00',
  accentHover: '#A3D600', // accent/90 equivalent
  inputBase: 'w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-150',
  cardBase: 'bg-white rounded-2xl p-6 shadow-sm'
} as const;
