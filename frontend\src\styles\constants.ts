/**
 * Shared Tailwind utility constants for consistent styling across the application
 */

export const COLORS = {
  // Accent colors
  accent: '#B4EB00',
  accentHover: '#A3D600', // accent/90 equivalent (approximately 90% of the original brightness)
} as const;

export const STYLES = {
  // Input base styles
  inputBase: 'w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-150',
  
  // Card base styles  
  cardBase: 'bg-white rounded-2xl p-6 shadow-sm',
} as const;

// Alternative approach using CSS custom properties for better Tailwind integration
export const CSS_VARIABLES = {
  '--accent-color': '#B4EB00',
  '--accent-hover': '#A3D600',
} as const;

// Tailwind class utilities for common patterns
export const TAILWIND_CLASSES = {
  accent: {
    bg: 'bg-accent',
    bgHover: 'hover:bg-accent-90',
    text: 'text-accent',
    border: 'border-accent',
    ring: 'ring-accent',
    focusRing: 'focus:ring-accent',
  },
  input: {
    base: 'w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-150',
    error: 'w-full h-12 px-4 border border-red-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-150',
    success: 'w-full h-12 px-4 border border-green-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-150',
  },
  card: {
    base: 'bg-white rounded-2xl p-6 shadow-sm',
    elevated: 'bg-white rounded-2xl p-6 shadow-md',
    interactive: 'bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-150 cursor-pointer',
  },
} as const;

// Export individual constants for convenience
export const accentColour = COLORS.accent;
export const accentHover = COLORS.accentHover;
export const inputBase = STYLES.inputBase;
export const cardBase = STYLES.cardBase;
