/* 
 * Popup Positioning System - CSS Moderno
 * Implementação das especificações técnicas para posicionamento de pop-ups
 */

/* Base class for all popups */
.popup-base {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  
  /* Responsividade */
  width: calc(95vw);
  max-width: 28rem; /* 448px */
  max-height: 90vh;
  
  /* Flexbox para layout interno */
  display: flex;
  flex-direction: column;
  
  /* Scroll suave */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  
  /* Margem mínima das bordas */
  margin: 16px;
  
  /* Estilo visual */
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Overlay backdrop */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 999;
}

/* Media queries para diferentes tamanhos de tela */
@media (max-width: 768px) {
  .popup-base {
    width: calc(100vw - 32px); /* 95vw com margem mínima */
    max-width: calc(100vw - 32px);
    margin: 16px;
  }
  
  /* Ajustes específicos para mobile */
  .popup-base {
    max-height: calc(100vh - 32px);
  }
  
  /* Melhor experiência touch */
  .popup-base {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

@media (min-width: 769px) {
  .popup-base {
    width: calc(80vw);
    max-width: 32rem; /* 512px para desktop */
  }
}

/* Telas muito pequenas (320px) */
@media (max-width: 320px) {
  .popup-base {
    width: calc(100vw - 24px);
    max-width: calc(100vw - 24px);
    margin: 12px;
    border-radius: 0.75rem;
  }
}

/* Telas grandes (1440px+) */
@media (min-width: 1440px) {
  .popup-base {
    max-width: 36rem; /* 576px */
  }
}

/* Animações suaves */
.popup-enter {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.95);
}

.popup-enter-active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.popup-exit {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.popup-exit-active {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.95);
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Estrutura interna flexível */
.popup-header {
  flex-shrink: 0;
  padding: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.popup-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.popup-footer {
  flex-shrink: 0;
  padding: 1.5rem;
  border-top: 1px solid #f3f4f6;
}

/* Melhorias para acessibilidade */
.popup-base:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Suporte para modo escuro */
@media (prefers-color-scheme: dark) {
  .popup-base {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .popup-header,
  .popup-footer {
    border-color: #374151;
  }
}

/* Otimizações para performance */
.popup-base {
  will-change: transform, opacity;
  contain: layout style paint;
}

/* Suporte para reduced motion */
@media (prefers-reduced-motion: reduce) {
  .popup-enter-active,
  .popup-exit-active {
    transition: opacity 150ms ease-in-out;
  }
  
  .popup-enter,
  .popup-exit-active {
    transform: translate(-50%, -50%) scale(1);
  }
}