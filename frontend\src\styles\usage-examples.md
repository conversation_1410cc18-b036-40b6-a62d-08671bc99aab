# Tailwind Utility Constants Usage Examples

This file demonstrates how to use the shared Tailwind utility constants defined in `styles/constants.ts`.

## Import Options

```typescript
// Import individual constants
import { accentColour, accentHover, inputBase, cardBase } from './constants';

// Import grouped constants
import { COLORS, STYLES, TAILWIND_CLASSES } from './constants';

// Import all
import * as StyleConstants from './constants';
```

## Basic Usage

### Using Individual Constants

```tsx
// Direct class usage
<div className={inputBase}>
  <input type="text" placeholder="Search..." />
</div>

<div className={cardBase}>
  <h2>Card Title</h2>
  <p>Card content...</p>
</div>

// Using with additional classes
<div className={`${cardBase} hover:shadow-lg`}>
  Interactive card
</div>
```

### Using Grouped Constants

```tsx
// Input variations
<input className={STYLES.inputBase} />
<input className={TAILWIND_CLASSES.input.base} />
<input className={TAILWIND_CLASSES.input.error} />
<input className={TAILWIND_CLASSES.input.success} />

// Card variations  
<div className={STYLES.cardBase} />
<div className={TAILWIND_CLASSES.card.base} />
<div className={TAILWIND_CLASSES.card.elevated} />
<div className={TAILWIND_CLASSES.card.interactive} />

// Accent color utilities
<button className={TAILWIND_CLASSES.accent.bg}>Primary Button</button>
<button className={`${TAILWIND_CLASSES.accent.bg} ${TAILWIND_CLASSES.accent.bgHover}`}>
  Hover Button
</button>
```

## Advanced Usage

### With Conditional Classes

```tsx
import { TAILWIND_CLASSES } from './constants';

function Button({ variant, children }: { variant: 'primary' | 'secondary', children: React.ReactNode }) {
  const baseClasses = 'px-4 py-2 rounded-xl transition-all duration-150';
  const variantClasses = variant === 'primary' 
    ? `${TAILWIND_CLASSES.accent.bg} ${TAILWIND_CLASSES.accent.bgHover} text-white`
    : 'bg-gray-200 hover:bg-gray-300 text-gray-800';
    
  return (
    <button className={`${baseClasses} ${variantClasses}`}>
      {children}
    </button>
  );
}
```

### With clsx/classnames

```tsx
import clsx from 'clsx';
import { TAILWIND_CLASSES } from './constants';

function FormInput({ error, success, className, ...props }: FormInputProps) {
  return (
    <input
      className={clsx(
        TAILWIND_CLASSES.input.base,
        {
          [TAILWIND_CLASSES.input.error]: error,
          [TAILWIND_CLASSES.input.success]: success,
        },
        className
      )}
      {...props}
    />
  );
}
```

### Using CSS Variables

```tsx
import { CSS_VARIABLES } from './constants';

// Apply CSS variables to a parent element
function App() {
  return (
    <div style={CSS_VARIABLES}>
      {/* Now you can use the CSS variables in CSS/SCSS */}
      <button className="bg-[var(--accent-color)] hover:bg-[var(--accent-hover)]">
        Button
      </button>
    </div>
  );
}
```

## Available Constants

### Colors
- `accentColour`: #B4EB00
- `accentHover`: #A3D600 (accent/90)

### Styles
- `inputBase`: Complete input styling with focus states
- `cardBase`: Basic card styling

### Tailwind Classes
- `TAILWIND_CLASSES.accent.*`: All accent color utilities
- `TAILWIND_CLASSES.input.*`: Input variations (base, error, success)
- `TAILWIND_CLASSES.card.*`: Card variations (base, elevated, interactive)

## Theme Integration

These constants work seamlessly with the Tailwind config. You can now use:

```tsx
// These work thanks to the Tailwind config extension
<div className="bg-accent text-white">Accent background</div>
<div className="hover:bg-accent-90">Hover effect</div>
<input className="focus:ring-accent" />
```
