// Utilitário para gerenciar cache e detectar problemas de loading infinito

export const cacheUtils = {
  // Limpar completamente todos os dados relacionados à autenticação
  clearAllAuthData: (): void => {
    console.log('Limpando completamente dados de autenticação...');
    
    const authKeys = [
      'access_token',
      'refresh_token', 
      'user_data',
      'device_uuid'
    ];
    
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    // Também limpar sessionStorage se houver dados relacionados
    authKeys.forEach(key => {
      sessionStorage.removeItem(key);
    });
    
    console.log('Dados de autenticação limpos completamente.');
  },

  // Verificar se há dados corrompidos que podem causar loops
  detectCorruptedData: (): string[] => {
    const corrupted: string[] = [];
    
    const keysToCheck = [
      'access_token',
      'refresh_token',
      'user_data',
      'device_uuid'
    ];
    
    keysToCheck.forEach(key => {
      const value = localStorage.getItem(key);
      if (value && (
        value === 'undefined' || 
        value === 'null' || 
        value === '' ||
        value === '{}' ||
        (key === 'user_data' && !isValidJSON(value))
      )) {
        corrupted.push(key);
        console.warn(`Dados corrompidos detectados para ${key}:`, value);
      }
    });
    
    return corrupted;
  },

  // Limpar apenas dados corrompidos
  cleanCorruptedData: (): boolean => {
    const corrupted = cacheUtils.detectCorruptedData();
    
    if (corrupted.length > 0) {
      console.log('Removendo dados corrompidos:', corrupted);
      corrupted.forEach(key => localStorage.removeItem(key));
      return true;
    }
    
    return false;
  },

  // Verificar se o sistema está em loop de loading
  detectLoadingLoop: (): boolean => {
    const loopDetectorKey = 'loading_loop_detector';
    const maxLoops = 5;
    const timeWindow = 30000; // 30 segundos
    
    const now = Date.now();
    const stored = localStorage.getItem(loopDetectorKey);
    
    let loopData: { count: number; firstOccurrence: number } = { count: 0, firstOccurrence: now };
    
    if (stored) {
      try {
        loopData = JSON.parse(stored);
      } catch (e) {
        // Se não conseguir parsear, resetar
        loopData = { count: 0, firstOccurrence: now };
      }
    }
    
    // Se passou da janela de tempo, resetar contador
    if (now - loopData.firstOccurrence > timeWindow) {
      loopData = { count: 1, firstOccurrence: now };
    } else {
      loopData.count++;
    }
    
    localStorage.setItem(loopDetectorKey, JSON.stringify(loopData));
    
    // Se ultrapassou o limite, detectar como loop
    if (loopData.count > maxLoops) {
      console.error('Loop de loading detectado!');
      localStorage.removeItem(loopDetectorKey);
      return true;
    }
    
    return false;
  },

  // Reset do detector de loop (chamar quando carregamento for bem-sucedido)
  resetLoopDetector: (): void => {
    localStorage.removeItem('loading_loop_detector');
  },

  // Função de emergência para quando tudo mais falha
  emergencyReset: (): void => {
    console.warn('EMERGÊNCIA: Executando reset completo...');
    
    // Limpar tudo relacionado à autenticação
    cacheUtils.clearAllAuthData();
    
    // Limpar detector de loop
    cacheUtils.resetLoopDetector();
    
    // Limpar cache do service worker se existir
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          caches.delete(cacheName);
        });
      });
    }
    
    console.warn('Reset de emergência concluído. Recarregando página...');
    
    // Recarregar a página após limpeza
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  },

  // Versão do cache (para invalidação quando necessário)
  getCacheVersion: (): string => {
    return localStorage.getItem('cache_version') || '1.0.0';
  },

  setCacheVersion: (version: string): void => {
    localStorage.setItem('cache_version', version);
  },

  // Verificar se precisa invalidar cache por versão
  shouldInvalidateCache: (currentVersion: string): boolean => {
    const storedVersion = cacheUtils.getCacheVersion();
    if (storedVersion !== currentVersion) {
      console.log(`Cache invalidado: versão ${storedVersion} -> ${currentVersion}`);
      cacheUtils.setCacheVersion(currentVersion);
      return true;
    }
    return false;
  }
};

// Função auxiliar para validar JSON
function isValidJSON(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}
