/**
 * Utilitários para formatação e manipulação de valores monetários
 */
import React from 'react';

/**
 * Formata um valor numérico para o formato de moeda brasileira (BRL)
 * @param value - Valor numérico
 * @returns String formatada como moeda BRL
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

/**
 * Formata um valor para exibição no input (sem símbolo de moeda)
 * @param value - Valor numérico
 * @returns String formatada para input
 */
export const formatCurrencyInput = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

/**
 * Remove formatação de moeda e converte para número
 * @param value - String formatada como moeda
 * @returns Número decimal
 */
export const parseCurrency = (value: string): number => {
  // Remove todos os caracteres que não são dígitos, vírgula ou ponto
  const cleanValue = value.replace(/[^\d,.-]/g, '');

  // Se estiver vazio, retorna 0
  if (!cleanValue) return 0;

  // Lida com formato brasileiro: pontos são separadores de milhares, vírgula é decimal
  let normalizedValue = cleanValue;

  // Se há vírgula, ela é o separador decimal
  if (normalizedValue.includes(',')) {
    // Remove todos os pontos (separadores de milhares)
    normalizedValue = normalizedValue.replace(/\./g, '');
    // Substitui vírgula por ponto (separador decimal)
    normalizedValue = normalizedValue.replace(',', '.');
  }
  // Se não há vírgula mas há ponto, pode ser decimal (ex: "10.50") ou milhares (ex: "1.000")
  else if (normalizedValue.includes('.')) {
    // Se o ponto está nos últimos 3 caracteres, provavelmente é decimal
    const lastDotIndex = normalizedValue.lastIndexOf('.');
    const afterDot = normalizedValue.substring(lastDotIndex + 1);

    if (afterDot.length <= 2) {
      // Provavelmente é separador decimal, mantém apenas o último ponto
      const beforeLastDot = normalizedValue.substring(0, lastDotIndex);
      normalizedValue = beforeLastDot.replace(/\./g, '') + '.' + afterDot;
    } else {
      // Provavelmente são separadores de milhares, remove todos
      normalizedValue = normalizedValue.replace(/\./g, '');
    }
  }

  return parseFloat(normalizedValue) || 0;
};

/**
 * Converte valor numérico para string decimal para envio ao backend
 * @param value - Valor numérico
 * @returns String decimal formatada
 */
export const toDecimalString = (value: number): string => {
  return value.toFixed(2);
};

/**
 * Aplica máscara de moeda brasileira em tempo real
 * @param value - Valor atual do input
 * @returns Valor formatado com máscara
 */
export const applyCurrencyMask = (value: string): string => {
  // Remove tudo que não é dígito
  const digits = value.replace(/\D/g, '');
  
  // Se não há dígitos, retorna vazio
  if (!digits) return '';
  
  // Converte para número (centavos)
  const cents = parseInt(digits);
  
  // Converte centavos para reais
  const reais = cents / 100;
  
  // Formata como moeda brasileira sem o símbolo
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(reais);
};

/**
 * Hook personalizado para input de moeda
 */
export const useCurrencyInput = (initialValue: number = 0) => {
  const [displayValue, setDisplayValue] = React.useState(
    initialValue > 0 ? formatCurrencyInput(initialValue) : ''
  );
  const [numericValue, setNumericValue] = React.useState(initialValue);

  const handleChange = (inputValue: string) => {
    const formatted = applyCurrencyMask(inputValue);
    setDisplayValue(formatted);
    setNumericValue(parseCurrency(formatted));
  };

  const setValue = (value: number) => {
    setNumericValue(value);
    setDisplayValue(value > 0 ? formatCurrencyInput(value) : '');
  };

  return {
    displayValue,
    numericValue,
    handleChange,
    setValue,
    toDecimalString: () => toDecimalString(numericValue)
  };
};
