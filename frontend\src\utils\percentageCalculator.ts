/**
 * Utilitário para calcular percentuais comparativos com dados reais
 * Substitui valores hardcoded por cálculos baseados em dados reais
 */

export interface ComparisonData {
  current: number;
  previous: number;
  label: string;
}

export interface PercentageResult {
  percentage: number;
  trend: 'up' | 'down' | 'stable';
  difference: number;
  isSignificant: boolean;
}

/**
 * Calcula percentual comparativo entre dois valores
 * @param current - Valor atual
 * @param previous - Valor anterior (base de comparação)
 * @param threshold - Limite para considerar mudança significativa (padrão: 5%)
 * @returns Objeto com percentual, tendência e diferença
 */
export function calculatePercentageChange(
  current: number,
  previous: number,
  threshold: number = 5
): PercentageResult {
  // Evitar divisão por zero
  if (previous === 0) {
    return {
      percentage: current > 0 ? 100 : 0,
      trend: current > 0 ? 'up' : 'stable',
      difference: current,
      isSignificant: current !== 0
    };
  }

  const percentage = ((current - previous) / previous) * 100;
  const difference = current - previous;

  const trend = percentage > threshold ? 'up' : 
                percentage < -threshold ? 'down' : 'stable';

  return {
    percentage: Number(percentage.toFixed(1)),
    trend,
    difference,
    isSignificant: Math.abs(percentage) >= threshold
  };
}

/**
 * Calcula múltiplos percentuais comparativos
 * @param data - Array de dados para comparação
 * @param threshold - Limite para considerar mudança significativa
 * @returns Array com resultados de cada comparação
 */
export function calculateMultiplePercentages(
  data: ComparisonData[],
  threshold: number = 5
): (PercentageResult & { label: string })[] {
  return data.map(item => ({
    ...calculatePercentageChange(item.current, item.previous, threshold),
    label: item.label
  }));
}

/**
 * Calcula percentual de progresso em relação a uma meta
 * @param current - Valor atual
 * @param target - Meta/objetivo
 * @returns Percentual de progresso (0-100)
 */
export function calculateProgressPercentage(
  current: number,
  target: number
): number {
  if (target === 0) return 0;
  
  const progress = (current / target) * 100;
  return Math.min(Math.max(progress, 0), 100); // Limitar entre 0 e 100
}

/**
 * Calcula percentual de distribuição de categorias
 * @param values - Array de valores
 * @returns Array com percentuais de cada categoria
 */
export function calculateDistributionPercentages(
  values: number[]
): number[] {
  const total = values.reduce((sum, value) => sum + value, 0);
  
  if (total === 0) return values.map(() => 0);
  
  return values.map(value => Number(((value / total) * 100).toFixed(1)));
}

/**
 * Calcula taxa de crescimento média
 * @param values - Array de valores em ordem cronológica
 * @returns Taxa de crescimento média como percentual
 */
export function calculateAverageGrowthRate(values: number[]): number {
  if (values.length < 2) return 0;
  
  const growthRates = [];
  
  for (let i = 1; i < values.length; i++) {
    const current = values[i];
    const previous = values[i - 1];
    
    if (previous !== 0) {
      const growthRate = ((current - previous) / previous) * 100;
      growthRates.push(growthRate);
    }
  }
  
  if (growthRates.length === 0) return 0;
  
  const averageGrowth = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
  return Number(averageGrowth.toFixed(1));
}

/**
 * Formata percentual para exibição
 * @param percentage - Valor percentual
 * @param showSign - Se deve mostrar sinal + para valores positivos
 * @returns String formatada
 */
export function formatPercentage(percentage: number, showSign: boolean = true): string {
  const sign = showSign && percentage > 0 ? '+' : '';
  return `${sign}${percentage.toFixed(1)}%`;
}

/**
 * Obtém cor baseada na tendência
 * @param trend - Tendência (up, down, stable)
 * @param inverted - Se as cores devem ser invertidas (up=red, down=green)
 * @returns Objeto com classes CSS para cor
 */
export function getTrendColor(trend: 'up' | 'down' | 'stable', inverted: boolean = false) {
  const colors = {
    up: inverted ? 'text-red-600' : 'text-green-600',
    down: inverted ? 'text-green-600' : 'text-red-600',
    stable: 'text-gray-600'
  };
  
  const bgColors = {
    up: inverted ? 'bg-red-50' : 'bg-green-50',
    down: inverted ? 'bg-green-50' : 'bg-red-50',
    stable: 'bg-gray-50'
  };
  
  return {
    text: colors[trend],
    bg: bgColors[trend]
  };
}

/**
 * Exemplo de uso para dados financeiros
 */
export function calculateFinancialComparisons(data: {
  currentMonth: { income: number; expenses: number; savings: number };
  previousMonth: { income: number; expenses: number; savings: number };
  currentYear: { income: number; expenses: number; savings: number };
  previousYear: { income: number; expenses: number; savings: number };
}) {
  return {
    monthly: {
      income: calculatePercentageChange(data.currentMonth.income, data.previousMonth.income),
      expenses: calculatePercentageChange(data.currentMonth.expenses, data.previousMonth.expenses),
      savings: calculatePercentageChange(data.currentMonth.savings, data.previousMonth.savings)
    },
    yearly: {
      income: calculatePercentageChange(data.currentYear.income, data.previousYear.income),
      expenses: calculatePercentageChange(data.currentYear.expenses, data.previousYear.expenses),
      savings: calculatePercentageChange(data.currentYear.savings, data.previousYear.savings)
    }
  };
}

/**
 * Exemplo de uso para dados de tarefas
 */
export function calculateTaskComparisons(data: {
  currentWeek: { completed: number; total: number };
  previousWeek: { completed: number; total: number };
  currentMonth: { completed: number; total: number };
  previousMonth: { completed: number; total: number };
}) {
  const currentWeekRate = data.currentWeek.total > 0 ? 
    (data.currentWeek.completed / data.currentWeek.total) * 100 : 0;
  const previousWeekRate = data.previousWeek.total > 0 ? 
    (data.previousWeek.completed / data.previousWeek.total) * 100 : 0;
  
  const currentMonthRate = data.currentMonth.total > 0 ? 
    (data.currentMonth.completed / data.currentMonth.total) * 100 : 0;
  const previousMonthRate = data.previousMonth.total > 0 ? 
    (data.previousMonth.completed / data.previousMonth.total) * 100 : 0;
  
  return {
    weekly: {
      completionRate: calculatePercentageChange(currentWeekRate, previousWeekRate),
      totalTasks: calculatePercentageChange(data.currentWeek.total, data.previousWeek.total)
    },
    monthly: {
      completionRate: calculatePercentageChange(currentMonthRate, previousMonthRate),
      totalTasks: calculatePercentageChange(data.currentMonth.total, data.previousMonth.total)
    }
  };
}
