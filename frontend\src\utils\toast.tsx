import React from 'react';
import { toast as reactToast } from 'react-toastify';
import { Check, X, AlertCircle, Info } from 'lucide-react';

interface ToastOptions {
  autoClose?: number;
  position?: 'top-right' | 'top-left' | 'top-center' | 'bottom-right' | 'bottom-left' | 'bottom-center';
}

const createToastContent = (message: string, icon: React.ReactNode, bgColor: string, textColor: string) => (
  <div className="flex items-center gap-3">
    <div className={`w-6 h-6 rounded-full ${bgColor} flex items-center justify-center`}>
      {icon}
    </div>
    <span className={`text-sm font-medium ${textColor}`}>{message}</span>
  </div>
);

export const toast = {
  success: (message: string, options?: ToastOptions) => {
    reactToast.success(
      createToastContent(
        message,
        <Check size={14} className="text-white" />,
        'bg-green-500',
        'text-gray-900'
      ),
      {
        autoClose: 4000,
        ...options,
      }
    );
  },

  error: (message: string, options?: ToastOptions) => {
    reactToast.error(
      createToastContent(
        message,
        <X size={14} className="text-white" />,
        'bg-red-500',
        'text-gray-900'
      ),
      {
        autoClose: 5000,
        ...options,
      }
    );
  },

  warning: (message: string, options?: ToastOptions) => {
    reactToast.warning(
      createToastContent(
        message,
        <AlertCircle size={14} className="text-white" />,
        'bg-yellow-500',
        'text-gray-900'
      ),
      {
        autoClose: 4000,
        ...options,
      }
    );
  },

  info: (message: string, options?: ToastOptions) => {
    reactToast.info(
      createToastContent(
        message,
        <Info size={14} className="text-white" />,
        'bg-blue-500',
        'text-gray-900'
      ),
      {
        autoClose: 4000,
        ...options,
      }
    );
  },
};
