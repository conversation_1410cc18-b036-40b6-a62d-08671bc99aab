/*
  # Ideas Management System Schema

  1. New Tables
    - `ideas`
      - Core table for storing user ideas
      - Supports favorites, archiving, and task conversion
      - Includes audit fields for tracking changes
    
    - `idea_tags`
      - Lookup table for categorizing ideas
      - Enables flexible organization and filtering
    
    - `idea_tag_assignments`
      - Junction table for many-to-many relationship between ideas and tags
    
    - `idea_tasks`
      - Tracks ideas converted to tasks
      - Links ideas with the tasks system
    
  2. Security
    - RLS policies for each table
    - Users can only access their own data
    - Audit trail for all changes
    
  3. Indexes
    - Optimized for common queries and filters
    - Full-text search support
*/

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Ideas table
CREATE TABLE IF NOT EXISTS ideas (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  is_favorite boolean DEFAULT false,
  is_archived boolean DEFAULT false,
  source text DEFAULT 'web',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  archived_at timestamptz,
  metadata jsonb DEFAULT '{}'::jsonb,
  search_vector tsvector GENERATED ALWAYS AS (
    setweight(to_tsvector('portuguese', coalesce(title, '')), 'A') ||
    setweight(to_tsvector('portuguese', coalesce(description, '')), 'B')
  ) STORED
);

-- Tags lookup table
CREATE TABLE IF NOT EXISTS idea_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  color text DEFAULT '#B4EB00',
  created_at timestamptz DEFAULT now()
);

-- Ideas-Tags junction table
CREATE TABLE IF NOT EXISTS idea_tag_assignments (
  idea_id uuid REFERENCES ideas(id) ON DELETE CASCADE,
  tag_id uuid REFERENCES idea_tags(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (idea_id, tag_id)
);

-- Ideas-Tasks relationship table
CREATE TABLE IF NOT EXISTS idea_tasks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  idea_id uuid NOT NULL REFERENCES ideas(id) ON DELETE CASCADE,
  task_id uuid NOT NULL,
  converted_at timestamptz DEFAULT now(),
  converted_by uuid REFERENCES auth.users(id)
);

-- Indexes
CREATE INDEX IF NOT EXISTS ideas_user_id_idx ON ideas(user_id);
CREATE INDEX IF NOT EXISTS ideas_created_at_idx ON ideas(created_at);
CREATE INDEX IF NOT EXISTS ideas_search_vector_idx ON ideas USING gin(search_vector);
CREATE INDEX IF NOT EXISTS ideas_favorite_idx ON ideas(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX IF NOT EXISTS ideas_archived_idx ON ideas(user_id, is_archived) WHERE is_archived = true;

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER ideas_updated_at
  BEFORE UPDATE ON ideas
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Enable RLS
ALTER TABLE ideas ENABLE ROW LEVEL SECURITY;
ALTER TABLE idea_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE idea_tag_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE idea_tasks ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own ideas"
  ON ideas
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own tags"
  ON idea_tags
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own tag assignments"
  ON idea_tag_assignments
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT user_id FROM ideas WHERE id = idea_tag_assignments.idea_id
    )
  );

CREATE POLICY "Users can manage their own idea-task relationships"
  ON idea_tasks
  FOR ALL
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT user_id FROM ideas WHERE id = idea_tasks.idea_id
    )
  );