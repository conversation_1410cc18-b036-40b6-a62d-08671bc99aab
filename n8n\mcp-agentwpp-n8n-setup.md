# Configuração MCP AgentWPP para N8N

## Visão Geral

Este documento contém todas as instruções para configurar os endpoints do **MCP AgentWPP** no **N8N** através de requisições HTTP diretas, substituindo o uso do MCP Server externo.

## Configuração Base

- **Base URL**: `https://dupli-api.hitvox.com`
- **Autenticação**: Header `x-api-key` com sua chave de API
- **Content-Type**: `application/json`

### Headers Padrão

```json
{
  "Content-Type": "application/json",
  "x-api-key": "SUA_CHAVE_API_AQUI"
}
```

---

## 🔗 ENDPOINTS DE INTEGRAÇÃO

### 1. Verificar Integração WhatsApp

- **Tool Name**: `integration_check_whatsapp`
- **Descrição**: Verifica se um número de telefone possui integração WhatsApp ativa
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/check-integration/{phone}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone (ex: +5511999999999)

**Exemplo de Resposta**:
```json
{
  "hasIntegration": true,
  "status": "active",
  "userId": 123
}
```

### 2. Validar Número de Telefone

- **Tool Name**: `integration_validate_phone`
- **Descrição**: Valida um número de telefone com código de ativação
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/validate`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário (ex: +5511999999999)', 'string')",
  "activation_code": "$fromAI('activation_code', 'Código de ativação recebido via WhatsApp', 'string')"
}
```

### 3. Buscar Dashboard

- **Tool Name**: `dashboard_get_summary`
- **Descrição**: Obtém dados resumidos do dashboard do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/dashboard/{phone}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone

**Exemplo de Resposta**:
```json
{
  "user": {
    "name": "Nome do Usuário",
    "timezone": "America/Sao_Paulo"
  },
  "tasks": {
    "completed": 5,
    "total": 10
  },
  "finances": {
    "spent": 1500.00,
    "budget": 3000.00,
    "income": 5000.00,
    "savings": 500.00
  },
  "ideas": {
    "today": 3,
    "total": 25,
    "favorites": 8
  }
}
```

---

## 📅 ENDPOINTS DE TAREFAS E AGENDAMENTOS

### 4. Criar Tarefa ou Agendamento

- **Tool Name**: `tasks_create`
- **Descrição**: Cria uma nova tarefa ou agendamento
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "task_type": "$fromAI('task_type', 'Tipo da tarefa: appointment ou task', 'string')",
  "name": "$fromAI('name', 'Nome da tarefa', 'string')",
  "description": "$fromAI('description', 'Descrição detalhada da tarefa', 'string')",
  "task_date": "$fromAI('task_date', 'Data e hora da tarefa (ISO 8601)', 'string')",
  "category_id": "$fromAI('category_id', 'ID da categoria da tarefa', 'number')"
}
```

### 5. Criar Agendamento

- **Tool Name**: `tasks_create_appointment`
- **Descrição**: Cria um agendamento com dados mínimos, enviando `task_type` como `appointment`
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Nome do agendamento', 'string')",
  "description": "$fromAI('description', 'Descrição do agendamento', 'string')",
  "task_type": "appointment",
  "task_date": "$fromAI('task_date', 'Data e hora do agendamento (ISO 8601)', 'string')"
}
```

### 6. Buscar Tarefas

- **Tool Name**: `tasks_list`
- **Descrição**: Lista tarefas do usuário com paginação
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{phone}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `page`: Página (opcional, padrão: 1)
  - `limit`: Limite por página (opcional, padrão: 50)

### 7. Buscar Tarefa por ID

- **Tool Name**: `tasks_get_by_id`
- **Descrição**: Obtém uma tarefa específica
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{phone}/{id}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da tarefa

### 8. Atualizar Tarefa

- **Tool Name**: `tasks_update`
- **Descrição**: Atualiza uma tarefa existente
- **Método**: `PATCH`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{id}`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "task_type": "$fromAI('task_type', 'Tipo da tarefa: appointment ou task', 'string')",
  "name": "$fromAI('name', 'Nome da tarefa', 'string')",
  "description": "$fromAI('description', 'Descrição da tarefa', 'string')",
  "task_date": "$fromAI('task_date', 'Data e hora da tarefa (ISO 8601)', 'string')",
  "category_id": "$fromAI('category_id', 'ID da categoria', 'number')"
}
```

### 9. Excluir Tarefa

- **Tool Name**: `tasks_delete`
- **Descrição**: Remove uma tarefa
- **Método**: `DELETE`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{phone}/{id}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da tarefa

### 10. Completar Tarefa

- **Tool Name**: `tasks_complete`
- **Descrição**: Marca uma tarefa como concluída
- **Método**: `PATCH`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{phone}/{id}/complete`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da tarefa

### 11. Buscar Categorias de Tarefas

- **Tool Name**: `tasks_categories_list`
- **Descrição**: Lista categorias de tarefas do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{phone}/categories`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone

### 12. Criar Categoria de Tarefa

- **Tool Name**: `tasks_categories_create`
- **Descrição**: Cria uma nova categoria de tarefa
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/categories`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Nome da categoria de tarefa', 'string')"
}
```

### 13. Criar Tarefa Rápida

- **Tool Name**: `tasks_create_quick`
- **Descrição**: Cria uma tarefa simples sem data específica (tipo 'task')
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/quick`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Nome da tarefa rápida', 'string')",
  "description": "$fromAI('description', 'Descrição opcional da tarefa', 'string')"
}
```

### 14. Buscar Tarefas Recentes

- **Tool Name**: `tasks_recent`
- **Descrição**: Lista tarefas recentes do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/tasks/{phone}/recent`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `limit`: Limite de itens (opcional, máx: 50)
  - `days`: Dias atrás (opcional, máx: 365)

---

## 💰 ENDPOINTS DE FINANÇAS

### 15. Criar Transação Financeira

- **Tool Name**: `finances_add_transaction`
- **Descrição**: Registra uma nova transação financeira
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "transaction_type": "$fromAI('transaction_type', 'Tipo de transação: income ou expense', 'string')",
  "amount": "$fromAI('amount', 'Valor da transação (ex: 150.00)', 'string')",
  "transaction_date": "$fromAI('transaction_date', 'Data da transação (ISO 8601)', 'string')",
  "description": "$fromAI('description', 'Descrição da transação', 'string')",
  "category_id": "$fromAI('category_id', 'ID da categoria financeira', 'number')",
  "is_saving": "$fromAI('is_saving', 'Se é um depósito na poupança', 'boolean', false)"
}
```

### 16. Buscar Transações Financeiras

- **Tool Name**: `finances_list_transactions`
- **Descrição**: Lista transações financeiras com resumo
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{phone}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `page`: Página (opcional)
  - `limit`: Limite por página (opcional)

### 17. Buscar Transação por ID

- **Tool Name**: `finances_get_transaction`
- **Descrição**: Obtém uma transação específica
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{phone}/{id}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da transação

### 18. Atualizar Transação Financeira

- **Tool Name**: `finances_update_transaction`
- **Descrição**: Atualiza uma transação existente
- **Método**: `PATCH`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{id}`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "transaction_type": "$fromAI('transaction_type', 'Tipo de transação: income ou expense', 'string')",
  "amount": "$fromAI('amount', 'Valor da transação (ex: 150.00)', 'string')",
  "transaction_date": "$fromAI('transaction_date', 'Data da transação (ISO 8601)', 'string')",
  "description": "$fromAI('description', 'Descrição da transação', 'string')",
  "category_id": "$fromAI('category_id', 'ID da categoria', 'number')",
  "is_saving": "$fromAI('is_saving', 'Se é poupança', 'boolean', false)"
}
```

### 19. Excluir Transação Financeira

- **Tool Name**: `finances_delete_transaction`
- **Descrição**: Remove uma transação financeira
- **Método**: `DELETE`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{phone}/{id}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da transação

### 20. Buscar Resumo Financeiro

- **Tool Name**: `finances_get_summary`
- **Descrição**: Obtém resumo financeiro do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{phone}/summary`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `startDate`: Data início (opcional, YYYY-MM-DD)
  - `endDate`: Data fim (opcional, YYYY-MM-DD)

### 21. Buscar Categorias Financeiras

- **Tool Name**: `finances_categories_list`
- **Descrição**: Lista categorias financeiras do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{phone}/categories`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone

### 22. Criar Categoria Financeira

- **Tool Name**: `finances_categories_create`
- **Descrição**: Cria uma nova categoria financeira
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/categories`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Nome da categoria financeira', 'string')",
  "transaction_type": "$fromAI('transaction_type', 'Tipo de transação: income ou expense', 'string')",
  "color": "$fromAI('color', 'Cor da categoria (hex, ex: #FF5733)', 'string')"
}
```

### 23. Buscar Transações Recentes

- **Tool Name**: `finances_recent_transactions`
- **Descrição**: Lista transações financeiras recentes
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/finances/{phone}/recent`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `limit`: Limite de itens (opcional)
  - `days`: Dias atrás (opcional)

---

## 💡 ENDPOINTS DE IDEIAS

### 24. Criar Ideia

- **Tool Name**: `ideas_create`
- **Descrição**: Registra uma nova ideia
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Título da ideia', 'string')",
  "description": "$fromAI('description', 'Descrição resumida da ideia', 'string')",
  "content": "$fromAI('content', 'Conteúdo detalhado da ideia', 'string')",
  "category_id": "$fromAI('category_id', 'ID da categoria da ideia', 'number')",
  "is_favorite": "$fromAI('is_favorite', 'Se é uma ideia favorita', 'boolean', false)"
}
```

### 25. Buscar Ideias

- **Tool Name**: `ideas_list`
- **Descrição**: Lista ideias do usuário com paginação
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{phone}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `page`: Página (opcional)
  - `limit`: Limite por página (opcional)

### 26. Buscar Ideia por ID

- **Tool Name**: `ideas_get_by_id`
- **Descrição**: Obtém uma ideia específica
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{phone}/{id}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da ideia

### 27. Atualizar Ideia

- **Tool Name**: `ideas_update`
- **Descrição**: Atualiza uma ideia existente
- **Método**: `PATCH`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{id}`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Título da ideia', 'string')",
  "description": "$fromAI('description', 'Descrição da ideia', 'string')",
  "content": "$fromAI('content', 'Conteúdo detalhado da ideia', 'string')",
  "category_id": "$fromAI('category_id', 'ID da categoria', 'number')",
  "is_favorite": "$fromAI('is_favorite', 'Se é favorita', 'boolean', false)"
}
```

### 28. Excluir Ideia

- **Tool Name**: `ideas_delete`
- **Descrição**: Remove uma ideia
- **Método**: `DELETE`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{phone}/{id}`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da ideia

### 29. Favoritar/Desfavoritar Ideia

- **Tool Name**: `ideas_toggle_favorite`
- **Descrição**: Alterna o status de favorito de uma ideia
- **Método**: `PATCH`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{phone}/{id}/favorite`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
  - `id`: ID da ideia

### 30. Buscar Categorias de Ideias

- **Tool Name**: `ideas_categories_list`
- **Descrição**: Lista categorias de ideias do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{phone}/categories`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone

### 31. Criar Categoria de Ideia

- **Tool Name**: `ideas_categories_create`
- **Descrição**: Cria uma nova categoria de ideia
- **Método**: `POST`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/categories`

**Body JSON para N8N**:
```json
{
  "phone": "$fromAI('phone', 'Número de telefone do usuário', 'string')",
  "name": "$fromAI('name', 'Nome da categoria de ideia', 'string')"
}
```

### 32. Buscar Ideias Recentes

- **Tool Name**: `ideas_recent`
- **Descrição**: Lista ideias recentes do usuário
- **Método**: `GET`
- **URL**: `https://dupli-api.hitvox.com/agentwpp/ideas/{phone}/recent`
- **Parâmetros de URL**: 
  - `phone`: Número de telefone
- **Query Parameters**:
  - `limit`: Limite de itens (opcional)
  - `days`: Dias atrás (opcional)

---

## 📋 NOTAS IMPORTANTES

### Validação de Dados

1. **Telefone**: Deve ser normalizado no formato `+5511999999999` ou `11999999999`
2. **Valores monetários**: Devem estar em formato string decimal (ex: "150.00")
3. **Datas**: Usar formato ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`) ou `YYYY-MM-DD`
4. **IDs**: Sempre números inteiros positivos

### Tratamento de Erros

- **400**: Dados inválidos fornecidos
- **401**: Chave de API inválida
- **403**: Acesso negado - usuário sem integração WhatsApp
- **404**: Recurso não encontrado
- **429**: Muitas requisições
- **500**: Erro interno do servidor

### Exemplos de Configuração no N8N

#### Configuração de Nó HTTP Request:

1. **Method**: Conforme especificado em cada endpoint
2. **URL**: URL completa do endpoint
3. **Headers**: 
   ```json
   {
     "Content-Type": "application/json",
     "x-api-key": "SUA_CHAVE_API_AQUI"
   }
   ```
4. **Body**: Copie o "Body JSON para N8N" do endpoint desejado
5. **Parameters**: Query params quando aplicável

#### Como usar `$fromAI()`:

A sintaxe `$fromAI(key, description, type, defaultValue)` permite que a IA passe parâmetros automaticamente:

- **key**: Nome do parâmetro (obrigatório)
- **description**: Descrição do parâmetro (opcional)
- **type**: Tipo de dado - `string`, `number`, `boolean`, `json` (opcional)
- **defaultValue**: Valor padrão (opcional)

**Exemplos**:
```javascript
$fromAI('phone')                                    // Parâmetro simples
$fromAI('amount', 'Valor da transação', 'string')   // Com descrição e tipo
$fromAI('is_saving', 'Se é poupança', 'boolean', false) // Com valor padrão
```

### Segurança

- Sempre use HTTPS
- Mantenha a chave API segura
- Valide dados de entrada
- Implemente rate limiting se necessário

---

**Data de Criação**: 16/08/2025  
**Versão da API**: 1.0.0  
**Baseado em**: MCP Server AgentWPP
